# 🔍 MongoDB Permission Debug Guide

## 🎯 Current Issue
You're still getting the listDatabases error, which means the `generalWeb` user doesn't have the required permissions yet.

## 🔧 Step-by-Step Fix

### **Step 1: Connect as Admin User**

```bash
# Connect to MongoDB with admin privileges
mongo --host localhost:27017 -u admin -p --authenticationDatabase admin
```

### **Step 2: Check Current generalWeb User Permissions**

```javascript
use admin

// Check what permissions generalWeb currently has
db.getUser("generalWeb")

// Check if user can run listDatabases
try {
  var result = db.runCommand({listDatabases: 1});
  print("✅ listDatabases works: " + result.databases.length + " databases found");
} catch(e) {
  print("❌ listDatabases failed: " + e.message);
}
```

### **Step 3: Grant Required Permissions**

```javascript
use admin

// Grant listDatabases permission
db.grantRolesToUser("generalWeb", [
  { role: "listDatabases", db: "admin" }
])

// Grant readWrite access to tenant databases
db.grantRolesToUser("generalWeb", [
  { role: "readWrite", db: "generalWebDemo" },
  { role: "readWrite", db: "generalWebNewcitymobile" },
  { role: "readWrite", db: "generalWebWanigarathna" }
])

print("✅ Permissions granted successfully");
```

### **Step 4: Verify Permissions Work**

```javascript
// Test as generalWeb user
// First, connect as generalWeb user:
// mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb

use admin

// Test listDatabases
var dbs = db.runCommand({listDatabases: 1});
print("Total databases: " + dbs.databases.length);

// Filter for tenant databases
var tenantDbs = dbs.databases.filter(db => db.name.startsWith('generalWeb'));
print("Tenant databases found: " + tenantDbs.length);
tenantDbs.forEach(db => print("  - " + db.name));
```

## 🚨 Alternative: Grant Broad Admin Access

If the above doesn't work, try granting broader permissions:

```javascript
use admin

// Grant comprehensive admin permissions
db.grantRolesToUser("generalWeb", [
  { role: "readWriteAnyDatabase", db: "admin" },
  { role: "listDatabases", db: "admin" }
])
```

## 🔍 Debug Commands

### **Check User Roles in Detail**

```javascript
use admin

// Get detailed user information
var userInfo = db.runCommand({usersInfo: "generalWeb", showPrivileges: true});
printjson(userInfo);
```

### **Test Connection as generalWeb User**

```bash
# Test connecting as generalWeb user
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb --eval "db.runCommand({listDatabases: 1})"
```

### **Check Authentication Database**

The issue might be the authentication database. Try these variations:

```bash
# Try with admin as auth database
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase admin --eval "db.runCommand({listDatabases: 1})"

# Try with generalWeb as auth database (current config)
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb --eval "db.runCommand({listDatabases: 1})"
```

## 🎯 Most Likely Solution

Run these commands as admin user:

```javascript
use admin

// Grant the user admin role on the admin database
db.grantRolesToUser("generalWeb", [
  { role: "userAdminAnyDatabase", db: "admin" },
  { role: "readWriteAnyDatabase", db: "admin" },
  { role: "dbAdminAnyDatabase", db: "admin" }
])
```

## ✅ Verification

After running the permission grants, test your backup service again. The error should be resolved and database discovery should work.

## 📋 Quick Test Script

```javascript
// Run this after granting permissions
use admin

print("=== Testing generalWeb User Permissions ===");

// Test 1: List databases
try {
  var dbs = db.runCommand({listDatabases: 1});
  print("✅ listDatabases: " + dbs.databases.length + " databases");
  
  var tenantDbs = dbs.databases.filter(db => db.name.startsWith('generalWeb'));
  print("✅ Tenant databases: " + tenantDbs.length);
  tenantDbs.forEach(db => print("   - " + db.name));
} catch(e) {
  print("❌ listDatabases failed: " + e.message);
}

// Test 2: Access tenant databases
["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"].forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    print(dbName + ": " + (result.ok ? "✅ ACCESSIBLE" : "❌ FAILED"));
  } catch(e) {
    print(dbName + ": ❌ ERROR - " + e.message);
  }
});

print("=== Test Complete ===");
```
