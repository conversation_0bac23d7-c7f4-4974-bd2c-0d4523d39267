# Silent Printing in ERP Web General

This document explains how to use the silent printing feature in the ERP Web General application.

## Overview

Silent printing allows you to print invoices directly without showing the Chrome print dialog. This is useful for POS terminals where you want to streamline the printing process.

## Requirements

1. Google Chrome browser
2. Chrome must be launched with the `--kiosk-printing` flag

## Setup Instructions

### 1. Enable Silent Printing in the Application

In the environment configuration files, set the `useSilentPrint` flag to `true`:

```typescript
// In environment.ts and/or environment.prod.ts
export const environment = {
  // other settings...
  useSilentPrint: true
};
```

### 2. Launch Chrome with the Kiosk Printing Flag

#### Using the Provided Batch File

1. Edit the `launch-chrome-silent-print.bat` file to ensure the Chrome path is correct for your system
2. Double-click the batch file to launch Chrome with silent printing enabled

#### Manual Setup (Windows)

1. Create a shortcut to Chrome
2. Right-click the shortcut and select "Properties"
3. In the "Target" field, add `--kiosk-printing` after the Chrome executable path
   
   Example:
   ```
   "C:\Program Files\Google\Chrome\Application\chrome.exe" --kiosk-printing
   ```
4. Click "Apply" and "OK"
5. Use this shortcut to launch Chrome when you want to use silent printing

#### Manual Setup (macOS)

1. Open Terminal
2. Run the following command:
   ```
   /Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --kiosk-printing
   ```
3. You can create an Automator script or shell script to make this easier to launch

## How It Works

When the `useSilentPrint` flag is enabled:

1. When you click "Save & Print" in the application, it will:
   - Save the invoice to the database
   - Generate an HTML representation of the invoice
   - Open a new window with the invoice content
   - Automatically print the content without showing the print dialog
   - Close the window after printing

## Troubleshooting

### Empty Pages When Printing

If you're getting empty pages when printing:

1. Make sure Chrome is launched with the `--kiosk-printing` flag
2. Check the browser console for any JavaScript errors
3. Try using the "Silent Print" button on the invoice page to test printing directly

### Print Dialog Still Shows

If the print dialog still appears:

1. Verify that Chrome was launched with the `--kiosk-printing` flag
2. Check that `useSilentPrint` is set to `true` in the environment configuration
3. Make sure you're using Chrome and not another browser

### Other Issues

- **Pop-up Blocked**: If you see a message about pop-ups being blocked, allow pop-ups for your application URL
- **Styling Issues**: If the printed output doesn't look right, you may need to adjust the CSS in the `generateInvoiceHtmlAndPrint` method

## Security Considerations

Silent printing bypasses user confirmation, which could potentially be misused. Only enable this feature in controlled environments like POS terminals.

## Support

For additional help, contact your system administrator or the development team.
