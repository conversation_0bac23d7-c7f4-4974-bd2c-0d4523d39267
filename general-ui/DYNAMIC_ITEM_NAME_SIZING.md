# 📏 Dynamic Item Name Sizing

## 🎯 **Feature Overview**

The barcode component now automatically adjusts the item name font size based on the length of the item name. This ensures optimal space utilization and readability across different item name lengths.

## 🔍 **How It Works**

### **Length-Based Sizing Logic**
```typescript
if (nameLength <= 10) {
  return sizes.short;  // Short names get larger text
} else if (nameLength <= 20) {
  return sizes.medium; // Medium names get medium text
} else {
  return sizes.long;   // Long names get smaller text
}
```

### **Paper Size Considerations**
Different paper sizes have different base font sizes to maintain readability:

```typescript
const baseSizes = {
  '30x20': { short: '0.7em', medium: '0.6em', long: '0.5em' },
  '38x25': { short: '0.8em', medium: '0.7em', long: '0.6em' },
  '100x50': { short: '1.0em', medium: '0.9em', long: '0.8em' }
};
```

## 📊 **Sizing Examples**

### **30x20mm Stickers (Small)**
- **Short names (≤10 chars)**: `"Phone"` → `0.7em` (larger text)
- **Medium names (11-20 chars)**: `"Samsung Galaxy S21"` → `0.6em` (medium text)
- **Long names (>20 chars)**: `"Apple iPhone 14 Pro Max 256GB"` → `0.5em` (smaller text)

### **38x25mm Stickers (Medium)**
- **Short names**: `"Laptop"` → `0.8em`
- **Medium names**: `"Dell Inspiron 15"` → `0.7em`
- **Long names**: `"Dell Inspiron 15 3000 Series Laptop"` → `0.6em`

### **100x50mm Stickers (Large)**
- **Short names**: `"Monitor"` → `1.0em`
- **Medium names**: `"LG 24 inch Monitor"` → `0.9em`
- **Long names**: `"LG 24 inch Full HD IPS Monitor with USB-C"` → `0.8em`

## 🎨 **Visual Examples**

### **Before (Fixed Sizing)**
```
┌─────────────────────┐
│ [Barcode]           │
│ Phone               │ ← Same size
│ Samsung Galaxy S21  │ ← Same size (cramped)
│ Apple iPhone 14 ... │ ← Same size (truncated)
│ Rs. 150.00          │
└─────────────────────┘
```

### **After (Dynamic Sizing)**
```
┌─────────────────────┐
│ [Barcode]           │
│ Phone               │ ← Larger text
│ Samsung Galaxy S21  │ ← Medium text
│ Apple iPhone 14 Pro │ ← Smaller text (fits better)
│ Rs. 150.00          │
└─────────────────────┘
```

## 🔧 **Implementation Details**

### **Component Method**
```typescript
getItemNameFontSize(): string {
  const nameLength = (this.itemName || '').length;
  const paperSize = this.paperSize;
  
  const baseSizes = {
    '30x20': { short: '0.7em', medium: '0.6em', long: '0.5em' },
    // ... other sizes
  };
  
  const sizes = baseSizes[paperSize] || baseSizes['30x20'];
  
  if (nameLength <= 10) return sizes.short;
  else if (nameLength <= 20) return sizes.medium;
  else return sizes.long;
}
```

### **Template Usage**
```html
<div class="item-name" [style.font-size]="getItemNameFontSize()">
  {{ itemName || 'No Name' }}
</div>
```

### **Print Service Implementation**
```typescript
private getDynamicNameFontSize(itemName: string, paperSize: string): number {
  const nameLength = (itemName || '').length;
  
  const baseSizes = {
    '30x20': { short: 5, medium: 4, long: 3 },
    // ... other sizes in points
  };
  
  // Returns font size in points for print
}
```

## 📋 **Configuration**

### **Length Thresholds**
- **Short names**: ≤ 10 characters
- **Medium names**: 11-20 characters  
- **Long names**: > 20 characters

### **Font Size Ranges**

| Paper Size | Short Names | Medium Names | Long Names |
|------------|-------------|--------------|------------|
| 30x20mm    | 0.7em (5pt) | 0.6em (4pt)  | 0.5em (3pt) |
| 33x21mm    | 0.75em (6pt)| 0.65em (5pt) | 0.55em (4pt)|
| 38x25mm    | 0.8em (7pt) | 0.7em (6pt)  | 0.6em (5pt) |
| 50x25mm    | 0.85em (8pt)| 0.75em (7pt) | 0.65em (6pt)|
| 65x15mm    | 0.6em (4pt) | 0.5em (3pt)  | 0.45em (2.5pt)|
| 100x50mm   | 1.0em (10pt)| 0.9em (9pt)  | 0.8em (8pt) |
| 100x150mm  | 1.2em (12pt)| 1.0em (11pt) | 0.9em (10pt)|

## 🧪 **Testing Scenarios**

### **Test Cases**
1. **Short Product Names**:
   - `"Phone"`, `"Laptop"`, `"Mouse"` → Larger, readable text
   
2. **Medium Product Names**:
   - `"Samsung Galaxy S21"`, `"Dell Inspiron 15"` → Balanced sizing
   
3. **Long Product Names**:
   - `"Apple iPhone 14 Pro Max 256GB Space Black"` → Smaller text, fits better

### **Edge Cases**
- **Empty name**: Falls back to `"No Name"` (short category)
- **Very long names**: Minimum font size maintained for readability
- **Special characters**: Counted in length calculation

## ✅ **Benefits**

1. **📏 Optimal Space Usage**: Long names don't waste space with oversized text
2. **👁️ Better Readability**: Short names get larger, more prominent text
3. **🎯 Consistent Layout**: All names fit properly within sticker bounds
4. **📱 Responsive**: Works across all paper sizes
5. **🔄 Automatic**: No manual configuration needed

## 🔄 **Backward Compatibility**

- **✅ Existing items**: All existing item names work with new sizing
- **✅ Settings preserved**: All barcode settings continue to work
- **✅ Paper sizes**: All configured paper sizes supported
- **✅ Print quality**: Maintains professional appearance

## 🚀 **Future Enhancements**

1. **Custom Thresholds**: Allow configuration of length thresholds
2. **Font Weight Adjustment**: Vary font weight based on size
3. **Multi-line Support**: Break very long names into multiple lines
4. **Language Support**: Different sizing for different character sets

## 📁 **Files Modified**

1. **`barcode.component.ts`**: Added `getItemNameFontSize()` method
2. **`barcode.component.html`**: Added dynamic font-size binding
3. **`barcode.component.css`**: Removed fixed font-size for item-name
4. **`barcode-settings.service.ts`**: Added `getDynamicNameFontSize()` method
5. **`DYNAMIC_ITEM_NAME_SIZING.md`**: Complete documentation

The dynamic item name sizing feature ensures that all product names are displayed optimally, regardless of their length, while maintaining readability and professional appearance across all sticker sizes.
