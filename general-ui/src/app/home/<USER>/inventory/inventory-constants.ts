import {environment} from '../../../../environments/environment';

export class ApiConstants {

  public static API_URL = environment.apiUrl;

  public static SAVE_BRAND = ApiConstants.API_URL + 'brand/save';
  public static GET_BRANDS = ApiConstants.API_URL + 'brand/findAll';
  public static SEARCH_BRAND = ApiConstants.API_URL + 'brand/search';

  public static SAVE_MODEL = ApiConstants.API_URL + 'model/save';
  public static GET_MODELS = ApiConstants.API_URL + 'model/findAll';
  public static SEARCH_MODEL = ApiConstants.API_URL + 'model/search';

  public static SAVE_RACK = ApiConstants.API_URL + 'rack/save';
  public static GET_RACKS = ApiConstants.API_URL + 'rack/findAll';
  public static GET_All_RACKS = ApiConstants.API_URL + 'rack/findAllRacks';
  public static SEARCH_RACK = ApiConstants.API_URL + 'rack/search';
  public static FIND_RACK = ApiConstants.API_URL + 'rack/searchByID';

  public static SAVE_ITEM_CATEGORY = ApiConstants.API_URL + 'item-category/save';
  public static GET_ITEM_CATEGORY = ApiConstants.API_URL + 'item-category/findAll';
  public static DELETE_ITEM_CATEGORY = ApiConstants.API_URL + 'item-category/delete';
  public static SEARCH_ITEM_CATEGORY = ApiConstants.API_URL + 'item-category/search';
  public static GET_CATEGORY = ApiConstants.API_URL + 'item-category/findById';

  public static SAVE_ITEM = ApiConstants.API_URL + 'item/save';
  public static GET_ITEM = ApiConstants.API_URL + 'item/findAll';
  public static GET_ITEM_FOR_STOCK_REPORT = ApiConstants.API_URL + 'item/findAllForStockReport';
  public static DELETE_ITEM = ApiConstants.API_URL + 'item/delete';
  public static FIND_ACTIVE_BY_NAME_LIKE = ApiConstants.API_URL + 'item/findActiveByNameLike';
  public static FIND_ALL_BY_NAME_LIKE = ApiConstants.API_URL + 'item/findAllByNameLike';
  public static FIND_ALL_BY_BARCODE_LIKE = ApiConstants.API_URL + 'item/findByBarcodeLike';
  public static FIND_ONE_BY_BARCODE = ApiConstants.API_URL + 'item/findOneByBarcode';
  public static FIND_ACTIVE_BY_NAME_LIKE_FOR_SERIAL = ApiConstants.API_URL + 'item/findActiveByNameLikeForSerialManagement';
  public static FIND_ACTIVE_BY_BARCODE_LIKE_FOR_SERIAL = ApiConstants.API_URL + 'item/findActiveByBarcodeLikeForSerialManagement';
  public static CHECK_AVAILABILITY_BY_BARCODE = ApiConstants.API_URL + 'item/checkAvailabilityByBarcode';
  public static UPDATE_BARCODE = ApiConstants.API_URL + 'item/updateBarcode';
  public static UPDATE_ITEM_PROPERTIES = ApiConstants.API_URL + 'item/updateItemProperties';
  public static FIND_ONE_BY_ITEM_CODE = ApiConstants.API_URL + 'item/findOneByItemCode';
  public static FIND_BY_CATEGORY = ApiConstants.API_URL + 'item/findByCategory';
  public static FIND_BY_BRAND = ApiConstants.API_URL + 'item/findByBrand';
  public static FIND_ONE_BY_ID = ApiConstants.API_URL + 'item/findOneById';
  public static GET_ITEM_FILTERED = ApiConstants.API_URL + 'item/findAllFiltered';

  public static SAVE_ITEM_TYPE = ApiConstants.API_URL + 'itemType/save';
  public static GET_ITEM_TYPE = ApiConstants.API_URL + 'itemType/findAll';
  public static GET_ITEM_TYPE_FOR_SELECT = ApiConstants.API_URL + 'itemType/findAllForSelect';
  public static DELETE_ITEM_TYPE = ApiConstants.API_URL + 'itemType/delete';
  public static SEARCH_ITEM_TYPE = ApiConstants.API_URL + 'itemType/search';

  public static ADD_MAIN_STOCK_MANUAL = ApiConstants.API_URL + 'stock/addStockManual';
  public static ADJUST_MAIN_STOCK = ApiConstants.API_URL + 'stock/adjustStock';
  public static GET_MAIN_STOCK = ApiConstants.API_URL + 'stock/findAllStock';
  public static FIND_STOCK_BY_BARCODE_LIKE = ApiConstants.API_URL + 'stock/findStockByWarehouseAndBarcodeLike';
  public static FIND_STOCK_BY_ITEM_NAME_LIKE = ApiConstants.API_URL + 'stock/findStockByWarehouseAndItemNameLike';
  public static FIND_MAIN_STOCK_BY_ITEM_CODE_PRICE = ApiConstants.API_URL + 'stock/findMainStockByItemCode';
  public static FIND_MAIN_STOCK_RECS_BY_ITEM_CODE = ApiConstants.API_URL + 'stock/findMainStockRecsByItemCode';
  public static TRANSFER_STOCK = ApiConstants.API_URL + 'stock/transferStock';
  public static FIND_STOCK_BY_BARCODE_AND_WAREHOUSE = ApiConstants.API_URL + 'stock/findByBarcodeAndWarehouse';
  public static FIND_PRICES_BY_BARCODE_AND_WAREHOUSE = ApiConstants.API_URL + 'stock/findPricesByBarcodeAndWarehouse';
  public static FIND_STOCK_BY_ITEM_CODE_AND_WAREHOUSE_AND_PRICE = ApiConstants.API_URL + 'stock/findByItemCodeAndWarehouseAndPrice';
  public static FIND_STOCK_RECORDS_BY_ITEM = ApiConstants.API_URL + 'stock/findStockRecordsByItem';

  // Supplier Return endpoints
  public static SAVE_SUPPLIER_RETURN = ApiConstants.API_URL + 'supplierReturn/save';
  public static GET_SUPPLIER_RETURNS = ApiConstants.API_URL + 'supplierReturn/findAll';
  public static FIND_SUPPLIER_RETURNS_BY_SUPPLIER = ApiConstants.API_URL + 'supplierReturn/findBySupplier';
  public static FIND_SUPPLIER_RETURNS_BY_ITEM = ApiConstants.API_URL + 'supplierReturn/findByItem';
  public static FIND_SUPPLIER_RETURNS_BY_DATE_RANGE = ApiConstants.API_URL + 'supplierReturn/findByDateRange';
  public static FIND_SUPPLIER_RETURNS_BY_SUPPLIER_AND_DATE_RANGE = ApiConstants.API_URL + 'supplierReturn/findBySupplierAndDateRange';
  public static FIND_SUPPLIER_RETURN_BY_ID = ApiConstants.API_URL + 'supplierReturn/findById';
  public static FIND_STOCK_SUMMARY = ApiConstants.API_URL + 'stock/findStockSummary';
  public static FIND_BY_WAREHOUSE = ApiConstants.API_URL + 'stock/findAllByWarehouse';
  public static FIND_REORDER_LIST_BY_WAREHOUSE = ApiConstants.API_URL + 'stock/findReorderListByWarehouse';
  public static FIND_REORDER_LIST = ApiConstants.API_URL + 'stock/findReorderList';
  public static FIND_STOCK_BY_ITEM_CATEGORY_WH_CODE = ApiConstants.API_URL + 'stock/findStockByItemCategoryAndWh';
  public static FIND_STOCK_BY_BRAND_WH_CODE = ApiConstants.API_URL + 'stock/findStockByBrandAndWh';
  public static GET_STOCK_DETAIL_REPORT = ApiConstants.API_URL + 'stock/getDetailReport';

  public static SAVE_UOM = ApiConstants.API_URL + 'uom/save';
  public static GET_UOM = ApiConstants.API_URL + 'uom/findAll';
  public static DELETE_UOM = ApiConstants.API_URL + 'uom/delete';
  public static SEARCH_UOM = ApiConstants.API_URL + 'uom/search';

  public static SEARCH_SUB_ITEM_CATEGORY = ApiConstants.API_URL + 'subItemCategory/search';
  public static GET_SUB_ITEM_CATEGORY = ApiConstants.API_URL + 'subItemCategory/get';
  public static SAVE_SUB_ITEM_CATEGORY = ApiConstants.API_URL + 'subItemCategory/save';
  public static DELETE_SUB_ITEM_CATEGORY = ApiConstants.API_URL + 'subItemCategory/delete';
  public static FIND_SUB_ITEM_CATEGORY = ApiConstants.API_URL + 'subItemCategory/findByParent';
  public static FIND_ITEM_CATEGORY = ApiConstants.API_URL + 'subItemCategory/findAll';

  public static SAVE_WAREHOUSE = ApiConstants.API_URL + 'warehouse/save';
  public static UPDATE_WAREHOUSE = ApiConstants.API_URL + 'warehouse/update';
  public static FIND_USER_SPECIFIC_WH = ApiConstants.API_URL + 'warehouse/findUserSpecific';
  public static FIND_ALL_WH = ApiConstants.API_URL + 'warehouse/findAll';
  public static FIND_WH_BY_CODE = ApiConstants.API_URL + 'warehouse/findByCode';
  public static GET_WAREHOUSE_PAGE = ApiConstants.API_URL + 'warehouse/findAllPage';
  public static FIND_WAREHOUSE = ApiConstants.API_URL + 'warehouse/findById';
  public static DELETE_WAREHOUSE = ApiConstants.API_URL + 'warehouse/delete';
  public static SEARCH_WAREHOUSE = ApiConstants.API_URL + 'warehouse/findAllByName';
  public static FIND_TARGET_WAREHOUSE = ApiConstants.API_URL + 'warehouse/findTargetWarehouse';

  // New constants for stock operations
  public static FIND_STOCK_BY_ITEM_AND_WAREHOUSE = ApiConstants.API_URL + 'stock/findByItemAndWarehouse';
  public static FIND_STOCK_BY_ID = ApiConstants.API_URL + 'stock/findById';
  public static SAVE_STOCK = ApiConstants.API_URL + 'stock/save';

  // New constants for reorder report filters
  public static FIND_STOCK_BY_SUPPLIER = ApiConstants.API_URL + 'stock/findBySupplier';

  // Serial Number Management
  public static SERIAL_NUMBERS = ApiConstants.API_URL + 'serial-numbers';
}
