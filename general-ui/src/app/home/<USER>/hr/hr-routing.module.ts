import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {EmployeeComponent} from './components/employee/employee.component';
import {ManageEmployeeComponent} from './components/manage-employee/manage-employee.component';
import {DepartmentComponent} from './components/department/department.component';
import {DesignationComponent} from './components/designation/designation.component';
import {ManageSalaryScaleComponent} from './components/manage-salary-scale/manage-salary-scale.component';
import {LeaveTypesComponent} from './components/leave-types/leave-types.component';
import {ManageLeavesComponent} from './components/manage-leaves/manage-leaves.component';
import {LeaveRequestComponent} from './components/leave-request/leave-request.component';
import {HierarchyComponent} from './components/employee-hierarchy/employee-hierarchy.component';
import {ShiftComponent} from './components/shift/shift.component';

const routes: Routes = [
  {
    path: 'employee',
    component: EmployeeComponent
  },
  {
    path: 'manage_employee',
    component: ManageEmployeeComponent
  },
  {
    path: 'department',
    component: DepartmentComponent
  },
  {
    path: 'designation',
    component: DesignationComponent
  },
  {
    path: 'manage_salary_scale',
    component: ManageSalaryScaleComponent
  },
  {
    path: 'leave_types',
    component: LeaveTypesComponent
  },
  {
    path: 'manage_leaves',
    component: ManageLeavesComponent
  },
  {
    path: 'leave_request',
    component: LeaveRequestComponent
  },
  {
    path: 'employee_hierarchy',
    component: HierarchyComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class HrRoutingModule {
}

export const hrRouteParams = [EmployeeComponent, HierarchyComponent, ManageLeavesComponent, LeaveRequestComponent,
  LeaveTypesComponent, ManageSalaryScaleComponent, DesignationComponent, DepartmentComponent, ManageEmployeeComponent,
  ShiftComponent
];
