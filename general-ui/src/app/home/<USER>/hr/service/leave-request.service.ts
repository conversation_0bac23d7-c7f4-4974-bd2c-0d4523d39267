import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {HrApiConstants} from '../hr-constants';

@Injectable({
  providedIn: 'root'
})
export class LeaveRequestService {

  constructor(private http: HttpClient) {
  }

  save(leaveRequest) {
    return this.http.post<any>(HrApiConstants.SAVE_LEAVE_REQUEST, leaveRequest);
  }

  public findAllPagination(page, pageSize) {
    return this.http.get(HrApiConstants.GET_LEAVE_REQUEST, {params: {page, pageSize}});
  }

  public findAllByType(type, page, pageSize) {
    return this.http.get(HrApiConstants.GET_All_LEAVE_REQUEST_BY_TYPE, {
      params: {
        type: type,
        page: page,
        pageSize: pageSize
      }
    });
  }

  public findByEpf(epf) {
    return this.http.get(HrApiConstants.SEARCH_EPF, {params: {any: epf}});
  }

  findByDatesAndEmployee(from, to, employee: string) {
    return this.http.get(HrApiConstants.FIND_BY_DATES_AND_EMPLOYEE, {
      params: {
        from: from,
        to: to,
        employee: employee
      }
    });
  }
}
