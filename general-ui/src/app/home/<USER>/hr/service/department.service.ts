import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {HrApiConstants} from '../hr-constants';

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {

  constructor(private http: HttpClient) {
  }

  save(department) {
    return this.http.post<any>(HrApiConstants.SAVE_DEPARTMENT, department);
  }

  public findAllPagination(page, pageSize) {
    return this.http.get(HrApiConstants.GET_DEPARTMENT, {params: {page: page, pageSize: pageSize}});
  }

  findById(id: string) {
    return this.http.get(HrApiConstants.FIND_DEPARTMENT_BY_ID, {params: {id: id}});
  }

  getAll() {
    return this.http.get(HrApiConstants.FIND_ALL_DEPARTMENT);
  }

  findByDepartmentName(name: string) {
    return this.http.get(HrApiConstants.FIND_BY_DEPARTMENT_NAME, {params: {name: name}});
  }


}
