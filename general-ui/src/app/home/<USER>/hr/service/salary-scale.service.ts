import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {HrApiConstants} from '../hr-constants';

@Injectable({
  providedIn: 'root'
})
export class SalaryScaleService {

  constructor(private http: HttpClient) { }

  save (salary) {
    return this.http.post<any>(HrApiConstants.SAVE_SALARY, salary);
  }


  public findAll(page, pageSize) {
    return this.http.get(HrApiConstants.GET_ALL_SALARY_SCALE, {params: {page: page, pageSize: pageSize}});

  }


  public findAllbyName(name) {
    return this.http.get(HrApiConstants.GET_ALL_SALARY_SCALE_BY_NAME, {params: {name: name}});
  }
  public findById(id) {
    return this.http.get(HrApiConstants.GET_SALARY_SCALE_BY_ID, {params: {id: id}});
  }
  public findAllSalaryScale () {
    return this.http.get(HrApiConstants.GET_SALARY_SCALE);

  }

  findBySalaryScaleName(name: string) {
    return this.http.get(HrApiConstants.FIND_BY_SALARY_SCALE_NAME, {params: {name: name}});
  }

}
