import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {HrApiConstants} from '../hr-constants';
import {Employee} from "../model/employee";

@Injectable({
  providedIn: 'root'
})

export class EmployeeService {

  constructor(private http: HttpClient) {
  }

  public findByEmployeeNameLike(name) {
    return this.http.get<Employee[]>(HrApiConstants.FIND_EMPLOYEE_BY_NAME_LIKE, {params: {name: name}});
  }

  public findAllEmployee(page, pageSize) {
    return this.http.get(HrApiConstants.FIND_ALL_EMPLOYEE, {params: {page: page, pageSize: pageSize}});
  }

  save(employee) {
    return this.http.post(HrApiConstants.SAVE_EMPLOYEE, employee);
  }

  public findAllByDesignation(search) {
    return this.http.get(HrApiConstants.FIND_ALL_BY_DESIGNATION_LIKE, {params: {name: search}});
  }

  public findByEmployeeId(id) {
    return this.http.get(HrApiConstants.SEARCH_BY_EMPLOYEE_ID, {params: {id: id}});
  }

  public checkEpf(epf) {
    return this.http.get(HrApiConstants.EMP_EPF_NO_CHECK, {params: {epf: epf}});
  }

  public checkNic(nic) {
    return this.http.get(HrApiConstants.CHK_NIC, {params: {nic: nic}});
  }

}
