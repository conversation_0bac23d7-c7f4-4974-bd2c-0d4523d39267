import {environment} from '../../../../environments/environment';

export class HrApiConstants {

  public static API_URL = environment.apiUrl;
  public static SAVE_DESIGNATION = HrApiConstants.API_URL + 'designation/save';
  public static GET_DESIGNATION = HrApiConstants.API_URL + 'designation/findAll';
  public static FIND_DESIGNATION = HrApiConstants.API_URL + 'designation/getAll';
  public static FIND_BY_ID = HrApiConstants.API_URL + 'designation/findById';
  public static DELETE_DESIGNATION = HrApiConstants.API_URL + 'designation/remove';
  public static FIND_ALL_BY_DESIGNATION_LIKE = HrApiConstants.API_URL + 'designation/findAllByDesignationLike';
  public static FIND_BY_DESIGNATION_NAME = HrApiConstants.API_URL + 'designation/findByDesignationName';
  public static FIND_BY_DESIGNATION = HrApiConstants.API_URL + 'designation/findByDesignation'

  public static SAVE_SALARY = HrApiConstants.API_URL + 'salaryScale/save';
  public static GET_ALL_SALARY_SCALE = HrApiConstants.API_URL + 'salaryScale/getAll';
  public static GET_ALL_SALARY_SCALE_BY_NAME = HrApiConstants.API_URL + 'salaryScale/searchByName';
  public static GET_SALARY_SCALE_BY_ID = HrApiConstants.API_URL + 'salaryScale/searchByID';
  public static GET_SALARY_SCALE = HrApiConstants.API_URL + 'salaryScale/findAll';
  public static FIND_BY_SALARY_SCALE_NAME = HrApiConstants.API_URL + 'salaryScale/findBySalaryScaleName';

  public static SEARCH_EMPLOYEE = HrApiConstants.API_URL + 'employee/findEmployees';
  public static FIND_EMPLOYEE_BY_NAME_LIKE = HrApiConstants.API_URL + 'employee/findEmployeeByNameLike';
  public static FIND_ALL_EMPLOYEE = HrApiConstants.API_URL + 'employee/findAllEmployee';
  public static SAVE_EMPLOYEE = HrApiConstants.API_URL + 'employee/save';
  public static CHK_NIC = HrApiConstants.API_URL + 'employee/checkNic';
  public static SEARCH_EMPLOYEE_BY_PERSON_ID = HrApiConstants.API_URL + 'employee/findEmployeeByPersonId';
  public static SEARCH_BY_EMPLOYEE_ID = HrApiConstants.API_URL + 'employee/findByEmployeeId';
  public static EMP_EPF_NO_CHECK = HrApiConstants.API_URL + 'employee/check_epf_no';

  public static SAVE_DEPARTMENT = HrApiConstants.API_URL + 'department/save';
  public static GET_DEPARTMENT = HrApiConstants.API_URL + 'department/findAll';
  public static FIND_ALL_DEPARTMENT = HrApiConstants.API_URL + 'department/getAll';
  public static FIND_DEPARTMENT_BY_ID = HrApiConstants.API_URL + 'department/findById';
  public static FIND_BY_DEPARTMENT_NAME = HrApiConstants.API_URL + 'department/findByDepartmentName';

  public static SAVE_ADD_LEAVE = HrApiConstants.API_URL + 'addLeaveTypes/save';
  public static GET_ADD_LEAVES = HrApiConstants.API_URL + 'addLeaveTypes/findAll';

  public static FIND_BY_LEAVE_ID = HrApiConstants.API_URL + 'addLeaveTypes/findById';
  public static FIND_LEAVE_TYPES = HrApiConstants.API_URL + 'addLeaveTypes/getAll';
  public static FIND_BY_LEAVE_NAME = HrApiConstants.API_URL + 'addLeaveTypes/findByName';

// job Role
  public static SAVE_JOB_ROLE = HrApiConstants.API_URL + 'jobRole/save';
  public static GET_JOB_ROLE = HrApiConstants.API_URL + 'jobRole/findAllPagination';
  public static GET_ALL_JOB_ROLE = HrApiConstants.API_URL + 'jobRole/findAll';
  public static GET_JOB_ROLE_BY_ID = HrApiConstants.API_URL + 'jobRole/searchByID';
  public static FIND_BY_JOB_ROLE_NAME = HrApiConstants.API_URL + 'jobRole/findByJobRoleName';

  // LEAVE REQUEST
  public static SAVE_LEAVE_REQUEST = HrApiConstants.API_URL + 'leaveRequest/save';
  public static GET_LEAVE_REQUEST = HrApiConstants.API_URL + 'leaveRequest/findAll';
  public static GET_All_LEAVE_REQUEST_BY_TYPE = HrApiConstants.API_URL + 'leaveRequest/findAllByType';
  //  public static GET_MANAGE_LEAVES = ApiConstants.API_URL + 'leaveRequest/findAllManageLeaves';
  //  public static GET_EPF_BY_NO_LIKE = ApiConstants.API_URL + 'leaveRequest/findByEpfLike';
  public static SEARCH_EPF = HrApiConstants.API_URL + 'leaveRequest/search';
  public static FIND_BY_DATES_AND_EMPLOYEE = HrApiConstants.API_URL + 'leaveRequest/findByDatesAndEmployee';

// EMPLOYEE hierarchy
  public static SAVE_HIERARCHY = HrApiConstants.API_URL + 'hierarchy/save';
  public static GET_HIERARCHY = HrApiConstants.API_URL + 'hierarchy/findAll';
  public static GET_HIERARCHIES = HrApiConstants.API_URL + 'hierarchy/findAllPagination';
  public static SEARCH_MANAGER = HrApiConstants.API_URL + 'hierarchy/search';
}
