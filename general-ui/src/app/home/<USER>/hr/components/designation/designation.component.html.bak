<div class="container-fluid px-0">
  <h2 class="component-title">MANAGE DESIGNATION</h2>

  <div class="row">
      <div class="col-md-6">
        <table class="table">
          <thead>
          <tr>
            <th>Designation</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let dis of designations,let i = index " (click)="designationDetail(dis);setClickedRow(i)"
              [class.active]="i === selectedRow">
            <td>{{dis.designationName}}</td>
          </tr>
          </tbody>
        </table>
        <div class="row">
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center"
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>  <div class="col-md-6">
        <form #ManageDesignation="ngForm">
          <label>Designation </label>
          <input required #typeValidation="ngModel" class="form-control" id="typeValidation" type="text"
                 name="typeValidation"
                 [class.is-invalid]="typeValidation.invalid && typeValidation.touched" placeholder="Enter Designation"
                 (keyup)="chekValidDesignation()" [(ngModel)]="designation.designationName">
          <small class="text-danger" [class.d-none]="typeValidation.valid || typeValidation.untouched">*Designation  is
            Required
          </small>
          <small *ngIf="invalidDesignation" [class.is-none]="true" class="text-danger">* Designation is
            already used
          </small>
          <div class="row ml-1">
            <div class="form-check checkbox mr-5 col-md-6">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="designation.active">
              <label class="form-check-label" for="check3">Is Active</label>
            </div>
          </div>  <div class="row text-right mt-2">
            <div class="mr-3">
              <button class="btn btn-theme" [disabled]="!ManageDesignation.form.valid || invalidDesignation"
                      (confirm)="save(ManageDesignation)" mwlConfirmationPopover >save</button>
            </div>  <div class="mr-3">
              <button class="btn btn-warning" (click)="Clear()">Clear</button>
            </div>
          </div>
        </form>
      </div>
    </div>
</div>

