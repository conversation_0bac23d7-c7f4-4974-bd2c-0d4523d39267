<div class="container-fluid px-0">
  <h2 class="component-title">MANAGE SALARY</h2>
    <div class="row">
      <div class="input-group col-md-6">
        <table class="table table-striped">
          <thead>
          <tr>
            <th>Salary Name</th>
            <th>Basic Salary</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let salary of salaryScaleArray,let i=index" (click)="salaryDetail(salary,i) "
              [class.active]="i === selectedRow">
            <td>{{salary.name}}</td>
            <td>{{salary.basicSalary}}</td>
          </tr>
          </tbody>
        </table>
        <div class="row">
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center"
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>  <div class="col-md-6">
        <form #manageSalaryForm="ngForm">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
              <label>Name </label>
              <input type="text" required #name="ngModel" [class.is-invalid]="name.invalid && name.touched"
                     class="form-control" id="name" (keyup)="invalidSalaryScale()" [(ngModel)]="salaryScale.name" name="name"
                     placeholder="Enter Name ">
             <!--<div *ngIf="name.errors && (name.invalid || name.touched)">-->
                <small class="text-danger" [class.d-none]="name.valid || name.untouched">*Name is required
                </small>
              <!--</div>-->
                <small *ngIf="invalidScale" [class.is-none]="true" class="text-danger">* LeaveName is
                  already used
                </small>
              </div>

            </div>  <div class="col-md-6">
              <label>Basic Salary </label>
              <input type="number" required #bName="ngModel" [class.is-invalid]="bName.invalid && bName.touched"
                     class="form-control" id="bName" [(ngModel)]="salaryScale.basicSalary" name="bName"
                     placeholder="Enter Basic Salary ">
              <div *ngIf="bName.errors && (bName.invalid || bName.touched)">
                <small class="text-danger" [class.d-none]="bName.valid || bName.untouched">*Basic Salary is required
                </small>
              </div>
            </div>
          </div>  <div class="row">
            <div class="col-md-6">
              <div class="form-group">
              <label>EPF</label>
              <input type="number"
                     class="form-control" id="epf" [(ngModel)]="salaryScale.epf" name="epf"
                     placeholder="Enter EPF">
            </div>
            </div>  <div class="col-md-6">
              <label>ETF </label>
              <input type="number"
                     class="form-control" id="etf" [(ngModel)]="salaryScale.etf" name="etf"
                     placeholder="Enter ETF ">
            </div>
          </div>  <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Living Allowance </label>
                <input type="number"
                       class="form-control" id="livingAllowance" [(ngModel)]="salaryScale.livingAllowance" name="livingAllowance"
                       placeholder="Enter living Allowance">
              </div>

            </div>  <div class="col-md-6">
              <label>Meal Allowance </label>
              <input type="number"
                     class="form-control" id="mealAllowance" [(ngModel)]="salaryScale.mealAllowance" name="mealAllowance"
                     placeholder="Enter Meal Allowance ">
            </div>
          </div>  <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Vehicle Allowance </label>
                <input type="number"
                       class="form-control" id="vehicleAllowance" [(ngModel)]="salaryScale.vehicleAllowance" name="vehicleAllowance"
                       placeholder="Enter Vehicle Allowance">
              </div>
            </div>  <div class="col-md-6">
              <label>Special Allowance</label>
              <input type="number"
                     class="form-control" id="specialAllowance" [(ngModel)]="salaryScale.specialAllowance" name="specialAllowance"
                     placeholder="Enter Special Allowance">
            </div>
          </div>  <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                <label>Housing Allowance</label>
                <input type="number"
                       class="form-control" id="housingAllowance" [(ngModel)]="salaryScale.housingAllowance" name="housingAllowance"
                       placeholder="Enter Housing Allowance">
            </div>

            </div>  <div class="col-md-6">
              <label>Budgetary Allowance</label>
              <input type="number"
                     class="form-control" id="budgetaryAllowance" [(ngModel)]="salaryScale.budgetaryAllowance" name="budgetaryAllowance"
                     placeholder="Enter Budgetary Allowance">
            </div>
          </div>  <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Attendance Incentive</label>
                <input type="number"
                       class="form-control" id="attendanceIncentive" [(ngModel)]="salaryScale.attendanceIncentive" name="attendanceIncentive"
                       placeholder="Enter Attendance Incentive">
              </div>

            </div>  <div class="col-md-6">
              <label>OT Rate PerHour</label>
              <input type="number"
                     class="form-control" id="otRatePerHour" [(ngModel)]="salaryScale.otRatePerHour" name="otRatePerHour"
                     placeholder="Enter OT Rate PerHour">
            </div>
          </div>  <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>noPay Deduction Basic</label>
                <input type="number"
                       class="form-control" id="noPayDeductionBasic" [(ngModel)]="salaryScale.noPayDeductionBasic" name="noPayDeductionBasic"
                       placeholder="Enter noPay Deduction Basic">
              </div>

            </div>  <div class="col-md-6">
              <label>noPay Deduction EPF</label>
              <input type="number"
                     class="form-control" id="noPayDeductionEpf" [(ngModel)]="salaryScale.noPayDeductionEpf" name="noPayDeductionEpf"
                     placeholder="Enter noPay Deduction Epf">
            </div>
          </div>  <div class="row ml-1">
            <div class="form-check checkbox mr-5 col-md-6">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="salaryScale.active">
              <label class="form-check-label" for="check3">Is Active</label>
            </div>
          </div>  <div class="row text-right">
            <div class="col-md-12">
              <button type="button" (click)="save(manageSalaryForm)" class="btn btn-primary mr-1"
                      [disabled]="!manageSalaryForm.form.valid || invalidScale" >save</button>
              <button type="button" (click)="clear()" class="btn btn-warning">clear</button>
            </div>
          </div>
        </form>
      </div>
  </div>

