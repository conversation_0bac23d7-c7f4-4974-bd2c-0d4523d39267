import {Component, OnInit} from '@angular/core';
import {NotificationService} from '../../../../core/service/notification.service';
import {MetaDataService} from '../../../../core/service/metaData.service';

import {MetaData} from '../../../../core/model/metaData';
import {HierarchyService} from '../../service/hierarchy.service';
import {Hierarchy} from '../../model/hierarchy';
import {Employee} from '../../model/employee';
import {EmployeeService} from '../../service/employee.service';
import {NgForm} from '@angular/forms';
import {BsModalRef, BsModalService} from 'ngx-bootstrap/modal';


@Component({
  selector: 'app-employee-hierarchy',
  templateUrl: './employee-hierarchy.component.html',
  styleUrls: ['./employee-hierarchy.component.css']
})
export class HierarchyComponent implements OnInit {
  hierarchy = new Hierarchy();
  hierarchies: Array<Hierarchy> = [];
  employees: Array<Employee> = [];
  keyEmployee: string;
  keyReportingManager: string;
  searchKeyReportingManager: string;
  keyManager: string;
  selectedRow: number;
  setClickedRow: Function;
  collectionSize;
  page;
  pageSize;
  type: MetaData;
  modalRef: BsModalRef;


  constructor(public  hierarchyService: HierarchyService,
              private notificationService: NotificationService,
              public metaDataService: MetaDataService,
              public employeeService: EmployeeService,
              private modalService: BsModalService) {

  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.findType();
    this.type = new MetaData();
    this.findAllPagination();
  }

  save(form: NgForm) {
    this.hierarchyService.save(this.hierarchy).subscribe(result => {
      this.notificationService.showSuccess(result);
      this.findAll();
      form.reset();
    });
  }

  findType() {
    this.metaDataService.findByValueAndCategory('employee', 'PersonType').subscribe((res: MetaData) => {
      this.type = res;
    });
  }

  setSelectedEmployee(e) {
    this.employeeService.findByEmployeeId(e.item.id).subscribe((data: Employee) => {
      this.hierarchy.employee = data;
    });
  }

  loadEmployees() {
    this.employeeService.findByEmployeeNameLike(this.keyEmployee).subscribe((data: Array<Employee>) => {
      return this.employees = data;
    });
  }

  setSelectedReportingManager(e) {
    this.employeeService.findByEmployeeId(e.item.id).subscribe((data: Employee) => {
      if (this.hierarchy.employee.id === data.id) {
        this.notificationService.showError('You Can\'t Add This Employee');
        this.keyReportingManager = null;
      } else {
        this.hierarchy.reportingManager = data;
      }
    });
  }

  loadReportingManager() {
    this.employeeService.findByEmployeeNameLike(this.keyReportingManager).subscribe((data: Array<Employee>) => {
      return this.employees = data;
    });
  }

  searchLoadReportingManager() {
    this.employeeService.findByEmployeeNameLike(this.searchKeyReportingManager).subscribe((data: Array<Employee>) => {
      return this.employees = data;
    });
  }

  setSearchSelectedReportingManager(e) {
    this.hierarchyService.findByManager(e.item.id).subscribe((data: Array<Hierarchy>) => {
      this.hierarchies = [];
      this.hierarchies = data;
      console.log(data);
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAllPagination();
  }

  findAll() {
    this.hierarchyService.findAll().subscribe((data: Array<Hierarchy>) => {
      this.hierarchies = data;
      console.log(data);
    });
  }

  findAllPagination() {
    this.hierarchyService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.hierarchies = data.content;
      this.collectionSize = data.totalPages * 10;
    });
  }

  hierarchyDetail(m) {
    this.hierarchy = m;
  }

  clear() {
    this.hierarchy = new Hierarchy();
  }
}
