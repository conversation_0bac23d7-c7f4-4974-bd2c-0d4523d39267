export interface BarcodeSettings {
  // Existing settings
  useCostCodes: boolean;
  showBarcode: boolean;
  columns: number;
  paperSize: string;
  
  // New font size settings
  itemNameFontSize: number;
  priceFontSize: number;
  costCodeFontSize: number;
  barcodeTextFontSize: number;
  
  // Font family settings
  fontFamily: string;
}

export interface CostCodeSettings {
  letterMapping: { [key: string]: string }; // Maps numbers 0-9 to letters
}

export interface BarcodeSettingsResponse {
  success: boolean;
  message: string;
  data?: BarcodeSettings;
}

export class BarcodeSettingsModel {
  // Default values
  static getDefaultSettings(): BarcodeSettings {
    return {
      useCostCodes: false,
      showBarcode: true,
      columns: 2,
      paperSize: '30x20',
      itemNameFontSize: 12,
      priceFontSize: 10,
      costCodeFontSize: 9,
      barcodeTextFontSize: 8,
      fontFamily: 'Arial, sans-serif'
    };
  }

  // Paper size options
  static getPaperSizeOptions(): { value: string, label: string }[] {
    return [
      { value: '30x20', label: '30mm x 20mm' },
      { value: '33x21', label: '33mm x 21mm' },
      { value: '38x25', label: '38mm x 25mm' },
      { value: '50x25', label: '50mm x 25mm' },
      { value: '65x15', label: '65mm x 15mm' },
      { value: '100x50', label: '100mm x 50mm' },
      { value: '100x150', label: '100mm x 150mm' }
    ];
  }

  // Font family options
  static getFontFamilyOptions(): { value: string, label: string }[] {
    return [
      { value: 'Arial, sans-serif', label: 'Arial (Recommended)' },
      { value: 'Helvetica, Arial, sans-serif', label: 'Helvetica' },
      { value: 'Times, "Times New Roman", serif', label: 'Times New Roman' },
      { value: '"Courier New", "Lucida Console", monospace', label: 'Courier New' },
      { value: 'Verdana, Geneva, sans-serif', label: 'Verdana' },
      { value: 'Georgia, serif', label: 'Georgia' },
      { value: 'Tahoma, Geneva, sans-serif', label: 'Tahoma' }
    ];
  }

  // Column options
  static getColumnOptions(): { value: number, label: string }[] {
    return [
      { value: 1, label: '1 Column' },
      { value: 2, label: '2 Columns' },
      { value: 3, label: '3 Columns' },
      { value: 4, label: '4 Columns' }
    ];
  }
}
