import { User } from './user';

/**
 * Model for a setting value with enabled status
 */
export interface SettingValue {
  value: string;
  enabled: boolean;
}

/**
 * Model for user settings
 */
export class UserSettings {
  id: string;
  user: User;
  settings: { [key: string]: SettingValue };
  createdDate: Date;
  createdBy: string;
  lastModifiedDate: Date;
  lastModifiedBy: string;

  constructor() {
    this.settings = {};
  }

  /**
   * Add or update a setting
   * @param key Setting key
   * @param value Setting value
   * @param enabled Whether the setting is enabled
   */
  addSetting(key: string, value: string, enabled: boolean = true): void {
    this.settings[key] = { value, enabled };
  }

  /**
   * Get a setting value
   * @param key Setting key
   * @returns Setting value or null if not found or disabled
   */
  getSettingValue(key: string): string | null {
    const setting = this.settings[key];
    return (setting && setting.enabled) ? setting.value : null;
  }

  /**
   * Check if a setting is enabled
   * @param key Setting key
   * @returns true if setting exists and is enabled, false otherwise
   */
  isSettingEnabled(key: string): boolean {
    const setting = this.settings[key];
    return setting ? setting.enabled : false;
  }

  /**
   * Enable or disable a setting
   * @param key Setting key
   * @param enabled Whether the setting should be enabled
   * @returns true if setting exists and was updated, false otherwise
   */
  setSettingEnabled(key: string, enabled: boolean): boolean {
    const setting = this.settings[key];
    if (setting) {
      setting.enabled = enabled;
      return true;
    }
    return false;
  }

  /**
   * Get all enabled settings as a key-value map
   * @returns Map of setting keys to values (only enabled settings)
   */
  getAllEnabledSettings(): { [key: string]: string } {
    const result: { [key: string]: string } = {};
    for (const key in this.settings) {
      if (this.settings[key].enabled) {
        result[key] = this.settings[key].value;
      }
    }
    return result;
  }
}
