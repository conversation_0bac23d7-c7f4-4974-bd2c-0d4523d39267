import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {AdminRoutingModule, adminRouteParams} from './admin-routing.module';
import {CoreModule} from '../core/core.module';
import {NgxLoadingModule} from "ngx-loading";
import {ReactiveFormsModule, FormsModule} from "@angular/forms";

@NgModule({
  declarations: [adminRouteParams],
    imports: [
        CommonModule,
        CoreModule,
        AdminRoutingModule,
        NgxLoadingModule,
        ReactiveFormsModule,
        FormsModule
    ]
})
export class AdminModule {
}
