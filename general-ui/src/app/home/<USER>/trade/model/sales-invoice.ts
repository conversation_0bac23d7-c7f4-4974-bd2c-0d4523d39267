import {SalesInvoiceRecord} from './sales-invoice-record';
import {MetaData} from '../../../core/model/metaData';
import {Cheque} from "./cheque";

export class SalesInvoice {

  id: string;

  invoiceNo: string;

  reference: string;

  date: string;

  advancePayment: number;

  subTotal: number;

  totalAmount: number;

  balance: number;

  totalDiscount: number;

  payment: number;

  cashBalance: number;

  paymentMethod: MetaData;

  cheque: Cheque;

  cashlessAmount: number;

  cardOrVoucherNo: string;

  cashAmount: number;

  dueDate: Date;

  status: MetaData;

  salesInvoiceRecords: Array<SalesInvoiceRecord>;

  customerName: string;

  customerNo: string;

  createdBy: string;

  routeNo: string;

  routeName: string;

  cashierUserName: string;

  drawerNo: string;

}
