import {Item} from '../../inventory/model/item';
import {MetaData} from '../../../core/model/metaData';

export class PurchaseInvoiceRecord {

  id: string;

  stockId: string;

  quantity: number;

  itemCost: number;

  itemName: string;

  sellingPrice: number;

  itemCode: string;

  barcode: string;

  discount: number;

  totalAmount: number;

  subTotal: number;

  item: Item;

  // Serial numbers for items that manage serial (phones, electronics)
  serialNumbers: string; // Comma-separated IMEI/serial numbers

  date;

  warehouseCode: number;

}
