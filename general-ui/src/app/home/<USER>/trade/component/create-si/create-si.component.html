<div class="container-fluid px-0 p-1">
  <div class="theme-color">
    <div *ngIf="!modalRef" class="d-flex justify-content-between align-items-center header-section">
      <div>
        <h2 class="mb-0">{{ 'SITE_NAME' | translate }}</h2>
      </div>
      <div class="d-none d-md-block">
        <i class="fa fa-person fa-2x ml-3 select-item cursor-pointer"
           (click)="openCustomer()"></i>
        <i class="fa fa-luggage-cart fa-2x ml-3 select-item cursor-pointer"
           (click)="openStock()"></i>
        <i class="fa fa-history fa-2x ml-3 select-item cursor-pointer"
           (click)="openPastInvoice()"></i>
        <i class="fa fa-money-bill fa-2x ml-3 select-item cursor-pointer"
           (click)="openCashier()"></i>
        <i class="fa fa-home fa-2x ml-3 select-item cursor-pointer"
           routerLink="../home/<USER>" (click)="closeFullscreen()"></i>
      </div>
    </div>
    <div *ngIf="modalRef" class="d-flex justify-content-between align-items-center header-section">
      <div>
        <h2 class="mb-0">Edit Invoice {{ isUpdateMode ? '(Update Mode)' : '' }}</h2>
        <div *ngIf="isUpdateMode && si && si.invoiceNo" class="text-white">
          <small>Invoice #: {{ si.invoiceNo }} | Customer: {{ si.customerName || 'N/A' }}</small>
        </div>
      </div>
      <div>
        <button type="button" class="btn btn-sm btn-outline-secondary" (click)="close()">
          <i class="fa fa-times"></i>
        </button>
      </div>
    </div>
    <!-- Mobile action buttons -->
    <div class="d-flex justify-content-between mt-2 d-md-none">
      <button class="btn btn-sm btn-secondary px-2" (click)="openCustomer()">
        <i class="fa fa-person"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="openStock()">
        <i class="fa fa-luggage-cart"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="openPastInvoice()">
        <i class="fa fa-history"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="openCashier()">
        <i class="fa fa-money-bill"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" routerLink="../home/<USER>" (click)="closeFullscreen()">
        <i class="fa fa-home"></i>
      </button>
    </div>
  </div>
  <div class="content-section overflow-auto" (keydown.delete)="clear()">
    <div class="p-0 m-0">
      <!-- Item search section - responsive layout -->
      <div class="row mt-0 mx-0">
        <!-- Barcode field -->
        <div class="form-group col-md-2 col-12 mb-2 px-1">
          <label>{{ 'INVENTORY.BARCODE' | translate }}</label>
          <div class="input-group">
            <input [(ngModel)]="keyItemSearch"
                   [typeahead]="itemSearchList"
                   (typeaheadLoading)="searchItems()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="barcode"
                   autocomplete="off"
                   #barcodeEle (keydown.enter)="gotoPayment()"
                   class="form-control" name="searchItem">
            <div class="input-group-append">
              <button class="btn btn-outline-primary" type="button" (click)="openBarcodeScanner()">
                <i class="fa fa-barcode"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Item name field -->
        <div class="form-group col-md-5 col-12 mb-2 px-1">
          <label>Item Name</label>
          <input [(ngModel)]="keyItemNameSearch"
                 [typeahead]="itemNameSearchList"
                 (typeaheadLoading)="searchItemsByName()"
                 (typeaheadOnSelect)="setSelectedItem($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="itemName"
                 autocomplete="off"
                 class="form-control" name="searchItem">
        </div>

        <!-- Price field -->
        <div class="form-group col-md-2 col-4 mb-2 px-1">
          <div class="d-flex justify-content-between align-items-center">
            <label>Price</label>
            <label class="small">{{ "(" + availableQty + ")" }}</label>
          </div>
          <input type="number" required #price="ngModel" #sellingPrice class="form-control" id="price"
                 name="price" [class.is-invalid]="price.invalid && price.touched"
                 [(ngModel)]="sPrice" (keydown.arrowRight)="focusDiscount()">
        </div>

        <!-- Discount field -->
        <div class="form-group col-md-1 col-4 mb-2 px-1">
          <label class="d-flex align-items-center">
            Disc
            <span class="ml-1" *ngIf="isPercentage">(<i class="fa fa-percent"
                                                        (click)="isPercentage = !isPercentage"></i>)</span>
            <span class="ml-1" *ngIf="!isPercentage">(<i class="fa fa-minus" (click)="isPercentage = !isPercentage"></i>)</span>
          </label>
          <div class="position-relative">
            <input type="number" #discountForItm="ngModel" #discountForItem class="form-control"
                   id="discount" name="price"
                   [(ngModel)]="discount" (keydown.arrowRight)="focusQty()">
            <span *ngIf="discount && sPrice" class="position-absolute text-secondary"
                  style="right: 10px; top: 50%; transform: translateY(-50%); font-size: 0.85rem;">
              {{ isPercentage ? (sPrice - (sPrice * discount / 100)).toFixed(2) : (sPrice - discount).toFixed(2) }}
            </span>
          </div>
        </div>

        <!-- Quantity field -->
        <div class="form-group col-md-2 col-4 mb-2 px-1">
          <div class="d-flex justify-content-between align-items-center">
            <label>Qty</label>
            <div class="form-check form-check-inline m-0 p-0">
              <input class="form-check-input" type="checkbox" [checked]="isReturn"
                     (change)="isReturn = !isReturn">
              <label class="form-check-label small">Return</label>
            </div>
          </div>
          <input type="number" required #qty="ngModel" #quantity class="form-control" id="qty"
                 name="qty" [class.is-invalid]="qty.invalid && qty.touched"
                 [(ngModel)]="itemQty" (keydown.enter)="checkAvailability()"
                 (keydown.arrowLeft)="focusPrice()">
        </div>
        <!-- Full width Add button for mobile only -->
        <button class="btn btn-primary w-100 mt-2 mb-2 d-md-none add-item-btn" type="button" (click)="checkAvailability()">
          <i class="fa fa-plus-circle mr-2"></i> Add Item
        </button>
      </div>
      <div class="row p-0 m-0">
        <div class="col-md-12 p-0 m-0">
          <div class="table-height">
            <!-- Desktop table view -->
            <div class="table-responsive d-none d-md-block">
              <table class="table table-bordered">
                <thead>
                <tr>
                  <td style="width: 350px; !important;">Barcode</td>
                  <td>Item</td>
                  <td style="width: 150px; !important;">Qty</td>
                  <td style="width: 250px; !important;">Price</td>
                  <td style="width: 12px; !important;"></td>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let siRec of si.salesInvoiceRecords,let i = index"
                    (click)="selectRow(i)" [class.active]="i === selectedSiRecordIndex">
                  <td>{{ siRec.barcode }}</td>
                  <td>{{ siRec.itemName }}</td>
                  <td>{{ siRec.quantity }}</td>
                  <td>{{ siRec.price | number }}</td>
                  <td>
                    <button class="btn btn-danger btn-sm" (click)="removeRow(i)">X</button>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>

            <!-- Mobile card view -->
            <div class="d-md-none">
              <div class="card mb-2" *ngFor="let siRec of si.salesInvoiceRecords,let i = index"
                   (click)="selectRow(i)" [ngClass]="{'border-primary': i === selectedSiRecordIndex}">
                <div class="card-body p-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="mb-1">{{ siRec.itemName }}</h6>
                      <small class="text-muted">{{ siRec.barcode }}</small>
                    </div>
                    <div class="text-right">
                      <div><strong>{{ siRec.price | number }}</strong></div>
                      <div>Qty: {{ siRec.quantity }}</div>
                    </div>
                  </div>
                  <div class="text-right mt-2">
                    <button class="btn btn-danger btn-sm" (click)="removeRow(i)">Remove</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment info section - improved for mobile -->
      <div class="row mt-3 mx-0">
        <!-- Customer section -->
        <div class="col-12 col-md-3 mb-2 px-1">
          <label>Customer</label>
          <div class="input-group">
            <input [(ngModel)]="keyCustomerSearch"
                   [typeahead]="customerSearchList"
                   (typeaheadLoading)="searchCustomers()"
                   (typeaheadOnSelect)="setSelectedCustomer($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   autocomplete="off"
                   placeholder="Search customer"
                   class="form-control" name="searchCustomer">
            <div class="input-group-append">
              <button class="btn btn-primary" (click)="newCustomer()" type="button">
                <i class="fa fa-plus"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Totals section -->
        <div class="col-12 col-md-9 px-1">
          <div class="row mx-0">
            <div class="col-4 mb-2 px-1">
              <label>Sub Total</label>
              <input class="form-control" [ngModel]="si.subTotal | number" readonly>
            </div>
            <div class="col-4 mb-2 px-1">
              <label>Discount</label>
              <input class="form-control" [(ngModel)]="si.totalDiscount" (ngModelChange)="calculateTotal()">
            </div>
            <div class="col-4 mb-2 px-1">
              <label>Total</label>
              <input class="form-control" [ngModel]="si.totalAmount | number" readonly>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment details section - improved for mobile -->
      <div class="row mt-2 mx-0">
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Reference</label>
          <input type="text" class="form-control" [(ngModel)]="si.reference" placeholder="Reference">
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Payment</label>
          <input type="number" class="form-control" [(ngModel)]="si.payment" placeholder="Amount paying" #payment
                 (ngModelChange)="calculateBalance()" (keyup.enter)="saveByEnter()">
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Payment Method</label>
          <select class="form-control" (change)="setPaymentMethod($event)" name="paymentMethodSelected"
                  [(ngModel)]="paymentMethodId" required #paymentMethodSelect="ngModel"
                  [class.is-invalid]="paymentMethodSelect.invalid && paymentMethodSelect.touched">
            <option>-Select-</option>
            <option *ngFor="let method of paymentMethods, let i = index"
                    [value]="method.id">
              {{ method.value }}
            </option>
          </select>
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Balance</label>
          <input class="form-control" [ngModel]="si.cashBalance | number" readonly>
        </div>
      </div>
    </div>
    <!-- Action buttons - optimized for mobile -->
    <div class="mt-3">
      <!-- Desktop view buttons -->
      <div class="d-none d-md-flex justify-content-between align-items-center">
        <!-- Left aligned buttons -->
        <div>
          <button type="button" class="btn btn-primary mr-2" [disabled]="!isProcessing"
                  (click)="isProcessing = !isProcessing" title="Reset Controls">
            <i class="fa fa-sync-alt"></i> Reset Controls
          </button>
          <button type="button" class="btn btn-dark"
                  (click)="loadFromMemory()" title="Load Memory">
            <i class="fa fa-memory"></i> Load Memory
          </button>
        </div>

        <!-- Right aligned buttons -->
        <div>
          <button *ngIf="null != si.cheque" class="btn btn-outline-primary btn-lg mr-2" mwlConfirmationPopover
                  (confirm)="removeCheque()" [popoverMessage]="'Do you want to remove the Cheque ?'"
                  title="Remove Cheque">
            <i class="fa fa-money-check"></i> {{ 'Cheque - ' + si.cheque.chequeNo + ' - ' + si.cheque.chequeAmount + '  X' }}
          </button>
          <button type="button" class="btn btn-danger btn-lg mr-2" mwlConfirmationPopover (confirm)="clear()" title="Clear">
            <i class="fa fa-trash"></i> Clear
          </button>
          <!-- Show Save and Save & Print buttons only when not in update mode -->
          <ng-container *ngIf="!isUpdateMode">
            <button type="button" class="btn btn-primary btn-lg mr-2" (click)="save(false)" [disabled]="isProcessing"
                    title="Save">
              <i class="fa fa-save"></i> Save
            </button>
            <button type="button" class="btn btn-primary btn-lg" (click)="save(true)" [disabled]="isProcessing"
                    title="Save & Print">
              <i class="fa fa-print"></i> Save & Print
            </button>
          </ng-container>
          <!-- Show Update button only when in update mode -->
          <ng-container *ngIf="isUpdateMode">
            <button type="button" class="btn btn-warning btn-lg" (click)="updateInvoice()" [disabled]="isProcessing"
                    title="Update Invoice">
              <i class="fa fa-edit"></i> Update Invoice
            </button>
          </ng-container>
        </div>
      </div>

      <!-- Mobile view buttons - compact layout -->
      <div class="d-flex d-md-none justify-content-between">
        <div>
          <button type="button" class="btn btn-primary btn-sm mx-1" [disabled]="!isProcessing"
                  (click)="isProcessing = !isProcessing" title="Reset Controls">
            <i class="fa fa-sync-alt"></i>
          </button>
          <button type="button" class="btn btn-dark btn-sm mx-1"
                  (click)="loadFromMemory()" title="Load Memory">
            <i class="fa fa-memory"></i>
          </button>
          <button *ngIf="null != si.cheque" class="btn btn-outline-primary btn-sm mx-1" mwlConfirmationPopover
                  (confirm)="removeCheque()" [popoverMessage]="'Do you want to remove the Cheque ?'"
                  title="Remove Cheque">
            <i class="fa fa-money-check"></i>
          </button>
        </div>
        <div>
          <button type="button" class="btn btn-danger btn-sm mx-1" mwlConfirmationPopover (confirm)="clear()"
                  title="Clear">
            <i class="fa fa-trash"></i>
          </button>
          <!-- Show Save and Save & Print buttons only when not in update mode -->
          <ng-container *ngIf="!isUpdateMode">
            <button type="button" class="btn btn-primary btn-sm mx-1" (click)="save(false)" [disabled]="isProcessing"
                    title="Save">
              <i class="fa fa-save"></i>
            </button>
            <button type="button" class="btn btn-primary btn-sm mx-1" (click)="save(true)" [disabled]="isProcessing"
                    title="Save & Print">
              <i class="fa fa-print"></i>
            </button>
          </ng-container>
          <!-- Show Update button only when in update mode -->
          <ng-container *ngIf="isUpdateMode">
            <button type="button" class="btn btn-warning btn-sm mx-1" (click)="updateInvoice()" [disabled]="isProcessing"
                    title="Update Invoice">
              <i class="fa fa-edit"></i>
            </button>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #tempMultiplePrice>
  <div class="modal-header">
    <h5 class="modal-title">Select Price</h5>
    <button type="button" class="close" aria-label="Close" (click)="hidePricesModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body text-center">
    <div class="form-group col-md-12">
      <ul class="list-group">
        <li *ngFor="let pr of prices; let i = index" class="list-group-item list-group-item-action"
            (click)="setPrice(pr[1], pr[0])">
          {{ pr[1] + " - " + pr[0] }}
        </li>
      </ul>
    </div>
  </div>
</ng-template>




