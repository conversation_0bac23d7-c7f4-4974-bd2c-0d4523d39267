<div class="card font-helvetica">
  <div class="card-header">
    <strong class="theme-color">{{ editMode ? 'Edit' : 'View' }} Sales Invoice</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-3 pr-0">
        <label class="theme-color">Invoice No</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.invoiceNo ? salesInvoice.invoiceNo : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Date</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.date | date : 'short': '+530' ? salesInvoice.date : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Customer Name</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.customerName ? salesInvoice.customerName : 'N/A' }}
          <span *ngIf="salesInvoice.routeName" class="text-muted small d-block">
            Route: {{ salesInvoice.routeName }}
          </span>
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Due Date</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.dueDate | date : 'short':'+530' ? salesInvoice.dueDate : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Payment Method</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.paymentMethod ? salesInvoice.paymentMethod.value : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Status</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.status ? salesInvoice.status.value : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Total Amount</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.totalAmount ? salesInvoice.totalAmount : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Total Discount</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.totalDiscount ? salesInvoice.totalDiscount : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0" *ngIf="cashPayment">
        <label class="theme-color small">Card / Voucher / Cheque No</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0" *ngIf="cashPayment">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.cardOrVoucherNo ? salesInvoice.cardOrVoucherNo : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0" *ngIf="cashPayment">
        <label class="theme-color">Cash Amount</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0" *ngIf="cashPayment">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.cashAmount ? salesInvoice.cashAmount : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0" *ngIf="cashPayment">
        <label class="theme-color">Cashless Amount</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0" *ngIf="cashPayment">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.cashlessAmount ? salesInvoice.cashlessAmount : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Sub Total</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.subTotal ? salesInvoice.subTotal : 'N/A' }}
        </p>
      </div>
      <div class="col-md-3 pr-0">
        <label class="theme-color">Balance</label>
        <label class="theme-color text-right">:</label>
      </div>
      <div class="col-md-3 pl-0">
        <p class="p-1 theme-color-border-bottom">
          {{ salesInvoice.balance ? salesInvoice.balance : 'N/A' }}
        </p>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col-md-3 pr-0">
        <label class="theme-color">Sales Invoice Records</label>
        <label class="theme-color text-right">:</label>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <table class="w-100">
          <thead align="center">
          <th class="p-2 theme-color">Barcode</th>
          <th class="p-2 theme-color">Item Name</th>
          <th class="p-2 theme-color">Quantity</th>
          <th class="p-2 theme-color">Unit Price</th>
          <th class="p-2 theme-color">item Cost</th>
          <th class="p-2 theme-color">Price</th>
          </thead>
          <tbody align="center">
          <tr *ngFor="let invoiceRecord of salesInvoice.salesInvoiceRecords">
            <td class="border-0 pt-1 theme-color">{{ invoiceRecord.barcode }}</td>
            <td class="border-0 pt-1 theme-color">{{ invoiceRecord.itemName }}</td>
            <td class="border-0 pt-1 theme-color">{{ invoiceRecord.quantity }}</td>
            <td class="border-0 pt-1 theme-color">{{ invoiceRecord.unitPrice | number : '1.2-2' }}</td>
            <td class="border-0 pt-1 theme-color">{{ invoiceRecord.itemCost | number : '1.2-2' }}</td>
            <td class="border-0 pt-1 theme-color">{{ invoiceRecord.price | number : '1.2-2' }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="row mt-4 text-right">
      <div class="col-md-12">
        <!-- Show Update button only in edit mode -->
        <button *ngIf="editMode" class="btn btn-primary mr-2" (click)="updateInvoice()">Update</button>
        <button class="btn btn-primary mr-2" (click)="print()">Print</button>
        <button class="btn theme-color-bg text-white" (click)="close()">Close</button>
      </div>
    </div>
  </div>
</div>
