/* Modal specific styles */
.p-3 {
  padding: 1rem !important;
}

/* Responsive pagination styles */
.responsive-pagination ::ng-deep .page-link {
  padding: 0.25rem 0.5rem;
}

/* Responsive adjustments for modal and pagination */
@media (max-width: 768px) {
  .p-3 {
    padding: 0.75rem !important;
  }

  .component-title {
    font-size: 1.25rem !important;
  }

  .table-responsive {
    max-height: 50vh;
    overflow-y: auto;
  }

  /* Make pagination more compact on mobile */
  .responsive-pagination ::ng-deep .page-link {
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
  }

  /* Hide some pagination elements on very small screens */
  @media (max-width: 400px) {
    .responsive-pagination ::ng-deep .page-item:not(.active):not(:first-child):not(:last-child) .page-link {
      padding: 0.2rem 0.3rem;
      font-size: 0.75rem;
    }
  }
}
