<div class="container-fluid px-0" [ngClass]="{'p-3': isModal}">
  <!-- Modal close button -->
  <div class="d-flex justify-content-between align-items-center mb-3" *ngIf="isModal">
    <h2 class="component-title mb-0">{{ 'CUSTOMERS.MANAGE_CUSTOMERS' | translate }}</h2>
    <button type="button" class="btn btn-sm btn-outline-secondary" (click)="modalRef.hide()">
      <i class="fa fa-times"></i>
    </button>
  </div>

  <!-- Regular title when not in modal -->
  <h2 class="component-title" *ngIf="!isModal">{{ 'CUSTOMERS.MANAGE_CUSTOMERS' | translate }}</h2>
  <div class="row g-3 mb-3">
    <div class="form-group input-focus col-12 col-sm-6 col-md-4">
      <label class="form-label d-block d-md-none">{{ 'CUSTOMERS.NAME' | translate }}</label>
      <input [(ngModel)]="keyName"
             [typeahead]="customerSearchList"
             (typeaheadLoading)="loadCustomer()"
             (typeaheadOnSelect)="setFilteredCustomer($event)"
             [typeaheadOptionsLimit]="7"
             typeaheadWaitMs="1000"
             typeaheadOptionField="name"
             autocomplete="off"
             placeholder="{{ 'COMMON.SEARCH' | translate }} {{ 'CUSTOMERS.NAME' | translate }}"
             class="form-control" name="category">
    </div>
    <div class="form-group input-focus col-12 col-sm-6 col-md-3">
      <label class="form-label d-block d-md-none">{{ 'CUSTOMERS.NIC' | translate }}</label>
      <input [(ngModel)]="keyNic"
             [typeahead]="customerSearchList"
             (typeaheadLoading)="loadCustomerByNic()"
             (typeaheadOnSelect)="setFilteredCustomer($event)"
             [typeaheadOptionsLimit]="7"
             typeaheadWaitMs="1000"
             typeaheadOptionField="nicBr"
             autocomplete="off"
             placeholder="{{ 'COMMON.SEARCH' | translate }} {{ 'CUSTOMERS.NIC' | translate }}"
             class="form-control" name="nic">
    </div>
    <div class="form-group input-focus col-12 col-sm-6 col-md-3">
      <label class="form-label d-block d-md-none">{{ 'CUSTOMERS.PHONE' | translate }}</label>
      <input [(ngModel)]="keyTp"
             [typeahead]="customerSearchList"
             (typeaheadLoading)="loadCustomerByTp()"
             (typeaheadOnSelect)="setFilteredCustomer($event)"
             [typeaheadOptionsLimit]="7"
             typeaheadWaitMs="1000"
             typeaheadOptionField="telephone1"
             autocomplete="off"
             placeholder="{{ 'COMMON.SEARCH' | translate }} {{ 'CUSTOMERS.PHONE' | translate }}"
             class="form-control" name="tp">
    </div>
    <div class="form-group col-12 col-sm-6 col-md-2 d-flex align-items-end">
      <div class="form-check checkbox">
        <input class="form-check-input" id="check3" name="check3" type="checkbox"
               (change)="searchActiveResult($event)"
               [value]="active"
               [(ngModel)]="active">
        <label class="form-check-label ms-2" for="check3">{{ 'CUSTOMERS.ACTIVE' | translate }}</label>
      </div>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-bordered table-striped table-hover">
      <thead class="table-light text-center">
      <tr>
        <th scope="col">{{ 'CUSTOMERS.NAME' | translate }}</th>
        <th scope="col" class="d-none d-md-table-cell">{{ 'CUSTOMERS.NIC' | translate }}</th>
        <th scope="col" class="d-none d-md-table-cell">{{ 'CUSTOMERS.PHONE' | translate }}</th>
        <th scope="col" class="d-none d-md-table-cell">{{ 'CUSTOMERS.ADDRESS' | translate }}</th>
        <th scope="col" class="d-none d-md-table-cell">Route</th>
        <th scope="col">{{ 'SALES.BALANCE' | translate }}</th>
      </tr>
      </thead>
      <tbody>
      <tr class="text-center" *ngFor="let customer of customers,let i = index"
          (click)="customerDetail(customer,i)" (dblclick)="openModal(false)"
          [class.active]="i === selectedRow">
        <td>{{ customer.name }}</td>
        <td class="d-none d-md-table-cell">{{ null != customer.nicBr ? customer.nicBr : 'N/A' }}</td>
        <td
          class="d-none d-md-table-cell">{{ customer.telephone1 + (null != customer.telephone2 ? ',' + customer.telephone2 : '') }}
        </td>
        <td class="d-none d-md-table-cell">{{ customer.address }}</td>
        <td class="d-none d-md-table-cell">{{ customer.routeName || ('ADDITIONAL.NOT_ASSIGNED' | translate) }}</td>
        <td>{{ customer.balance | number: '1.2-2' }}</td>
      </tr>
      </tbody>
    </table>
  </div>

  <!-- Mobile view for selected customer details -->
  <div class="d-md-none mt-3 mb-3"
       *ngIf="selectedRow !== undefined && customers && customers.length > 0 && selectedRow !== null">
    <div class="card bg-light">
      <div class="card-body">
        <h5 class="card-title">{{ 'ADDITIONAL.SELECTED_CUSTOMER_DETAILS' | translate }}</h5>
        <div class="row g-2">
          <div class="col-6">
            <p class="mb-1 fw-bold">{{ 'CUSTOMERS.NIC' | translate }}:</p>
            <p>{{ customers[selectedRow].nicBr }}</p>
          </div>
          <div class="col-6">
            <p class="mb-1 fw-bold">{{ 'CUSTOMERS.PHONE' | translate }}:</p>
            <p>{{ customers[selectedRow].telephone1 }}</p>
          </div>
          <div class="col-12">
            <p class="mb-1 fw-bold">{{ 'CUSTOMERS.ADDRESS' | translate }}:</p>
            <p>{{ customers[selectedRow].address }}</p>
          </div>
          <div class="col-6">
            <p class="mb-1 fw-bold">Route:</p>
            <p>{{ customers[selectedRow].routeName || ('ADDITIONAL.NOT_ASSIGNED' | translate) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row d-flex">
    <div class="col-12">
      <pagination class="pagination-sm justify-content-center responsive-pagination"
                  [totalItems]="collectionSize"
                  [boundaryLinks]="true"
                  [maxSize]="maxSize"
                  [(ngModel)]="page"
                  (pageChanged)="pageChanged($event)">
      </pagination>
    </div>
  </div>
  <div class="row mt-3">
    <div class="col-12 text-right">
      <button class="btn btn-theme mr-2" type="button" (click)="openModal(true)"
              [disabled]="selectedRow===null">
        <i class="fa fa-eye"></i> {{ 'COMMON.VIEW' | translate }}
      </button>
      <button class="btn btn-warning mr-2" type="button" (click)="openModal(false)"
              [disabled]="selectedRow===null">
        <i class="fa fa-edit"></i> {{ 'CUSTOMERS.EDIT_CUSTOMER' | translate }}
      </button>
      <button class="btn btn-theme" type="button" (click)="setSelectedCustomer()"
              [hidden]="disableSetCustomer">{{ 'ADDITIONAL.SET_SELECTED' | translate }}
      </button>
    </div>
  </div>
</div>





