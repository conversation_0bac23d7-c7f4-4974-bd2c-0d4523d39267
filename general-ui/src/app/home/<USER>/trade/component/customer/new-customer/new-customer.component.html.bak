<div class="container-fluid px-0" [ngClass]="{'p-3': isModal}">
  <!-- Modal close button -->
  <div class="d-flex justify-content-between align-items-center mb-3" *ngIf="isModal">
    <h2 class="component-title mb-0">{{ isEdit ? ('CUSTOMERS.EDIT_CUSTOMER' | translate) : ('CUSTOMERS.NEW_CUSTOMER' | translate) }}</h2>
    <button type="button" class="btn btn-sm btn-outline-secondary" (click)="modalRef.hide()">
      <i class="fa fa-times"></i>
    </button>
  </div>

  <!-- Regular title when not in modal -->
  <h2 class="component-title" *ngIf="!isModal">{{ isEdit ? ('CUSTOMERS.EDIT_CUSTOMER' | translate) : ('CUSTOMERS.NEW_CUSTOMER' | translate) }}</h2>
    <form #newPersonForm="ngForm" (ngSubmit)="savePerson(newPersonForm);">
      <div class="row">
        <div class="form-group col-md-6">
          <label>{{ 'CUSTOMERS.NIC' | translate }}</label>
          <input #nic="ngModel" type="text" name="nic" id="nic"
                 [(ngModel)]="customer.nicBr"
                 [class.is-invalid]="nic.invalid && nic.touched"
                 [typeahead]="customerSearchList"
                 (typeaheadLoading)="loadCustomerByNic()"
                 (typeaheadOnSelect)="setSelectedCustomer($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="nicBr"
                 autocomplete="off"
                 placeholder="{{ 'CUSTOMERS.SEARCH_NIC' | translate }}"
                 pattern="([0-9]{9}[x|X|v|V]|[0-9]{12})$"
                 class="form-control" placeholder="Enter NIC No" [readonly]="isEdit">
          <small class="text-danger" [class.d-none]="nic.valid || nic.untouched">{{ 'CUSTOMERS.NIC_REQUIRED' | translate }}
          </small>

          <small *ngIf="nicAvailability" [class.is-none]="true" class="text-danger">{{ 'CUSTOMERS.NIC_ALREADY_USED' | translate }}
          </small>
        </div>  <div class="form-group col-md-2">
          <label>{{ 'CUSTOMERS.SALUTATION' | translate }} </label>
          <select type="text" #salutation="ngModel"
                  [class.is-invalid]="salutation.invalid && salutation.touched"
                  class="form-control" id="salutation" [(ngModel)]="customer.salutation" name="salutation">
            <option *ngFor="let sal of salutations" [value]="sal">{{sal}}</option>
          </select>
        </div>  <div class="form-group col-md-4">
          <label>{{ 'CUSTOMERS.NAME' | translate }} </label>
          <input type="text" required #CuName="ngModel" [class.is-invalid]="CuName.invalid && CuName.touched"
                 class="form-control" id="CuName" [(ngModel)]="customer.name" name="CuName"
                 placeholder="{{ 'CUSTOMERS.ENTER_NAME' | translate }}">
          <small class="text-danger" [class.d-none]="CuName.valid || CuName.untouched">{{ 'CUSTOMERS.NAME_REQUIRED' | translate }}
          </small>
        </div>  <div class="form-group col-md-6">
          <label>{{ 'CUSTOMERS.ADDRESS' | translate }}</label>
          <input type="text" #address1="ngModel" class="form-control" id="address1"
                 name="address1" placeholder="{{ 'CUSTOMERS.ENTER_ADDRESS' | translate }}"
                 [class.is-invalid]="address1.invalid && address1.touched"
                 [(ngModel)]="customer.address" [disabled]="isEdit">
          <small class="text-danger" [class.d-none]="address1.valid || address1.untouched">{{ 'CUSTOMERS.ADDRESS_REQUIRED' | translate }}
          </small>
        </div>  <div class="form-group col-md-6">
          <label>{{ 'CUSTOMERS.EMAIL' | translate }} </label>
          <input type="text" #email="ngModel" class="form-control" id="email" placeholder="{{ 'CUSTOMERS.ENTER_EMAIL' | translate }}"
                 name="email" [(ngModel)]="customer.email"
                 pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"
                 [class.is-invalid]="email.invalid " [disabled]="isEdit">
          <div *ngIf="email.errors && (email.invalid && email.touched)">
            <small class="text-danger" [class.d-none]="email.valid ">{{ 'CUSTOMERS.EMAIL_REQUIRED' | translate }}
            </small>
          </div>
        </div>  <div class="form-group col-md-6">
          <label>{{ 'CUSTOMERS.PHONE' | translate }} 1</label>
          <input required #telephone1="ngModel" [class.is-invalid]="telephone1.invalid && telephone1.touched"
                 type="text" class="form-control" id="telephone1" placeholder="{{ 'CUSTOMERS.ENTER_PHONE_1' | translate }}"
                 name="telephone1"
                 pattern="^\d{10}$"
                 [(ngModel)]="customer.telephone1" [disabled]="isEdit">
          <small class="text-danger" [class.d-none]="telephone1.valid || telephone1.untouched">{{ 'CUSTOMERS.PHONE_1_REQUIRED' | translate }}
          </small>
        </div>  <div class="form-group col-md-6">
          <label>{{ 'CUSTOMERS.PHONE' | translate }} 2 </label>
          <input type="text" class="form-control" id="telephone2" placeholder="{{ 'CUSTOMERS.ENTER_PHONE_2' | translate }}"
                 name="telephone2"
                 pattern="^\d{10}$"
                 [(ngModel)]="customer.telephone2" [disabled]="isEdit">
        </div>  <div class="form-group col-md-6">
          <label>{{ 'CUSTOMERS.ROUTE' | translate }}</label>
          <div class="input-group">
            <input [(ngModel)]="routeSearchTerm"
                   [typeahead]="routes"
                   (typeaheadLoading)="loadRoutes()"
                   (typeaheadOnSelect)="selectRoute($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadWaitMs="500"
                   typeaheadOptionField="name"
                   placeholder="{{ 'CUSTOMERS.SELECT_ROUTE' | translate }}"
                   autocomplete="off"
                   class="form-control"
                   name="route"
                   [disabled]="isEdit">
          </div>
          <small *ngIf="customer.routeNo" class="text-info">
            {{ 'CUSTOMERS.SELECTED_ROUTE' | translate }}: {{customer.routeName}})
          </small>
        </div>  <div class="col-md-6">
          <div class="form-check checkbox col-md-6 mt-4">
            <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                   [(ngModel)]="customer.active">
            <label class="form-check-label mr-2" for="check3">{{ 'CUSTOMERS.ACTIVE' | translate }}</label>
          </div>
        </div>
      </div>  <div class="row text-right">
        <div class="col-md-12">
          <button type="submit" class="btn btn-theme mr-4"
                  [disabled]="!newPersonForm.form.valid || (!isEdit && nicAvailability)">
            {{ isEdit ? ('CUSTOMERS.UPDATE' | translate) : ('CUSTOMERS.SAVE' | translate) }}
          </button>
          <button type="button" class="btn btn-warning" (click)="clear();">
            {{ 'CUSTOMERS.CLEAR' | translate }}
          </button>
        </div>
      </div>
    </form>
</div>


