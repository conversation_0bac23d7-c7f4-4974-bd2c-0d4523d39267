/* Styles for 76mm invoice */
@media print {
  @page {
    size: 76mm auto;
    margin: 0;
  }
  
  body {
    margin: 0;
    padding: 0;
  }

  /* Hide print button when printing */
  .btn {
    display: none !important;
  }

  /* Ensure proper font rendering for print */
  #print-section {
    font-family: 'Courier New', monospace !important;
    font-size: 12px !important;
    line-height: 1.2 !important;
  }

  /* Optimize spacing for 76mm width */
  #print-section table {
    font-size: 11px !important;
  }

  #print-section .item-row {
    font-size: 12px !important;
    margin: 0.1rem !important;
  }

  /* Ensure proper column alignment for narrow width */
  #print-section .item-number {
    width: 8% !important;
  }

  #print-section .item-name {
    width: 92% !important;
  }

  #print-section .item-qty {
    width: 14% !important;
  }

  #print-section .item-list-price {
    width: 26% !important;
  }

  #print-section .item-sale-price {
    width: 26% !important;
  }

  #print-section .item-amount {
    width: 34% !important;
  }

  /* Compact totals section */
  #print-section .totals-section {
    font-size: 13px !important;
  }

  #print-section .totals-section li {
    margin-bottom: 3px !important;
  }

  /* Footer adjustments */
  #print-section .footer-text {
    font-size: 0.4rem !important;
  }

  #print-section .thank-you {
    font-size: 0.5rem !important;
    margin-bottom: 3px !important;
  }
}

/* Screen styles for preview */
@media screen {
  #print-section {
    border: 1px solid #ddd;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    background: white;
    margin: 20px auto;
    max-width: 76mm;
  }

  /* Responsive adjustments for mobile preview */
  @media (max-width: 768px) {
    #print-section {
      margin: 10px;
      max-width: 100%;
      width: auto !important;
    }
  }
}

/* General optimizations for 76mm width */
.compact-header {
  font-size: 16px !important;
  margin-bottom: 6px !important;
}

.compact-address {
  font-size: 12px !important;
  margin-bottom: 6px !important;
}

.compact-table {
  font-size: 11px !important;
}

.compact-item {
  font-size: 13px !important;
  margin: 0.15rem !important;
}

.compact-totals {
  font-size: 13px !important;
}

.compact-footer {
  font-size: 0.4rem !important;
}
