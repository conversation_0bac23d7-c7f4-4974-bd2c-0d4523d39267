<div class="card"> <!-- Removed font-helvetica -->
  <div class="card-header">
    <strong>View Purchase Invoice</strong> <!-- Removed theme-color, rely on default or inherited styles -->
  </div>
  <div class="card-body">
    <!-- Invoice Details using Definition List -->
    <div class="row mb-3"> <!-- Added margin-bottom -->
      <!-- Left Column -->
      <div class="col-md-6">
        <dl class="row mb-0"> <!-- Horizontal Definition List -->
          <dt class="col-sm-5">Invoice No:</dt> <!-- Use dt for term -->
          <dd class="col-sm-7">{{ purchaseInvoice.invoiceNo ? purchaseInvoice.invoiceNo : 'N/A' }}</dd> <!-- Use dd for definition -->

          <dt class="col-sm-5">Supplier Name:</dt> <!-- Changed from Customer Name based on context -->
          <dd class="col-sm-7">{{ purchaseInvoice.supplier?.name ? purchaseInvoice.supplier.name : 'N/A' }}</dd> <!-- Optional chaining -->

          <dt class="col-sm-5">Payment Method:</dt>
          <dd class="col-sm-7">{{ purchaseInvoice.paymentMethod?.value ? purchaseInvoice.paymentMethod.value : 'N/A' }}</dd>

          <dt class="col-sm-5">Total Amount:</dt>
          <dd class="col-sm-7">{{ purchaseInvoice.totalAmount ? (purchaseInvoice.totalAmount | number : '1.2-2') : 'N/A' }}</dd> <!-- Pipe inside ternary -->
        </dl>
      </div>

      <!-- Right Column -->
      <div class="col-md-6">
        <dl class="row mb-0">
          <dt class="col-sm-5">Date:</dt>
          <!-- Corrected Date Pipe Logic -->
          <dd class="col-sm-7">{{ purchaseInvoice.date ? (purchaseInvoice.date | date:'short':'+0530') : 'N/A' }}</dd>

          <dt class="col-sm-5">Due Date:</dt>
          <dd class="col-sm-7">{{ purchaseInvoice.dueDate ? (purchaseInvoice.dueDate | date:'short':'+0530') : 'N/A' }}</dd>

          <dt class="col-sm-5">Status:</dt>
          <dd class="col-sm-7">{{ purchaseInvoice.status?.value ? purchaseInvoice.status.value : 'N/A' }}</dd>

          <dt class="col-sm-5">Balance:</dt>
          <dd class="col-sm-7">{{ purchaseInvoice.balance ? (purchaseInvoice.balance | number : '1.2-2') : 'N/A' }}</dd>

          <!-- Sub Total (Keep commented if not needed) -->
          <!--
          <dt class="col-sm-5">Sub Total:</dt>
          <dd class="col-sm-7">{{ purchaseInvoice.subTotal ? (purchaseInvoice.subTotal | number : '1.2-2') : 'N/A' }}</dd>
           -->
        </dl>
      </div>
    </div>

    <!-- Invoice Records Section -->
    <div class="row mb-2 mt-3"> <!-- Added margin top/bottom -->
      <div class="col-12">
        <!-- Removed extra label structure for the heading -->
        <h5>Invoice Records</h5>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <!-- Added table-responsive for smaller screens -->
        <div class="table-responsive">
          <!-- Added basic Bootstrap table classes -->
          <table class="table table-sm table-bordered w-100">
            <!-- Added thead, tbody and Bootstrap text alignment -->
            <thead class="text-center table-light"> <!-- Added table-light for header contrast -->
            <tr> <!-- Added table row for headers -->
              <th class="p-2">Barcode</th>
              <th class="p-2">Item Name</th>
              <th class="p-2">Quantity</th>
              <th class="p-2">Unit Price</th>
              <th class="p-2">Item Cost</th>
              <th class="p-2">Price</th>
            </tr>
            </thead>
            <tbody class="text-center">
            <tr *ngIf="!purchaseInvoice.purchaseInvoiceRecords || purchaseInvoice.purchaseInvoiceRecords.length === 0">
              <td colspan="6" class="text-muted fst-italic py-3">No invoice records found.</td> <!-- Added empty state row -->
            </tr>
            <tr *ngFor="let invoiceRecord of purchaseInvoice.purchaseInvoiceRecords">
              <!-- Removed border-0 and theme-color, rely on table classes -->
              <td class="pt-1">{{ invoiceRecord.item?.barcode ? invoiceRecord.item.barcode : 'N/A' }}</td>
              <td class="pt-1 text-start">{{ invoiceRecord.itemName ? invoiceRecord.itemName : 'N/A' }}</td> <!-- Left align item name -->
              <td class="pt-1">{{ invoiceRecord.quantity ? invoiceRecord.quantity : 'N/A' }}</td>
              <td class="pt-1 text-end">{{ invoiceRecord.sellingPrice ? (invoiceRecord.sellingPrice | number : '1.2-2') : 'N/A' }}</td> <!-- Right align currency -->
              <td class="pt-1 text-end">{{ invoiceRecord.itemCost ? (invoiceRecord.itemCost | number : '1.2-2') : 'N/A' }}</td> <!-- Right align currency -->
              <td class="pt-1 text-end">{{ invoiceRecord.totalAmount ? (invoiceRecord.totalAmount | number : '1.2-2') : 'N/A' }}</td> <!-- Right align currency -->
            </tr>
            </tbody>
          </table>
        </div>
      </div>
  </div>
