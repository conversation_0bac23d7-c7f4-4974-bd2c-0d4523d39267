<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- Header with close button when in modal mode -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">Manage Sales Invoices</h2>
    <button *ngIf="isModal" type="button" class="btn btn-sm btn-outline-secondary" (click)="closeModal()">
      <i class="fa fa-times"></i>
    </button>
  </div>
  <!-- Filters -->
  <div class="row g-2 mb-1">
    <!-- Customer Filter -->
    <div class="col-12 col-sm-6 col-md-3 mb-1">
      <label class="form-label d-block d-md-none">Customer</label>
      <div class="form-group">
        <input
          type="text"
          class="form-control"
          [(ngModel)]="keyCustomerSearch"
          [typeahead]="customerSearchList"
          (typeaheadLoading)="searchCustomers()"
          (typeaheadOnSelect)="setSelectedCustomer($event)"
          [typeaheadOptionsLimit]="7"
          typeaheadWaitMs="500"
          typeaheadOptionField="name"
          autocomplete="off"
          placeholder="Customer"
        >
      </div>
    </div>

    <!-- Invoice Number Filter -->
    <div class="col-12 col-sm-6 col-md-2 mb-1">
      <label class="form-label d-block d-md-none">Invoice No</label>
      <div class="form-group">
        <input
          type="text"
          class="form-control"
          [(ngModel)]="keyInvoiceNo"
          placeholder="Invoice No"
          autocomplete="off"
        >
      </div>
    </div>

    <!-- From Date -->
    <div class="col-6 col-sm-6 col-md-2 mb-1">
      <label class="form-label d-block d-md-none">From Date</label>
      <div class="form-group">
        <input
          type="text"
          class="form-control"
          [(ngModel)]="startDate"
          bsDatepicker
          [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
          placeholder="From Date">
      </div>
    </div>

    <!-- To Date -->
    <div class="col-6 col-sm-6 col-md-2 mb-1">
      <label class="form-label d-block d-md-none">To Date</label>
      <div class="form-group">
        <input
          type="text"
          class="form-control"
          [(ngModel)]="endDate"
          bsDatepicker
          [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
          placeholder="To Date">
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="col-12 col-sm-12 col-md-3 d-flex">

      <div class="form-group mr-1 flex-grow-1">
        <button class="btn btn-primary btn-block" (click)="openMoreFiltersModal()" title="More Filters">
          <i class="fa fa-filter"></i>
        </button>
      </div>

      <div class="form-group mr-1 flex-grow-1">
        <button class="btn btn-outline-secondary btn-block" (click)="clearFilters()" title="Clear Filters">
          <i class="fa fa-times"></i>
        </button>
      </div>

      <div class="form-group flex-grow-1">
        <button class="btn btn-primary btn-block" (click)="search()" title="Search">
          <i class="fa fa-search"></i>
        </button>
      </div>

    </div>

  </div>

  <!-- Active Additional Filters Display -->
  <div class="row mt-1" *ngIf="selectedCashier || selectedCashierUser || selectedRoute || keyInvoiceNo || (startDate && endDate && isTodaySelected() === false)">
    <div class="col-12">
      <div class="alert alert-info py-2">
        <strong>Active Filters:</strong>
        <span *ngIf="keyInvoiceNo" class="badge badge-primary ml-2 mr-2">
            Invoice No: {{ keyInvoiceNo }}
          </span>
        <span *ngIf="selectedCashier" class="badge badge-primary ml-2 mr-2">
            Cash Drawer: {{ selectedCashier.drawerNo }} - {{ selectedCashier.userName || 'No User Assigned' }}
          </span>
        <span *ngIf="selectedCashierUser" class="badge badge-primary ml-2 mr-2">
            User: {{ selectedCashierUser.username }}
          </span>
        <span *ngIf="selectedRoute" class="badge badge-primary ml-2 mr-2">
            Route: {{ selectedRoute.name }} ({{ selectedRoute.from }} - {{ selectedRoute.to }})
          </span>

        <span *ngIf="startDate && endDate && isTodaySelected() === false" class="badge badge-primary ml-2 mr-2">
            Date Range: {{ formatDateForDisplay(startDate) }} to {{ formatDateForDisplay(endDate) }}
          </span>
      </div>
    </div>
  </div>

  <!-- Table settings controls -->
  <div class="d-flex justify-content-between align-items-center mb-2">
    <div>
      <button type="button" class="btn btn-sm btn-outline-secondary" (click)="toggleColumnSelector()">
        <i class="fa fa-columns"></i> Customize Columns
      </button>
    </div>
    <app-page-size-selector [pageSize]="pageSize" (pageSizeChange)="onPageSizeChange($event)"></app-page-size-selector>
  </div>

  <!-- Column selector -->
  <div *ngIf="showColumnSelector" class="mb-3 p-3 border rounded bg-light">
    <app-column-selector [columns]="tableColumns" (columnsChange)="onColumnsChange($event)"></app-column-selector>
  </div>

  <div class="row mt-2">
    <div class="col-12 table-responsive">
      <table class="table table-striped table-hover">
        <thead class="table-light text-center">
        <tr>
          <!-- Dynamic columns based on user selection -->
          <th scope="col" *ngFor="let column of visibleColumns" [ngClass]="column.class">
            {{ column.header }}
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let si of sis,let i = index"
            (click)="selectSi(si,i)" (dblclick)="viewSi()"
            [class.active]="i === selectedRow" class="text-center">
          <!-- Dynamic columns based on user selection -->
          <ng-container *ngFor="let column of visibleColumns">
            <!-- Invoice No column -->
            <td *ngIf="column.key === 'invoiceNo'" [ngClass]="column.class">{{ si.invoiceNo }}</td>

            <!-- Customer column (without route) -->
            <td *ngIf="column.key === 'customerName'" [ngClass]="column.class">{{ si.customerName }}</td>

            <!-- Date column -->
            <td *ngIf="column.key === 'date'" [ngClass]="column.class">{{ si.date | date:'short': '+530' }}</td>

            <!-- Amount column -->
            <td *ngIf="column.key === 'totalAmount'" [ngClass]="column.class">{{ si.totalAmount | number : '1.2-2' }}</td>

            <!-- Payment column -->
            <td *ngIf="column.key === 'payment'" [ngClass]="column.class">{{ si.payment | number : '1.2-2' }}</td>

            <!-- Balance column -->
            <td *ngIf="column.key === 'balance'" [ngClass]="column.class">{{ si.balance | number: '1.2-2' }}</td>

            <!-- Payment Method column -->
            <td *ngIf="column.key === 'paymentMethod'" [ngClass]="column.class">{{ si.paymentMethod ? si.paymentMethod.value : 'N/A' }}</td>

            <!-- Cash Drawer column -->
            <td *ngIf="column.key === 'drawerNo'" [ngClass]="column.class">{{ si.drawerNo || 'N/A' }}</td>

            <!-- Cashier column -->
            <td *ngIf="column.key === 'cashierUserName'" [ngClass]="column.class">{{ si.cashierUserName || si.createdBy || 'N/A' }}</td>

            <!-- Route column -->
            <td *ngIf="column.key === 'routeName'" [ngClass]="column.class">{{ si.routeName || 'N/A' }}</td>

            <!-- Status column -->
            <td *ngIf="column.key === 'status'" [ngClass]="column.class">
              <span [ngClass]="getStatusClass(si.status)">
                {{ si.status ? si.status.value : 'N/A' }}
              </span>
            </td>
          </ng-container>
        </tr>
        <tr *ngIf="!sis || sis.length === 0">
          <td [attr.colspan]="visibleColumns.length" class="text-center">No invoices found</td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="row d-flex align-items-center">
    <!-- Bill count display on the left -->
    <div class="col-md-6 text-left">
      <small class="text-muted">
        Showing {{ sis?.length || 0 }} of {{ collectionSize || 0 }} invoices
        <span *ngIf="totalAmount > 0"> | Total Amount: <strong class="text-success">{{ totalAmount | number:'1.2-2' }}</strong></span>
      </small>
    </div>
    <!-- Pagination on the right -->
    <div class="col-md-6">
      <pagination class="pagination-sm justify-content-center"
                  [totalItems]="collectionSize"
                  [itemsPerPage]="pageSize"
                  [maxSize]="maxSize"
                  [boundaryLinks]="true"
                  [(ngModel)]="page"
                  (pageChanged)="pageChanged($event)">
      </pagination>
    </div>
  </div>
  <!-- Mobile view for selected invoice details -->
  <div class="d-md-none mt-3 mb-3" *ngIf="selectedRow !== undefined && sis && sis.length > 0">
    <div class="card bg-light">
      <div class="card-body">
        <h5 class="card-title">Selected Invoice Details</h5>
        <div class="row g-2">
          <div class="col-6">
            <p class="mb-1 fw-bold">Date:</p>
            <p>{{ sis[selectedRow].date | date:'short': '+530' }}</p>
          </div>
          <div class="col-6">
            <p class="mb-1 fw-bold">Balance:</p>
            <p>{{ sis[selectedRow].balance | number: '1.2-2' }}</p>
          </div>
          <div class="col-6">
            <p class="mb-1 fw-bold">Payment Method:</p>
            <p>{{ sis[selectedRow].paymentMethod ? sis[selectedRow].paymentMethod.value : 'N/A' }}</p>
          </div>
          <div class="col-6">
            <p class="mb-1 fw-bold">Created By:</p>
            <p>{{ sis[selectedRow].cashierUserName || sis[selectedRow].createdBy || 'N/A' }}</p>
          </div>

        </div>
      </div>
    </div>
  </div>

  <!-- Action buttons - improved for mobile -->
  <div class="mt-3 mb-1">
    <div class="d-flex flex-wrap justify-content-end">
      <!-- Edit button - only enabled for admin/manager and invoices within 30 days -->
      <button type="button" class="btn btn-danger m-1"
              [disabled]="!selectedSi || !isInvoiceEditable(selectedSi) || !isAdminOrManager"
              (click)="editInvoice()"
              title="Edit invoice (only available for managers and administrators, and invoices within 30 days)">
        <i class="fa fa-pencil-alt mr-1 d-none d-sm-inline"></i> Edit
      </button>
      <!-- Cancel button - only enabled for admin/manager -->
      <button type="button" class="btn btn-danger m-1"
              [disabled]="!isAdminOrManager"
              mwlConfirmationPopover
              (confirm)="amend()"
              title="Cancel invoice (only available for managers and administrators)">
        <i class="fa fa-edit mr-1 d-none d-sm-inline"></i> Cancel
      </button>
      <button type="button" class="btn btn-danger m-1" (click)="paymentHistory()">
        <i class="fa fa-history mr-1 d-none d-sm-inline"></i> History
      </button>
      <button type="button" class="btn btn-danger m-1" (click)="print()">
        <i class="fa fa-print mr-1 d-none d-sm-inline"></i> Reprint
      </button>
      <button type="button" class="btn btn-danger m-1" (click)="viewSi()">
        <i class="fa fa-eye mr-1 d-none d-sm-inline"></i> View
      </button>
      <button type="button" class="btn btn-danger m-1" [disabled]="selectedSi.balance == 0"
              (click)="payBalance()">
        <i class="fa fa-money-bill mr-1 d-none d-sm-inline"></i> Pay Balance
      </button>
      <!-- Mark As Paid button - only enabled for admin/manager and invoices with balance -->
      <button type="button" class="btn btn-primary m-1"
              [disabled]="!selectedSi || selectedSi.balance == 0 || !isAdminOrManager"
              mwlConfirmationPopover
              [popoverTitle]="'Confirm Payment'"
              [popoverMessage]="'Are you sure you want to mark this invoice as fully paid with Cash payment method?'"
              placement="top"
              (confirm)="markAsPaid()"
              title="Mark invoice as paid (only available for managers and administrators)">
        <i class="fa fa-check-circle mr-1 d-none d-sm-inline"></i> Mark As Paid
      </button>
    </div>
  </div>

</div>
