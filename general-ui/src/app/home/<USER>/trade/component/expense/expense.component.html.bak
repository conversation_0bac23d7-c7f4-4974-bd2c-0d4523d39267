<div class="container-fluid px-0">
  <h2 class="component-title">ADD EXPENSE</h2>
    <form #expenseForm="ngForm">
      <div class="row">
        <div class="form-group col-md-6">
          <label class="form-label">Date</label>
          <input required #expenseDate="ngModel" type="text" name="piDate" id="piDate"
                 [(ngModel)]="expense.date" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                 [class.is-invalid]="expenseDate.invalid && expenseDate.touched"
                 class="form-control" placeholder="Enter Invoice Date" autocomplete="false">
          <small class="text-danger" [class.d-none]="expenseDate.valid || expenseDate.untouched">*Expense Date is
            required
          </small>
        </div>  <div class="form-group col-md-6">
          <label>Expense Type</label>
          <div class="input-group">
            <input [(ngModel)]="keyExpenseType"
                   [typeahead]="expenseTypeList"
                   (typeaheadLoading)="loadExpenseTypes()"
                   (typeaheadOnSelect)="setSelectedExpType($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   placeholder="Search Expense Types"
                   autocomplete="off"
                   size="16"
                   class="form-control" name="searchExpenseType">
          </div>
        </div>  <div class="col-md-6 form-group">
          <label>Amount</label>
          <input type="number" required #amount="ngModel" placeholder="Amount"
                 [class.is-invalid]="amount.invalid && amount.touched"
                 class="form-control" name="amount" id="amount" [(ngModel)]="expense.amount">
          <small class="text-danger" [class.d-none]="amount.valid || amount.untouched">*Expense Amount Required
          </small>
        </div>  <div class="col-md-6 form-group">
          <label>Responsible Person</label>
          <input [(ngModel)]="keyEmpSearch"
                 [typeahead]="empSearchList"
                 (typeaheadLoading)="searchEmployee()"
                 (typeaheadOnSelect)="setSelectedEmp($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="Enter Responsible Name"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="searchEmp">
        </div>  <div class="col-md-3 form-group">
          <label>Reference</label>
          <input type="text" #reference="ngModel"
                 [class.is-invalid]="reference.invalid && reference.touched"
                 class="form-control" name="reference" id="reference" [(ngModel)]="expense.reference">
        </div>  <div class="col-md-9 form-group">
          <label>Remark</label>
          <input type="text" required #remark="ngModel" class="form-control" name="percentage" id="percentage"
                 [(ngModel)]="expense.remark">
        </div>  <div class="col-md-12 mt-3 pr-0 text-right">
          <button type="button" (click)="save(expenseForm)"
                  [disabled]="!expenseForm.form.valid" class="btn btn-theme mr-2"> Save
          </button>
          <button type="button" (click)="expenseForm.reset()" class="btn btn-warning">Clear
          </button>
        </div>
      </div>
    </form>
</div>


