import {Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {TradeConstants} from "../trade-constants";

@Injectable({
  providedIn: 'root'
})
export class CashRecordService {

  constructor(private http: HttpClient) {
  }

  dayStart(addingAmount) {
    return this.http.get(TradeConstants.DAY_START, {params: {addingAmount: addingAmount}});
  }

  addCash(addingAmount) {
    return this.http.get(TradeConstants.ADD_CASH, {params: {addingAmount: addingAmount}});
  }

  withdraw(withdrawingAmount, purpose) {
    return this.http.get(TradeConstants.WITHDRAW_CASH,
      {
        params: {
          withdrawingAmount: withdrawingAmount,
          purpose: purpose
        }
      });
  }

  findByDrawerNoAndDataBetween(drawerNo, sDate, eDate) {
    return this.http.get(TradeConstants.FIND_BY_DRAWER_NO_AND_DATES_BETWEEN,
      {
        params: {
          drawerNo: drawerNo,
          sDate: sDate,
          eDate: eDate
        }
      });
  }

  findByTypeAndDataBetween(typeId, sDate, eDate) {
    return this.http.get(TradeConstants.FIND_BY_TYPE_AND_DATES_BETWEEN,
      {
        params: {
          typeId: typeId,
          sDate: sDate,
          eDate: eDate
        }
      });
  }

  findByDataBetween(sDate, eDate) {
    return this.http.get(TradeConstants.FIND_BY_DATES_BETWEEN,
      {
        params: {
          sDate: sDate,
          eDate: eDate
        }
      });
  }

  findByDrawerNoAndTypeAndDataBetween(drawerNo, typeId, sDate, eDate) {
    return this.http.get(TradeConstants.FIND_BY_DRAWER_NO_AND_TYPE_AND_DATES_BETWEEN,
      {
        params: {
          drawerNo: drawerNo,
          typeId: typeId,
          sDate: sDate,
          eDate: eDate
        }
      });
  }


}
