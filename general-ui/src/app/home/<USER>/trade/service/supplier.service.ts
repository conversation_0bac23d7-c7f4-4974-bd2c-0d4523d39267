import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {TradeConstants} from "../trade-constants";

@Injectable({
  providedIn: 'root'
})
export class SupplierService {

  constructor(private http: HttpClient) {
  }

  save(supplier) {
    return this.http.post<any>(TradeConstants.SAVE_SUPPLIER, supplier);
  }

  public findAll(page, pageSize) {
    return this.http.get(TradeConstants.GET_SUPPLIERS, {params: {page: page, pageSize: pageSize}});
  }

  public delete(id) {
    return this.http.delete(TradeConstants.DELETE_SUPPLIER, {params: {id: id}});
  }

  public getLastSupplier() {
    return this.http.get(TradeConstants.GET_LAST_SUPPLIER);
  }

  public findByNameLike(name) {
    return this.http.get(TradeConstants.SEARCH_SUPPLIER_BY_NAME_LIKE, {params: {name: name}});
  }

  public findBySupplierId(id) {
    return this.http.get(TradeConstants.GET_SUPPLIER_BY_ID, {params: {id: id}});
  }
}
