import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {TradeConstants} from "../trade-constants";

@Injectable({
  providedIn: 'root'
})
export class CashierHistoryService {

  constructor(private http: HttpClient) { }

  save(actualAmount, withdrawalAmount, balance){
    return this.http.get(TradeConstants.SAVE_CASH_DRAWER_HISTORY, {params: {
        actualAmount: actualAmount,
        withdrawalAmount: withdrawalAmount,
        balance: balance
      }});
  }
}
