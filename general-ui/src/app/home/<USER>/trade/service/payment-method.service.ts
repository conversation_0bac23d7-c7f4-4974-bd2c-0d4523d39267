import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {TradeConstants} from '../trade-constants';

@Injectable({
  providedIn: 'root'
})
export class PaymentMethodService {

  constructor(private http:HttpClient) { }

  public findAllPaymentMethod () {
    return this.http.get(TradeConstants.GET_ALL_PAYMENT_METHOD);

  }

  public findPaymentMethodById (id) {
    return this.http.get(TradeConstants.FIND_PAYMENT_METHOD_BY_ID, {params: {id: id}});
  }

}
