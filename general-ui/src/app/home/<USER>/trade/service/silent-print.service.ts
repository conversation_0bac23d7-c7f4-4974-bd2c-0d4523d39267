import { Injectable } from '@angular/core';
import { SettingsService } from '../../../core/service/settings.service';

@Injectable({
  providedIn: 'root'
})
export class SilentPrintService {

  constructor(private settingsService: SettingsService) { }

  /**
   * Get the useSilentPrint setting from SettingsService
   * @returns true if silent printing is enabled, false otherwise
   */
  private getUseSilentPrintSetting(): boolean {
    return this.settingsService.useSilentPrint();
  }

  /**
   * Prints the specified element silently without showing the print dialog
   * Note: This requires Chrome to be launched with --kiosk-printing flag
   * @param elementId The ID of the element to print
   */
  printElementSilently(elementId: string): void {
    const printContent = document.getElementById(elementId);

    if (!printContent) {
      console.error(`Element with ID ${elementId} not found`);
      return;
    }

    // Check if silent printing is enabled in localStorage
    const useSilentPrint = this.getUseSilentPrintSetting();
    if (useSilentPrint) {
      this.printHtmlContentSilently(printContent.innerHTML);
    } else {
      // Fall back to regular printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        console.error('Could not open print window. Pop-up might be blocked.');
        return;
      }

      printWindow.document.write(`
        <html>
          <head>
            <title>Print</title>
            <script>
              // Add event listener for afterprint
              window.addEventListener('afterprint', function() {
                // Close the window after printing
                setTimeout(function() {
                  window.close();
                }, 500);
              });
            </script>
          </head>
          <body>
            ${printContent.innerHTML}
          </body>
        </html>
      `);

      printWindow.document.close();
      printWindow.focus();

      // Let the user handle the printing dialog
      printWindow.print();

      // Force close the window after a timeout as a fallback
      setTimeout(() => {
        try {
          if (!printWindow.closed) {
            printWindow.close();
          }
        } catch (e) {
          console.error('Error closing print window:', e);
        }
      }, 2000);
    }
  }

  /**
   * Auto-prints the current page without showing the dialog
   * This is a simpler approach but requires Chrome with --kiosk-printing flag
   */
  printCurrentPageSilently(): void {
    // Always use window.print() for this method, as it's the simplest approach
    // The browser will decide whether to show the dialog based on its settings
    window.print();
  }

  /**
   * Prints HTML content directly without showing the print dialog
   * @param htmlContent The HTML content to print
   * @param customStyles Optional CSS styles to apply to the print
   */
  printHtmlContentSilently(htmlContent: string, customStyles: string = ''): void {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      console.error('Could not open print window. Pop-up might be blocked.');
      return;
    }

    // Add necessary styles
    printWindow.document.write(`
      <html>
        <head>
          <title>Print</title>
          <style>
            @media print {
              body { margin: 0; padding: 0; }
              @page { size: auto; margin: 0mm; }
            }
            ${customStyles}
          </style>
          <script>
            // Add event listener for afterprint
            window.addEventListener('afterprint', function() {
              // Close the window after printing
              setTimeout(function() {
                window.close();
              }, 500);
            });
          </script>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `);

    printWindow.document.close();

    // Wait for content to load before printing
    printWindow.onload = function() {
      printWindow.focus();
      printWindow.print();

      // Force close the window after a timeout as a fallback
      // This ensures the window closes even if the afterprint event doesn't fire
      setTimeout(() => {
        try {
          if (!printWindow.closed) {
            printWindow.close();
          }
        } catch (e) {
          console.error('Error closing print window:', e);
        }
      }, 2000);
    };
  }

  /**
   * Prints HTML content with styles from the current document
   * @param htmlContent The HTML content to print
   * @param customStyles Optional CSS styles to apply to the print
   */
  printHtmlWithDocumentStylesSilently(htmlContent: string, customStyles: string = ''): void {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      console.error('Could not open print window. Pop-up might be blocked.');
      return;
    }

    // Get all stylesheets from the current document
    const styleSheets = Array.from(document.styleSheets);
    let styles = '';

    // Extract styles from stylesheets
    styleSheets.forEach(sheet => {
      try {
        if (sheet.cssRules) {
          const cssRules = Array.from(sheet.cssRules);
          cssRules.forEach(rule => {
            styles += rule.cssText;
          });
        }
      } catch (e) {
        console.warn('Cannot access stylesheet rules', e);
      }
    });

    // Add the document to the new window
    printWindow.document.write(`
      <html>
        <head>
          <title>Print</title>
          <style>
            ${styles}
            @media print {
              body { margin: 0; padding: 0; }
              @page { size: auto; margin: 0mm; }
            }
            ${customStyles}
          </style>
          <script>
            // Add event listener for afterprint
            window.addEventListener('afterprint', function() {
              // Close the window after printing
              setTimeout(function() {
                window.close();
              }, 500);
            });
          </script>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `);

    printWindow.document.close();

    // Wait for content to load before printing
    printWindow.onload = function() {
      printWindow.focus();
      printWindow.print();

      // Force close the window after a timeout as a fallback
      // This ensures the window closes even if the afterprint event doesn't fire
      setTimeout(() => {
        try {
          if (!printWindow.closed) {
            printWindow.close();
          }
        } catch (e) {
          console.error('Error closing print window:', e);
        }
      }, 2000);
    };
  }

  /**
   * Prints the specified element with custom styles silently
   * @param elementId The ID of the element to print
   * @param customStyles Additional CSS styles to apply to the print
   * @param htmlContent Optional direct HTML content to print (used if elementId is empty)
   */
  printElementWithStylesSilently(elementId: string, customStyles: string = '', htmlContent: string = ''): void {
    let content = '';

    // If elementId is provided, get content from that element
    if (elementId && elementId.trim() !== '') {
      const printContent = document.getElementById(elementId);

      if (!printContent) {
        console.error(`Element with ID ${elementId} not found`);
        return;
      }

      content = printContent.innerHTML;
    } else {
      // Otherwise use the provided HTML content
      content = htmlContent;
    }

    this.printHtmlWithDocumentStylesSilently(content, customStyles);
  }

  /**
   * Print an invoice
   * @param invoiceNo The invoice number to print
   * @param printerTemplate The printer template to use
   */
  printInvoice(invoiceNo: string, printerTemplate: string = '58mm_English'): void {
    // Log the invoice number and printer template
    console.log(`Printing invoice ${invoiceNo} using template ${printerTemplate}`);

    // This method should be implemented by the application to fetch invoice data
    // and generate the appropriate HTML for printing based on the template
  }
}
