import {Injectable} from '@angular/core';
import {TradeConstants} from '../trade-constants';
import {HttpClient} from "@angular/common/http";
import {SalesInvoice} from "../model/sales-invoice";
import {SalesInvoiceRecord} from "../model/sales-invoice-record";

@Injectable({
  providedIn: 'root'
})
export class SalesInvoiceService {
  constructor(private http: HttpClient) {
  }

  public save(salesInvoice) {
    return this.http.post<any>(TradeConstants.SAVE_SALES_INVOICE, salesInvoice);
  }

  /**
   * Update an existing sales invoice
   * @param salesInvoice The sales invoice to update
   * @returns Observable with the result
   */
  public update(salesInvoice) {
    return this.http.post<any>(TradeConstants.UPDATE_SALES_INVOICE, salesInvoice);
  }

  public findAll(page, pageSize) {
    return this.http.get(TradeConstants.GET_SALES_INVOICE, {params: {page: page, pageSize: pageSize}});
  }

  public findAllPendingPages(page, pageSize) {
    return this.http.get(TradeConstants.GET_PENDING_SALES_INVOICE, {params: {page: page, pageSize: pageSize}});
  }

  public amendInvoice(invNo) {
    return this.http.get(TradeConstants.AMEND_SI, {params: {invNo: invNo}});
  }

  /**
   * Find a sales invoice by ID
   * @param id The ID of the invoice to find
   * @returns Observable of SalesInvoice
   */
  public findById(id: string) {
    return this.http.get<SalesInvoice>(TradeConstants.GET_SALES_INVOICE_BY_ID, {params: {id: id}});
  }

  /**
   * Find a sales invoice record by ID
   * @param id The ID of the invoice record to find
   * @returns Observable of SalesInvoiceRecord
   */
  public findRecordById(id: string) {
    return this.http.get<SalesInvoiceRecord>(TradeConstants.GET_SALES_INVOICE_RECORD_BY_ID, {params: {id: id}});
  }

  public findAllIncomplete() {
    return this.http.get(TradeConstants.GET_ALL_INCOMPLETE, {});
  }

  public findByInvoiceNo(invNo: string) {
    return this.http.get<SalesInvoice>(TradeConstants.FIND_SI_BY_INV_NO, {params: {invNo: invNo}});
  }

  public findByCustomerNicBr(nicBr) {
    return this.http.get(TradeConstants.FIND_SI_BY_CUST_NIC_BR, {params: {nicBr: nicBr}});
  }

  public findByCustomerId(id, page, pageSize) {
    return this.http.get(TradeConstants.FIND_SI_BY_CUST_ID, {params: {id: id, page: page, pageSize: pageSize}});
  }

  public findByPaymentType(paymentType) {
    return this.http.get(TradeConstants.FIND_SI_BY_PAYMENT_TYPE, {params: {pay: paymentType}});
  }

  public findByJobNo(jobNo) {
    return this.http.get(TradeConstants.FIND_SI_BY_JOB_NO, {params: {jobNo: jobNo}});
  }

  public cancelSalesInvoice(salesInvoice: string, status: string) {
    return this.http.get<SalesInvoice[]>(TradeConstants.CANCEL_SALES_INVOICE, {
      params: {
        invoiceId: salesInvoice,
        statusId: status
      }
    });
  }

  public payBalance(payBalance) {
    return this.http.post(TradeConstants.PAY_SI_BALANCE, payBalance);
  }


  findByPaymentMethod(paymentMethodId, page, pageSize) {
    return this.http.get(TradeConstants.FIND_BY_PAYMENT_METHOD, {
      params: {
        paymentMethodId: paymentMethodId,
        page: page,
        pageSize: pageSize
      }
    });
  }

  findByPaymentStatus(paymentStatusId, page, pageSize) {
    return this.http.get(TradeConstants.FIND_ALL_BY_PAYMENT_STATUS, {
      params: {
        paymentStatusId: paymentStatusId,
        page: page,
        pageSize: pageSize
      }
    });
  }

  public findPendingByCustomer(nicBr) {
    return this.http.get(TradeConstants.FIND_PENDING_SI_BY_CUSTOMER, {params: {nicBr: nicBr}});
  }

  findAllByDate(date) {
    return this.http.get(TradeConstants.FIND_ALL_SIS_BY_DATE, {
      params: {date: date}
    })
  }

  /**
   * Find sales invoices with multiple filters
   * @param filters Object containing filter parameters
   * @returns Observable with filtered sales invoices
   */
  findWithFilters(filters: any) {
    // Build parameters object
    const params: any = {
      page: filters.page || 0,
      pageSize: filters.pageSize || 10
    };

    // Add filters if they exist
    if (filters.startDate && filters.endDate) {
      params.startDate = filters.startDate;
      params.endDate = filters.endDate;
    }

    if (filters.customerNo) {
      params.customerNo = filters.customerNo;
    }

    if (filters.invoiceNo) {
      params.invoiceNo = filters.invoiceNo;
    }

    if (filters.drawerNo) {
      params.drawerNo = filters.drawerNo;
    }

    if (filters.cashierUserName) {
      params.cashierUserName = filters.cashierUserName;
    }

    if (filters.routeNo) {
      params.routeNo = filters.routeNo;
    }

    console.log('Sending filters to backend:', params);

    return this.http.get(TradeConstants.FIND_WITH_FILTERS, {
      params: params
    });
  }
}
