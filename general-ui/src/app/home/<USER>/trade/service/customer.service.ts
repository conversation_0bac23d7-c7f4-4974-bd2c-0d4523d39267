import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {TradeConstants} from "../trade-constants";

@Injectable({
  providedIn: 'root'
})
export class CustomerService {

  constructor(private http: HttpClient) {
  }

  save(customer) {
    return this.http.post<any>(TradeConstants.SAVE_CUSTOMER, customer);
  }

  public findAllPagination(page, pageSize) {
    return this.http.get(TradeConstants.FIND_ALL_CUSTOMERS, {params: {page: page, pageSize: pageSize}});
  }

  findById(id: string) {
    return this.http.get(TradeConstants.FIND_CUSTOMER_BY_ID, {params: {id: id}});
  }

  findByNameLike(name: string) {
    return this.http.get(TradeConstants.FIND_BY_CUSTOMER_NAME, {params: {name: name}});
  }

  findByNicLike(nic: string) {
    return this.http.get(TradeConstants.FIND_CUSTOMER_BY_NIC_LIKE, {params: {nic: nic}});
  }

  findByTpLike(tp: string) {
    return this.http.get(TradeConstants.FIND_CUSTOMER_BY_TP_LIKE, {params: {tp: tp}});
  }

  public checkNic(nic) {
    return this.http.get(TradeConstants.CUSTOMER_NIC_CHECK, {params: {nic: nic}});
  }

  findByCustomerNo(customerNo: string) {
    return this.http.get(TradeConstants.FIND_CUSTOMER_BY_NO, {params: {customerNo: customerNo}});
  }
}
