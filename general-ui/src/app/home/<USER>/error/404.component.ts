import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  templateUrl: '404.component.html',
  styles: [`
    .fas { margin-right: 8px; }
    .btn { margin: 0 8px; }
    .display-1 { font-size: 6rem; }
  `]
})
export class P404Component implements OnInit {

  constructor(private router: Router) { }

  ngOnInit(): void {
    // You could add analytics tracking for 404 errors here
  }

  goToHome(): void {
    this.router.navigate(['/home/<USER>']);
  }

  goBack(): void {
    window.history.back();
  }
}
