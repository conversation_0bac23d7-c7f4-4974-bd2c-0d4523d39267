import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AppStateService {
  private isNavigating = new BehaviorSubject<boolean>(false);
  private isAuthenticating = new BehaviorSubject<boolean>(false);
  private currentRoute = new BehaviorSubject<string>('');

  constructor(private router: Router) {
    // Track route changes
    this.router.events.subscribe(() => {
      this.currentRoute.next(this.router.url);
    });
  }

  /**
   * Check if currently navigating
   */
  isNavigating$(): Observable<boolean> {
    return this.isNavigating.asObservable();
  }

  /**
   * Set navigation state
   */
  setNavigating(isNavigating: boolean): void {
    this.isNavigating.next(isNavigating);
  }

  /**
   * Check if currently authenticating
   */
  isAuthenticating$(): Observable<boolean> {
    return this.isAuthenticating.asObservable();
  }

  /**
   * Set authentication state
   */
  setAuthenticating(isAuthenticating: boolean): void {
    this.isAuthenticating.next(isAuthenticating);
  }

  /**
   * Get current route
   */
  getCurrentRoute$(): Observable<string> {
    return this.currentRoute.asObservable();
  }

  /**
   * Safe navigation that prevents conflicts
   */
  safeNavigate(route: string[]): Promise<boolean> {
    if (this.isNavigating.value) {
      console.log('Navigation already in progress, skipping...');
      return Promise.resolve(false);
    }

    const targetRoute = route.join('/');
    if (this.currentRoute.value === targetRoute) {
      console.log('Already on target route:', targetRoute);
      return Promise.resolve(true);
    }

    this.setNavigating(true);
    
    return this.router.navigate(route).then(
      (success) => {
        this.setNavigating(false);
        return success;
      },
      (error) => {
        this.setNavigating(false);
        console.error('Navigation failed:', error);
        return false;
      }
    );
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
    
    if (!currentUser || !currentUser.token) {
      return false;
    }

    try {
      const tokenPayload = JSON.parse(atob(currentUser.token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      
      if (tokenPayload.exp && tokenPayload.exp < currentTime) {
        // Token is expired
        this.clearUserSession();
        return false;
      }
      
      return true;
    } catch (error) {
      // Invalid token format
      this.clearUserSession();
      return false;
    }
  }

  /**
   * Clear user session and redirect to login
   */
  clearUserSession(): void {
    localStorage.removeItem('currentUser');
    
    // Prevent multiple simultaneous redirects
    if (!this.isNavigating.value && !this.router.url.includes('/login')) {
      this.safeNavigate(['/login']);
    }
  }

  /**
   * Handle authentication errors
   */
  handleAuthError(): void {
    if (this.isAuthenticating.value) {
      return; // Already handling auth error
    }

    this.setAuthenticating(true);
    this.clearUserSession();
    
    setTimeout(() => {
      this.setAuthenticating(false);
    }, 1000);
  }

  /**
   * Reset application state
   */
  resetState(): void {
    this.isNavigating.next(false);
    this.isAuthenticating.next(false);
  }
}
