import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PermissionsSidebarService {

  private sidebarVisibleSubject = new BehaviorSubject<boolean>(false);
  public sidebarVisible$ = this.sidebarVisibleSubject.asObservable();

  private permissionsSubject = new BehaviorSubject<Array<any>>([]);
  public permissions$ = this.permissionsSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor() { }

  /**
   * Toggle sidebar visibility
   */
  toggleSidebar() {
    this.sidebarVisibleSubject.next(!this.sidebarVisibleSubject.value);
  }

  /**
   * Show sidebar
   */
  showSidebar() {
    this.sidebarVisibleSubject.next(true);
  }

  /**
   * Hide sidebar
   */
  hideSidebar() {
    this.sidebarVisibleSubject.next(false);
  }

  /**
   * Set permissions data
   */
  setPermissions(permissions: Array<any>) {
    this.permissionsSubject.next(permissions);
  }

  /**
   * Set loading state
   */
  setLoading(loading: boolean) {
    this.loadingSubject.next(loading);
  }

  /**
   * Get current sidebar state
   */
  isSidebarVisible(): boolean {
    return this.sidebarVisibleSubject.value;
  }

  /**
   * Set permissions and show sidebar (convenience method)
   */
  setPermissionsAndShow(permissions: Array<any>) {
    this.setPermissions(permissions);
    this.showSidebar();
  }
}
