import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {CoreApiConstants} from '../core-constants';
import {Company} from '../model/company';

@Injectable({
  providedIn: 'root'
})
export class CompanyService {

  constructor(private http: HttpClient) {
  }

  saveLogo(selectedFile) {
      const formData: FormData = new FormData();
      formData.append('logo', selectedFile);
      return this.http.post(CoreApiConstants.SAVE_COMPANY_LOGO, formData);
  }

  save(company: Company) {
    return this.http.post(CoreApiConstants.SAVE_COMPANY, company);
  }

  findCompany() {
    return this.http.get(CoreApiConstants.GET_COMPANY);
  }

  /**
   * Find all companies
   * @returns Observable of Company array
   */
  findAll() {
    return this.http.get<Company[]>(CoreApiConstants.GET_COMPANY);
  }
}
