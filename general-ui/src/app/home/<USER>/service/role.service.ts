import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ApiConstants} from '../admin-constants';
import {Role} from '../model/role';

@Injectable({
  providedIn: 'root'
})
export class RoleService {

  constructor(private http: HttpClient) {
  }
  public findAll() {
    return this.http.get(ApiConstants.GET_ROLES);
  }

  public save(roles: Role) {
    return this.http.post<any>(ApiConstants.SAVE_ROLE, roles);
  }

  public deleteRole(id) {
    return this.http.delete(ApiConstants.DELETE_ROLE,{params:{id:id}});
  }
  public search( search){
    return this.http.get(ApiConstants.SEARCH_ROLE,{params:{any:search}});
  }




}
