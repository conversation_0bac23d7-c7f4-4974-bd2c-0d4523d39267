import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import {catchError, map, tap} from 'rxjs/operators';
import { CoreApiConstants } from '../core-constants';

@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  // Constants for localStorage keys
  private readonly SETTINGS_STORAGE_KEY = 'app_settings';
  private readonly SETTINGS_ENABLED_KEY = 'app_settings_enabled';
  private readonly SETTINGS_TIMESTAMP_KEY = 'app_settings_timestamp';

  // Default values for essential settings
  private readonly DEFAULT_SETTINGS = {
    defaultDiscountMode: 'percentage',
    useSilentPrint: false,
    printerTemplate: '58mm_English',
    minimumMarkupPercentage: 4,
    defaultLanguage: 'en',
    allowSellingUnderCost: false
  };

  constructor(private http: HttpClient) { }

  /**
   * Get a setting from localStorage
   * @param key The setting key
   * @param defaultValue The default value to return if the setting is not found
   * @returns The setting value or the default value
   */
  getSetting(key: string, defaultValue: any = null): any {
    const settings = this.getAllSettings();
    return settings[key] !== undefined ? settings[key] : defaultValue;
  }

  /**
   * Get all settings from localStorage
   * @returns All settings as an object
   */
  getAllSettings(): any {
    // First try to get from app_settings (new format)
    const appSettingsStr = localStorage.getItem(this.SETTINGS_STORAGE_KEY);
    if (appSettingsStr) {
      try {
        const parsedSettings = JSON.parse(appSettingsStr);
        // Normalize the settings object to handle mixed formats
        return this.normalizeSettings(parsedSettings);
      } catch (error) {
        console.error('Error parsing app_settings from localStorage:', error);
      }
    }

    // Fallback to legacy 'settings' key
    const settingsStr = localStorage.getItem('settings');
    if (settingsStr) {
      try {
        return JSON.parse(settingsStr);
      } catch (error) {
        console.error('Error parsing settings from localStorage:', error);
      }
    }

    // If nothing found or parsing failed, return default settings
    return { ...this.DEFAULT_SETTINGS };
  }

  /**
   * Normalize settings from mixed formats to a simple key-value object
   * @param settings The settings object to normalize
   * @returns A normalized key-value object
   */
  private normalizeSettings(settings: any): any {
    if (!settings || typeof settings !== 'object') {
      return { ...this.DEFAULT_SETTINGS };
    }

    const normalized = { ...this.DEFAULT_SETTINGS };

    // Process all properties in the settings object
    Object.keys(settings).forEach(key => {
      const value = settings[key];

      // If the value is an object with a key and value property, it's from the backend API
      if (value && typeof value === 'object' && 'key' in value && 'value' in value) {
        normalized[value.key] = value.value;
      }
      // If the key is numeric, it might be an array-like entry from the backend
      else if (!isNaN(Number(key)) && value && typeof value === 'object' && 'key' in value && 'value' in value) {
        normalized[value.key] = value.value;
      }
      // Otherwise, it's a direct key-value pair
      else {
        normalized[key] = value;
      }
    });

    return normalized;
  }

  /**
   * Save a setting to localStorage
   * @param key The setting key
   * @param value The setting value
   * @param description Optional description of the setting
   * @param category Optional category for the setting
   */
  saveSetting(key: string, value: any, description: string = '', category: string = 'System'): void {
    const settings = this.getAllSettings();
    settings[key] = value;
    localStorage.setItem(this.SETTINGS_STORAGE_KEY, JSON.stringify(settings));
    localStorage.setItem(this.SETTINGS_TIMESTAMP_KEY, Date.now().toString());
  }

  /**
   * Save multiple settings to localStorage
   * @param settingsObj An object containing settings to save
   */
  saveSettings(settingsObj: any): void {
    const settings = this.getAllSettings();
    Object.assign(settings, settingsObj);
    localStorage.setItem(this.SETTINGS_STORAGE_KEY, JSON.stringify(settings));
    localStorage.setItem(this.SETTINGS_TIMESTAMP_KEY, Date.now().toString());
  }

  /**
   * Clean up the settings in localStorage to use a consistent format
   * This converts the mixed format to a simple key-value format
   */
  cleanupSettings(): void {
    const settings = this.getAllSettings();
    localStorage.setItem(this.SETTINGS_STORAGE_KEY, JSON.stringify(settings));
    localStorage.setItem(this.SETTINGS_TIMESTAMP_KEY, Date.now().toString());
    console.log('Settings cleaned up and normalized');
  }

  /**
   * Check if silent printing is enabled
   * @returns True if silent printing is enabled, false otherwise
   */
  useSilentPrint(): boolean {
    const value = this.getSetting('useSilentPrint', 'false');
    return value === true || value === 'true';
  }

  /**
   * Get the print page setting
   * @returns The print page format based on the printer template
   */
  getPrintPage(): string {
    const printerTemplate = this.getSetting('printerTemplate', '58mm_English');
    return this.mapPrinterTemplateToFormat(printerTemplate);
  }

  /**
   * Map printer template to print page format
   * @param printerTemplate The printer template
   * @returns The print page format
   */
  private mapPrinterTemplateToFormat(printerTemplate: string): string {
    switch (printerTemplate) {
      case '58mm_English':
        return '58en';
      case '76mm_English':
        return '76en';
      case '80mm_English':
        return '80en';
      case '80mm_Sinhala':
        return '80sn';
      case 'Legal_English':
        return 'legal';
      case 'Legal_Customised_English':
        return 'halfLetter';
      default:
        return 'legal'; // Default to legal if no valid template is specified
    }
  }

  /**
   * Get the minimum markup percentage
   * This setting can be user-specific or general
   * @returns The minimum markup percentage or 4 as default
   */
  getMinimumMarkupPercentage(): number {
    // First try to get from user settings in localStorage
    const currentUserData = localStorage.getItem('currentUser');
    if (currentUserData) {
      try {
        const userData = JSON.parse(currentUserData);
        // Check if user has specific settings for minimumMarkupPercentage
        if (userData && userData.userSettings && userData.userSettings.minimumMarkupPercentage) {
          const userValue = userData.userSettings.minimumMarkupPercentage;
          console.log('Using user-specific minimumMarkupPercentage:', userValue);
          return typeof userValue === 'number' ? userValue : parseFloat(userValue);
        }
      } catch (error) {
        console.error('Error parsing user settings for minimumMarkupPercentage:', error);
      }
    }

    // Fallback to general settings
    const value = this.getSetting('minimumMarkupPercentage', '4');
    console.log('Using general minimumMarkupPercentage:', value);
    return typeof value === 'number' ? value : parseFloat(value);
  }

  /**
   * Get the default discount mode
   * @returns The default discount mode or 'percentage' as default
   */
  getDefaultDiscountMode(): string {
    return this.getSetting('defaultDiscountMode', 'percentage');
  }

  /**
   * Get the default language
   * @returns The default language or 'en' as default
   */
  getDefaultLanguage(): string {
    return this.getSetting('defaultLanguage', 'en');
  }

  /**
   * Check if selling under cost is allowed
   * @returns True if selling under cost is allowed, false otherwise
   */
  isSellingUnderCostAllowed(): boolean {
    const value = this.getSetting('allowSellingUnderCost', 'false');
    return value === true || value === 'true';
  }

  /**
   * Get a setting value from the server
   * @param key The setting key
   * @param defaultValue The default value to return if the setting is not found
   * @returns Observable with the setting value
   */
  getSettingValue(key: string, defaultValue: any = null): Observable<any> {
    // First try to get from localStorage
    const localValue = this.getSetting(key, null);
    if (localValue !== null) {
      return of(localValue);
    }

    // If not in localStorage, try to get from the server
    return this.http.get(CoreApiConstants.GET_SETTING_BY_KEY, {
      params: { key }
    }).pipe(
      map((setting: any) => setting && setting.value !== undefined ? setting.value : defaultValue),
      tap((value: any) => {
        // Save to localStorage for next time
        if (value !== null) {
          this.saveSetting(key, value);
        }
      }),
      catchError(() => {
        console.error(`Error fetching setting with key ${key} from server`);
        return of(defaultValue);
      })
    );
  }

  /**
   * Get a setting value synchronously (from localStorage only)
   * @param key The setting key
   * @param defaultValue The default value to return if the setting is not found
   * @returns The setting value or the default value
   */
  getSettingValueSync(key: string, defaultValue: any = null): any {
    return this.getSetting(key, defaultValue);
  }

  /**
   * Refresh settings from the server
   * @returns Observable with all settings
   */
  refreshSettings(): Observable<any> {
    return this.http.get(CoreApiConstants.GET_ALL_SETTINGS).pipe(
      tap((settings: any[]) => {
        // Convert array of settings to a key-value map
        const settingsMap = {};
        if (Array.isArray(settings)) {
          settings.forEach(setting => {
            if (setting.key && setting.value !== undefined) {
              settingsMap[setting.key] = setting.value;
            }
          });
        }
        // Save to localStorage
        this.saveSettings(settingsMap);
      }),
      catchError(error => {
        console.error('Error refreshing settings:', error);
        return of([]);
      })
    );
  }
}
