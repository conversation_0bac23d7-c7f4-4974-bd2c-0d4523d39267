import { Injectable } from '@angular/core';
import {CoreApiConstants} from '../core-constants';
import {HttpClient} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class SequenceService {
  constructor(private http: HttpClient) {
  }

  save(sequence) {
    return this.http.post<any>(CoreApiConstants.SAVE_SEQUENCE, sequence);
  }

  public findAll(page, pageSize) {
    return this.http.get(CoreApiConstants.GET_SEQUENCES, {params: {page: page, pageSize: pageSize}});
  }

  findSequenceByName(name) {
    return this.http.get(CoreApiConstants.FIND_SEQUENCE, {params: {any: name}});
  }

  /**
   * Save a sequence if it doesn't already exist
   * @param name The name of the sequence
   * @param prefix The prefix to use for the sequence
   * @param initialValue The initial counter value
   * @returns Observable with the result
   */
  saveIfUnavailable(name: string, prefix: string, initialValue: number) {
    return this.http.post(CoreApiConstants.SAVE_SEQUENCE_IF_UNAVAILABLE, null, {
      params: {
        name: name,
        prefix: prefix,
        initialValue: initialValue.toString()
      }
    });
  }

}
