import { Injectable } from '@angular/core';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root'
})
export class ResponseHandlerService {

  constructor(private notificationService: NotificationService) { }

  /**
   * Handle save operation response
   * @param response - Backend response
   * @param entityName - Name of the entity being saved (e.g., 'Item', 'User', 'Brand')
   * @param callback - Optional callback to execute on success
   */
  handleSaveResponse(response: any, entityName: string, callback?: () => void): void {
    const successMessage = `${entityName} saved successfully`;
    const errorMessage = `Failed to save ${entityName.toLowerCase()}`;
    
    this.handleResponse(response, successMessage, errorMessage, callback);
  }

  /**
   * Handle update operation response
   * @param response - Backend response
   * @param entityName - Name of the entity being updated
   * @param callback - Optional callback to execute on success
   */
  handleUpdateResponse(response: any, entityName: string, callback?: () => void): void {
    const successMessage = `${entityName} updated successfully`;
    const errorMessage = `Failed to update ${entityName.toLowerCase()}`;
    
    this.handleResponse(response, successMessage, errorMessage, callback);
  }

  /**
   * Handle delete operation response
   * @param response - Backend response
   * @param entityName - Name of the entity being deleted
   * @param callback - Optional callback to execute on success
   */
  handleDeleteResponse(response: any, entityName: string, callback?: () => void): void {
    const successMessage = `${entityName} deleted successfully`;
    const errorMessage = `Failed to delete ${entityName.toLowerCase()}`;
    
    this.handleResponse(response, successMessage, errorMessage, callback);
  }

  /**
   * Handle generic operation response
   * @param response - Backend response
   * @param successMessage - Message to show on success
   * @param errorMessage - Message to show on error
   * @param callback - Optional callback to execute on success
   */
  handleResponse(response: any, successMessage: string, errorMessage: string, callback?: () => void): void {
    if (this.isSuccessResponse(response)) {
      // Extract message from response or use default
      const message = this.extractMessage(response) || successMessage;
      this.notificationService.showSuccess(message);
      
      // Execute callback if provided
      if (callback) {
        callback();
      }
    } else {
      // Extract error message from response or use default
      const message = this.extractMessage(response) || errorMessage;
      this.notificationService.showError(message);
    }
  }

  /**
   * Handle error from HTTP request
   * @param error - HTTP error object
   * @param operation - Description of the operation that failed
   */
  handleError(error: any, operation: string): void {
    console.error(`${operation} failed:`, error);
    
    let errorMessage = `${operation} failed`;
    
    if (error) {
      if (error.message) {
        errorMessage += `: ${error.message}`;
      } else if (error.error && error.error.message) {
        errorMessage += `: ${error.error.message}`;
      } else if (error.status) {
        errorMessage += ` (Status: ${error.status})`;
      }
    }
    
    this.notificationService.showError(errorMessage);
  }

  /**
   * Extract message from response object
   * @param response - Response object
   * @returns Extracted message or null
   */
  private extractMessage(response: any): string | null {
    if (!response) {
      return null;
    }

    // If it's already a string, return it
    if (typeof response === 'string') {
      return response;
    }

    // If it's an object, try to extract message
    if (typeof response === 'object') {
      if (response.message) {
        return response.message;
      }
      if (response.msg) {
        return response.msg;
      }
      if (response.data && typeof response.data === 'string') {
        return response.data;
      }
    }

    return null;
  }

  /**
   * Determine if response indicates success
   * @param response - Response object
   * @returns True if response indicates success
   */
  private isSuccessResponse(response: any): boolean {
    if (!response) {
      return false;
    }

    // Check success property
    if (response.hasOwnProperty('success')) {
      return response.success === true;
    }

    // Check status/code properties
    if (response.code) {
      return response.code === 200 || response.code === 201;
    }
    if (response.status) {
      return response.status === 200 || response.status === 201;
    }

    // Check if it's a boolean true
    if (typeof response === 'boolean') {
      return response;
    }

    // If response exists and no explicit failure indicators, assume success
    return true;
  }
}
