import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {map} from 'rxjs/operators';
import {CoreApiConstants} from '../core-constants';
import {Router} from '@angular/router';

@Injectable({providedIn: 'root'})
export class AuthenticationService {
  constructor(private http: HttpClient, private router: Router) {
  }

  login(username: string, password: string) {
    return this.http.post<any>(CoreApiConstants.LOGIN, {username, password})
      .pipe(map(user => {
        if (user && user.token) {
          localStorage.setItem('currentUser', JSON.stringify(user));
          this.router.navigate(['/home']);
        }
        return user;
      }));
  }

  logout() {
    //  remove user from local storage to log user out
    localStorage.removeItem('currentUser');
    this.router.navigateByUrl('/login');
  }
}
