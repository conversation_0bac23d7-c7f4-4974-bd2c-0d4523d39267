import {Injectable} from '@angular/core';
import {CoreApiConstants} from '../core-constants';
import {HttpClient} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})

export class PermissionService {

  constructor(private http: HttpClient) {
  }

  findAvailablePermissions(username) {
    return this.http.get(CoreApiConstants.FIND_AVAILABLE_PERMISSION, {params: {'username': username}});
  }

  findDesktopPermissions(username) {
    return this.http.get(CoreApiConstants.FIND_DESKTOP_PERMISSIONS, {params: {'username': username}});
  }

  findPermissionByName(permName) {
    return this.http.get(CoreApiConstants.GET_PERMISSION_BY_NAME, {params: {'permName': permName}});
  }
}
