import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ModuleSidebarService {

  private activeModuleSubject = new BehaviorSubject<string | null>(null);

  constructor() { }

  /**
   * Get active module observable
   */
  getActiveModule(): Observable<string | null> {
    return this.activeModuleSubject.asObservable();
  }

  /**
   * Set active module
   */
  setActiveModule(module: string | null) {
    this.activeModuleSubject.next(module);
  }

  /**
   * Get current active module value
   */
  getCurrentActiveModule(): string | null {
    return this.activeModuleSubject.value;
  }
}
