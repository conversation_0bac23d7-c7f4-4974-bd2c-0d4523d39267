<div class="container-fluid px-0">
  <h2 class="component-title">{{ 'BUSINESS.TITLE' | translate }}</h2>

  <form #companyForm=ngForm (ngSubmit)="saveCompany();companyForm.reset()">
    <div class="row">
        <div class="form-group  col-md-6">
          <label>{{ 'BUSINESS.COMPANY_NAME' | translate }}</label>
          <input required #company_name="ngModel" type="text" name="company_name" id="company_name"
                 [class.is-invalid]="company_name.invalid && company_name.touched" [(ngModel)]="company.name"
                 class="form-control" placeholder="{{ 'BUSINESS.COMPANY_NAME_PLACEHOLDER' | translate }}">
          <small class="text-danger" [class.d-none]="company_name.valid || company_name.untouched">{{ 'BUSINESS.COMPANY_NAME_REQUIRED' | translate }}
          </small>
        </div>

        <div class="form-group col-md-6">
          <label>{{ 'BUSINESS.COMPANY_SLOGAN' | translate }}</label>
          <input type="text" name="company_slogan" id="company_slogan"
                 [(ngModel)]="company.slogan" class="form-control" placeholder="{{ 'BUSINESS.COMPANY_SLOGAN_PLACEHOLDER' | translate }}">
        </div>
      </div>

      <div class="row">
        <div class="form-group col-md-4">
          <label>{{ 'BUSINESS.CONTACT_1' | translate }}</label>
          <input required #company_contact_1="ngModel" type="text" name="company_contact_1" id="company_contact_1"
                 [class.is-invalid]="company_contact_1.invalid && company_contact_1.touched"
                 [(ngModel)]="company.telephone1" class="form-control" pattern="^\d{10}$"
                 placeholder="{{ 'BUSINESS.CONTACT_1_PLACEHOLDER' | translate }}">
          <small class="text-danger" [class.d-none]="company_contact_1.valid || company_contact_1.untouched">{{ 'BUSINESS.CONTACT_1_REQUIRED' | translate }}</small>
        </div>

        <div class="form-group col-md-4">
          <label>{{ 'BUSINESS.CONTACT_2' | translate }}</label>
          <input type="text" name="company_contact_2" #company_contact_2="ngModel"
                 id="company_contact_2"
                 [(ngModel)]="company.telephone2" class="form-control" pattern="^\d{10}$"
                 placeholder="{{ 'BUSINESS.CONTACT_2_PLACEHOLDER' | translate }}">
        </div>
        <div class="form-group col-md-4">
          <label>{{ 'BUSINESS.CONTACT_3' | translate }}</label>
          <input type="text" name="company_contact_3" #company_contact_3="ngModel"
                 id="company_contact_3"
                 [(ngModel)]="company.telephone3" class="form-control" pattern="^\d{10}$"
                 placeholder="{{ 'BUSINESS.CONTACT_3_PLACEHOLDER' | translate }}">
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <label>{{ 'BUSINESS.ADDRESS' | translate }}</label>
          <div class="form-group">
            <input type="text" #address2="ngModel" class="form-control" id="address2"
                   name="address2" placeholder="{{ 'BUSINESS.ADDRESS_PLACEHOLDER' | translate }}"
                   [class.is-invalid]="address2.invalid && address2.touched"
                   [(ngModel)]="company.fullAddress">
            <small class="text-danger" [class.d-none]="address2.valid || address2.untouched">{{ 'BUSINESS.ADDRESS_REQUIRED' | translate }}
            </small>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="form-group col-md-6">
          <label>{{ 'BUSINESS.COMPANY_EMAIL' | translate }}</label>
          <input #company_email="ngModel" type="text" name="company_email" id="company_email"
                 pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"
                 [class.is-invalid]="company_email.invalid && company_email.touched" [(ngModel)]="company.email"
                 class="form-control" placeholder="{{ 'BUSINESS.COMPANY_EMAIL_PLACEHOLDER' | translate }}">
          <small class="text-danger" [class.d-none]="company_email.valid || company_email.untouched">{{ 'BUSINESS.COMPANY_EMAIL_REQUIRED' | translate }}
          </small>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>{{ 'BUSINESS.COMPANY_REG_NO' | translate }}</label>
            <input #regNo="ngModel" type="text" class="form-control" id="regNo"
                   name="regNo" placeholder="{{ 'BUSINESS.COMPANY_REG_NO_PLACEHOLDER' | translate }}"
                   [class.is-invalid]="regNo.invalid && regNo.touched"
                   [(ngModel)]="company.regNo">
            <small class="text-danger" [class.d-none]="regNo.valid || regNo.untouched">{{ 'BUSINESS.COMPANY_REG_NO_REQUIRED' | translate }}
            </small>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="form-group col-md-6">
          <label>{{ 'BUSINESS.COMPANY_LOGO' | translate }}</label>
          <div class="form-group col-md-12">
            <input class="form-control-file" type="file" #logo placeholder="upload your image"
                   (change)="getFiles($event)"
                   accept="image/*" multiple>
            <img [src]="imageFile" class="mt-2 img-thumbnail"
                 width="250px"
                 height="120px"
                 (error)="imageFile='assets/img/no-image-thumb.png'">

          </div>
        </div>
      </div>

      <div class="row text-right">
        <div class="form-group col-md-12">
          <button type="submit" [disabled]="!companyForm.form.valid" class="btn  btn-theme"><i
            class="fa fa-dot-circle-o"></i> {{ 'BUSINESS.SAVE' | translate }}
          </button>
        </div>
      </div>
  </form>

</div>
