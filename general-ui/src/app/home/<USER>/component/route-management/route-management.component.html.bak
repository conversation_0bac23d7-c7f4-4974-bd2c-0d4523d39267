<div class="container-fluid px-0">
  <h2 class="component-title">{{ 'ROUTE_MANAGEMENT.TITLE' | translate }}</h2>
  <div class="row">
    <div class="input-group col-md-6">
      <div class="row">
        <div class="col-md-12">
          <input [(ngModel)]="searchTerm"
                 [typeahead]="routes"
                 (typeaheadLoading)="loadRoutes()"
                 (typeaheadOnSelect)="selectRoute($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="{{ 'ROUTE_MANAGEMENT.SEARCH_ROUTES' | translate }}"
                 autocomplete="off"
                 size="16"
                 required
                 class="form-control" name="route">
        </div>

        <table class="table table-striped">
          <thead>
          <th>{{ 'ROUTE_MANAGEMENT.ROUTE_NAME' | translate }}</th>
          <th>{{ 'ROUTE_MANAGEMENT.ROUTE_NO' | translate }}</th>
          <th>{{ 'ROUTE_MANAGEMENT.FROM' | translate }}</th>
          <th>{{ 'ROUTE_MANAGEMENT.TO' | translate }}</th>
          <th>{{ 'ROUTE_MANAGEMENT.STATUS' | translate }}</th>
          </thead>
          <tbody>
          <tr *ngFor="let route of routes,let i=index"
              (click)="selectRoute(route,i)"
              [class.active]="i === selectedRow">
            <td>{{route.name}}</td>
            <td>{{route.routeNo}}</td>
            <td>{{route.from}}</td>
            <td>{{route.to}}</td>
            <td>
              <span class="badge" [ngClass]="route.active ? 'badge-success' : 'badge-danger'">
                {{ route.active ? ('ROUTE_MANAGEMENT.ACTIVE' | translate) : ('ROUTE_MANAGEMENT.INACTIVE' | translate) }}
              </span>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="col-xs-12 col-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="totalElements"
                      [(ngModel)]="currentPage"
                      [boundaryLinks]="true"
                      [maxSize]="10"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <form #manageRouteForm="ngForm" (ngSubmit)="saveRoute(); manageRouteForm.reset()">
        <div class="form-group">
          <label>{{ 'ROUTE_MANAGEMENT.ROUTE_NAME' | translate }}</label>
          <input required #name="ngModel" [class.is-invalid]="name.invalid && name.touched"
                 class="form-control" id="name" [(ngModel)]="route.name" name="name"
                 placeholder="{{ 'ROUTE_MANAGEMENT.ROUTE_NAME' | translate }}">
          <div *ngIf="name.errors && (name.invalid || name.touched)">
            <small class="text-danger" [class.d-none]="name.valid || name.untouched">{{ 'ROUTE_MANAGEMENT.ROUTE_NAME_REQUIRED' | translate }}
            </small>
          </div>
        </div>
        <div class="form-group">
          <label>{{ 'ROUTE_MANAGEMENT.ROUTE_NO' | translate }}</label>
          <input class="form-control" id="routeNo" [(ngModel)]="route.routeNo" name="routeNo"
                 placeholder="{{ 'ROUTE_MANAGEMENT.ROUTE_NO' | translate }}" readonly>
          <small class="text-muted">{{ 'ROUTE_MANAGEMENT.ROUTE_NO_AUTO' | translate }}</small>
        </div>
        <div class="form-group">
          <label>{{ 'ROUTE_MANAGEMENT.FROM' | translate }}</label>
          <input required #from="ngModel" [class.is-invalid]="from.invalid && from.touched"
                 class="form-control" id="from" [(ngModel)]="route.from" name="from"
                 placeholder="{{ 'ROUTE_MANAGEMENT.FROM' | translate }}">
          <div *ngIf="from.errors && (from.invalid || from.touched)">
            <small class="text-danger" [class.d-none]="from.valid || from.untouched">{{ 'ROUTE_MANAGEMENT.FROM_REQUIRED' | translate }}
            </small>
          </div>
        </div>
        <div class="form-group">
          <label>{{ 'ROUTE_MANAGEMENT.TO' | translate }}</label>
          <input required #to="ngModel" [class.is-invalid]="to.invalid && to.touched"
                 class="form-control" id="to" [(ngModel)]="route.to" name="to"
                 placeholder="{{ 'ROUTE_MANAGEMENT.TO' | translate }}">
          <div *ngIf="to.errors && (to.invalid || to.touched)">
            <small class="text-danger" [class.d-none]="to.valid || to.untouched">{{ 'ROUTE_MANAGEMENT.TO_REQUIRED' | translate }}
            </small>
          </div>
        </div>
        <div class="form-check checkbox mr-2">
          <input class="form-check-input" id="active" name="active" type="checkbox"
                 [(ngModel)]="route.active">
          <label class="form-check-label" for="active">{{ 'ROUTE_MANAGEMENT.IS_ACTIVE' | translate }}</label>
        </div>
        <div class="row text-right">
          <div class="col-md-12">
            <button type="button" class="btn btn-warning mr-2" (click)="cancelEdit()">{{ 'ROUTE_MANAGEMENT.CLEAR' | translate }}</button>
            <button type="submit" class="btn btn-theme" [disabled]="!manageRouteForm.form.valid">{{ 'ROUTE_MANAGEMENT.SAVE' | translate }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<ngx-loading [show]="loading" [config]="{ backdropBorderRadius: '3px' }"></ngx-loading>

