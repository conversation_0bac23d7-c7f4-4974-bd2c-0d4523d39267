/* Component title styling */
.component-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
}

/* Modal specific styles */
.p-3 {
  padding: 1rem !important;
}

/* Close button styling */
.close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  opacity: 0.5;
  transition: opacity 0.15s;
}

.close:hover {
  opacity: 1;
}

/* Tag input styling */
tag-input {
  margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .component-title {
    font-size: 1.25rem;
  }

  .form-control {
    font-size: 0.9rem;
  }

  .btn {
    font-size: 0.9rem;
  }
}