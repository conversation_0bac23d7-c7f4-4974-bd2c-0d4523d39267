import {Component, EventEmitter, OnInit} from '@angular/core';
import {NotificationService} from '../../../core/service/notification.service';
import {PermissionService} from '../../service/permission.service';
import {Module} from '../../model/module';
import {Permission} from '../../model/permission';
import {BsModalRef} from "ngx-bootstrap/modal";
import {User} from "../../model/user";
import {UserService} from "../../service/user.service";

@Component({
  selector: 'app-manage-user-permissions',
  templateUrl: './manage-user-permissions.component.html',
  styleUrls: ['./manage-user-permissions.component.css']
})

export class ManageUserPermissionsComponent implements OnInit {

  modules: Array<Module> = [];
  availablePerms: Array<Permission> = [];
  desktopPerms: Array<Permission> = [];
  selectedPerm: Permission = new Permission();
  module = new Module();
  modalRef: BsModalRef;
  user: User;
  isModal: boolean = false;
  selectedUsername: string;
  userName: string;

  public event: EventEmitter<any> = new EventEmitter();

  constructor(
    private permService: PermissionService,
    private notificationService: NotificationService,
    private userService: UserService
  ) {
  }

  ngOnInit() {
    // If no user is provided (not in modal mode), use the current user
    if (!this.selectedUsername) {
      this.user = JSON.parse(localStorage.getItem('currentUser'));
      this.selectedUsername = this.user.username;
    }

    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;

    this.getAllModulesForUser();
    this.loadUserPermissions();
  }

  getAllModulesForUser() {
    this.permService.getEnabledModules(this.selectedUsername).subscribe((result: Array<Module>) => {
      this.modules = result;
    })
  }

  loadUserPermissions() {
    // Load the user's current permissions
    this.userService.searchByName(this.selectedUsername).subscribe((user: User) => {
      if (user && user.permissions) {
        this.desktopPerms = user.permissions;
      }
    });
  }

  getPermsForModule(module: Module) {
    this.permService.findPermsByModule(this.module.id).subscribe((result: Array<Permission>) => {
      this.availablePerms = result;
    })
  }

  addToDesktop(perm) {
    if (null != perm.id) {
      this.desktopPerms.push(perm);
    } else {
      this.notificationService.showError('Please Select a Permission')
    }
  }

  close() {
    this.event.emit(this.desktopPerms);
    this.modalRef.hide();
  }

  savePermissions() {
    // Save the user's permissions
    this.userService.searchByName(this.selectedUsername).subscribe((user: User) => {
      if (user) {
        user.permissions = this.desktopPerms;
        this.userService.save(user).subscribe(result => {
          if (result) {
            this.notificationService.showSuccess('User permissions updated successfully');
            this.close();
          } else {
            this.notificationService.showError('Failed to update user permissions');
          }
        });
      }
    });
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }
}
