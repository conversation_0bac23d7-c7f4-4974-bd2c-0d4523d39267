.card-header {
  font-weight: bold;
}

.form-group label {
  font-weight: 500;
}

.alert-info {
  background-color: #e8f4f8;
  border-color: #bee5eb;
}

/* Language switcher styles */
.language-switcher {
  display: flex;
  align-items: center;
}

.language-switcher button {
  margin: 0 5px 0 0;
  min-width: 60px;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
}

/* Tab styling */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 20px;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  color: #495057;
  font-weight: 500;
  padding: 12px 20px;
  transition: all 0.15s ease-in-out;
}

.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
  color: #007bff;
}

.nav-tabs .nav-link.active {
  color: #007bff;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
  border-bottom: 2px solid #fff;
  margin-bottom: -2px;
}

.nav-tabs .nav-link i {
  margin-right: 8px;
}

.tab-content {
  background: #fff;
  border-radius: 0 0 0.25rem 0.25rem;
}

.tab-pane {
  padding: 20px 0;
}

.tab-pane.active {
  display: block;
}

.tab-pane:not(.active) {
  display: none;
}

/* Barcode Settings Styles */
.barcode-settings-container {
  padding: 20px;
}

.settings-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.section-title i {
  margin-right: 8px;
  color: #007bff;
}

.form-control {
  border-radius: 4px;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-control-label {
  font-weight: 500;
  color: #495057;
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #007bff;
  border-color: #007bff;
}

.input-group-text {
  background-color: #e9ecef;
  border-color: #ced4da;
  color: #495057;
  font-weight: 500;
}

.preview-section {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
  position: sticky;
  top: 20px;
}

.preview-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.preview-item {
  margin-bottom: 15px;
}

.preview-item label {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  margin-bottom: 5px;
  display: block;
}

.preview-text {
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  min-height: 35px;
  display: flex;
  align-items: center;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .language-switcher button {
    padding: 0.375rem 0.5rem;
    font-size: 0.9rem;
    min-width: 50px;
  }

  .nav-tabs .nav-link {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .barcode-settings-container {
    padding: 10px;
  }

  .settings-section {
    padding: 15px;
  }

  .preview-section {
    position: static;
    margin-top: 20px;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 10px;
  }

  .d-flex.justify-content-between > div {
    display: flex;
    gap: 10px;
  }
}
