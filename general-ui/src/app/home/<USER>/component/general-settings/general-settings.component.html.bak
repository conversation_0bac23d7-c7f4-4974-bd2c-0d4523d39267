<div class="container-fluid px-0">
  <h2 class="component-title">General Settings</h2>
  <div class="alert alert-info">
    <i class="fas fa-info-circle"></i> {{ 'SETTINGS.OVERRIDE_INFO' | translate }}
  </div>

  <form #settingsForm="ngForm">
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="settings-section">
          <!-- Default Discount Mode -->
          <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <label for="discountMode">{{ 'SETTINGS.DEFAULT_DISCOUNT_MODE' | translate }}</label>
              <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="discountModeSwitch"
                  [checked]="isSettingEnabled('defaultDiscountMode')"
                  (change)="toggleSetting('defaultDiscountMode')">
                <label class="custom-control-label" for="discountModeSwitch">
                  {{ isSettingEnabled('defaultDiscountMode') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                </label>
              </div>
            </div>
            <select
              id="discountMode"
              name="discountMode"
              class="form-control"
              [(ngModel)]="settingsMap['defaultDiscountMode']"
              [disabled]="!isSettingEnabled('defaultDiscountMode')">
              <option *ngFor="let option of discountModeOptions" [value]="option.value">
                {{option.label}}
              </option>
            </select>
            <small class="form-text text-muted">
              {{ 'SETTINGS.DISCOUNT_MODE_HELP' | translate }}
            </small>
          </div>



          <!-- Silent Printing -->
          <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <label for="useSilentPrint">{{ 'SETTINGS.SILENT_PRINTING' | translate }}</label>
              <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="silentPrintSwitch"
                  [checked]="isSettingEnabled('useSilentPrint')"
                  (change)="toggleSetting('useSilentPrint')">
                <label class="custom-control-label" for="silentPrintSwitch">
                  {{ isSettingEnabled('useSilentPrint') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                </label>
              </div>
            </div>
            <select
              id="useSilentPrint"
              name="useSilentPrint"
              class="form-control"
              [(ngModel)]="settingsMap['useSilentPrint']"
              [disabled]="!isSettingEnabled('useSilentPrint')">
              <option value="true">{{ 'SETTINGS.ENABLED_OPTION' | translate }}</option>
              <option value="false">{{ 'SETTINGS.DISABLED_OPTION' | translate }}</option>
            </select>
            <small class="form-text text-muted">
              {{ 'SETTINGS.SILENT_PRINT_HELP' | translate }}
            </small>
          </div>

          <!-- Printer Template -->
          <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <label for="printerTemplate">{{ 'SETTINGS.PRINTER_TEMPLATE' | translate }}</label>
              <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="printerTemplateSwitch"
                  [checked]="isSettingEnabled('printerTemplate')"
                  (change)="toggleSetting('printerTemplate')">
                <label class="custom-control-label" for="printerTemplateSwitch">
                  {{ isSettingEnabled('printerTemplate') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                </label>
              </div>
            </div>
            <select
              id="printerTemplate"
              name="printerTemplate"
              class="form-control"
              [(ngModel)]="settingsMap['printerTemplate']"
              [disabled]="!isSettingEnabled('printerTemplate')">
              <option *ngFor="let option of printerTemplateOptions" [value]="option.value">{{option.label}}</option>
            </select>
            <small class="form-text text-muted">
              {{ 'SETTINGS.PRINTER_TEMPLATE_HELP' | translate }}
            </small>
          </div>

          <!-- Invoice Creation Mode has been moved to User Settings -->
          <!-- This setting is now user-specific and can be found in the User Settings page -->

          <!-- Minimum Markup Percentage for CASHIER role -->
          <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <label for="minimumMarkupPercentage">Minimum Markup Percentage</label>
              <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="minimumMarkupPercentageSwitch"
                  [checked]="isSettingEnabled('minimumMarkupPercentage')"
                  (change)="toggleSetting('minimumMarkupPercentage')">
                <label class="custom-control-label" for="minimumMarkupPercentageSwitch">
                  {{ isSettingEnabled('minimumMarkupPercentage') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                </label>
              </div>
            </div>
            <div class="input-group">
              <input
                type="number"
                id="minimumMarkupPercentage"
                name="minimumMarkupPercentage"
                class="form-control"
                [(ngModel)]="settingsMap['minimumMarkupPercentage']"
                [disabled]="!isSettingEnabled('minimumMarkupPercentage')"
                min="0"
                max="100"
                step="0.1">
              <div class="input-group-append">
                <span class="input-group-text">%</span>
              </div>
            </div>
            <small class="form-text text-muted">
              Minimum percentage markup above cost for users with CASHIER role. Default is 4%.
              <br>
              <strong>Note:</strong> This is now a user-specific setting. This value will be used as a default for users who don't have their own setting.
            </small>
          </div>

          <!-- Allow Selling Under Cost -->
          <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <label for="allowSellingUnderCost">Allow Selling Under Cost</label>
            </div>
            <div class="form-check mb-2">
              <input class="form-check-input" type="radio" name="allowSellingUnderCost" id="allowSellingUnderCostYes"
                [value]="'true'" [(ngModel)]="settingsMap['allowSellingUnderCost']">
              <label class="form-check-label" for="allowSellingUnderCostYes">Yes, allow selling below cost</label>
            </div>
            <div class="form-check mb-2">
              <input class="form-check-input" type="radio" name="allowSellingUnderCost" id="allowSellingUnderCostNo"
                [value]="'false'" [(ngModel)]="settingsMap['allowSellingUnderCost']">
              <label class="form-check-label" for="allowSellingUnderCostNo">No, prevent selling below cost</label>
            </div>
            <small class="form-text text-muted mt-2">
              When set to "No", the system prevents adding items to invoices with selling prices below cost.
              <br>
              <strong>Note:</strong> This is a global setting that affects all users.
            </small>
          </div>

          <!-- Custom Invoice Footer -->
          <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <label for="customInvoiceFooter">Custom Invoice Footer</label>
              <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="customInvoiceFooterSwitch"
                  [checked]="isSettingEnabled('customInvoiceFooter')"
                  (change)="toggleSetting('customInvoiceFooter')">
                <label class="custom-control-label" for="customInvoiceFooterSwitch">
                  {{ isSettingEnabled('customInvoiceFooter') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                </label>
              </div>
            </div>
            <textarea
              id="customInvoiceFooter"
              name="customInvoiceFooter"
              class="form-control"
              rows="3"
              [(ngModel)]="settingsMap['customInvoiceFooter']"
              [disabled]="!isSettingEnabled('customInvoiceFooter')"
              placeholder="Enter custom text to display at the bottom of invoices (e.g., business hours, contact info, promotional message)">
            </textarea>
            <small class="form-text text-muted">
              Custom text to display at the bottom of invoices after "Thank you. Come Again". Leave empty to show no custom footer.
            </small>
          </div>

        </div>
      </div>

      <!-- Additional settings can be added in another column -->
      <div class="col-md-6">
        <div class="settings-section">

          <!-- Currency Settings -->
          <div class="form-group mb-4">
            <label class="mb-3"><strong>Currency Settings</strong></label>

            <!-- Default Currency -->
            <div class="form-group mb-3">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <label for="defaultCurrency">Default Currency</label>
                <div class="custom-control custom-switch">
                  <input type="checkbox" class="custom-control-input" id="defaultCurrencySwitch"
                    [checked]="isSettingEnabled('defaultCurrency')"
                    (change)="toggleSetting('defaultCurrency')">
                  <label class="custom-control-label" for="defaultCurrencySwitch">
                    {{ isSettingEnabled('defaultCurrency') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                  </label>
                </div>
              </div>
              <select
                id="defaultCurrency"
                name="defaultCurrency"
                class="form-control"
                [(ngModel)]="settingsMap['defaultCurrency']"
                (change)="onCurrencyChange()"
                [disabled]="!isSettingEnabled('defaultCurrency')">
                <option *ngFor="let option of currencyOptions" [value]="option.value">
                  {{option.label}}
                </option>
              </select>
              <small class="form-text text-muted">
                Select the default currency for the application
              </small>
            </div>

            <!-- Currency Symbol -->
            <div class="form-group mb-3">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <label for="currencySymbol">Currency Symbol</label>
                <div class="custom-control custom-switch">
                  <input type="checkbox" class="custom-control-input" id="currencySymbolSwitch"
                    [checked]="isSettingEnabled('currencySymbol')"
                    (change)="toggleSetting('currencySymbol')">
                  <label class="custom-control-label" for="currencySymbolSwitch">
                    {{ isSettingEnabled('currencySymbol') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                  </label>
                </div>
              </div>
              <input
                type="text"
                id="currencySymbol"
                name="currencySymbol"
                class="form-control"
                [(ngModel)]="settingsMap['currencySymbol']"
                [disabled]="!isSettingEnabled('currencySymbol')"
                maxlength="5"
                placeholder="₹">
              <small class="form-text text-muted">
                Symbol to display with currency amounts (auto-updated when currency changes)
              </small>
            </div>

            <!-- Currency Position -->
            <div class="form-group mb-3">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <label for="currencyPosition">Currency Position</label>
                <div class="custom-control custom-switch">
                  <input type="checkbox" class="custom-control-input" id="currencyPositionSwitch"
                    [checked]="isSettingEnabled('currencyPosition')"
                    (change)="toggleSetting('currencyPosition')">
                  <label class="custom-control-label" for="currencyPositionSwitch">
                    {{ isSettingEnabled('currencyPosition') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                  </label>
                </div>
              </div>
              <select
                id="currencyPosition"
                name="currencyPosition"
                class="form-control"
                [(ngModel)]="settingsMap['currencyPosition']"
                [disabled]="!isSettingEnabled('currencyPosition')">
                <option *ngFor="let option of currencyPositionOptions" [value]="option.value">
                  {{option.label}}
                </option>
              </select>
              <small class="form-text text-muted">
                Position of currency symbol relative to the amount
              </small>
            </div>
          </div>

          <hr class="my-4">

          <!-- Barcode Customization Settings -->
          <div class="form-group mb-4">
            <label class="mb-3"><strong>Barcode Customization</strong></label>

            <!-- Code Type Settings -->
            <div class="card mb-3">
              <div class="card-header">
                <h6 class="mb-0">Code Type Settings</h6>
              </div>
              <div class="card-body">
                <!-- Show Barcode -->
                <div class="form-group mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <label for="showBarcode">Show Barcode Element</label>
                    <div class="custom-control custom-switch">
                      <input type="checkbox" class="custom-control-input" id="showBarcodeSwitch"
                        [checked]="isSettingEnabled('showBarcode')"
                        (change)="toggleSetting('showBarcode')">
                      <label class="custom-control-label" for="showBarcodeSwitch">
                        {{ isSettingEnabled('showBarcode') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                      </label>
                    </div>
                  </div>
                  <select
                    id="showBarcode"
                    name="showBarcode"
                    class="form-control"
                    [(ngModel)]="settingsMap['showBarcode']"
                    [disabled]="!isSettingEnabled('showBarcode')">
                    <option value="true">Show Barcode</option>
                    <option value="false">Hide Barcode</option>
                  </select>
                  <small class="form-text text-muted">
                    Show or hide the barcode element in barcode labels. Some businesses prefer text-only labels.
                  </small>
                </div>

                <!-- Use Cost Codes -->
                <div class="form-group mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <label for="useCostCodes">Use Cost Codes Instead of Barcodes</label>
                    <div class="custom-control custom-switch">
                      <input type="checkbox" class="custom-control-input" id="useCostCodesSwitch"
                        [checked]="isSettingEnabled('useCostCodes')"
                        (change)="toggleSetting('useCostCodes')">
                      <label class="custom-control-label" for="useCostCodesSwitch">
                        {{ isSettingEnabled('useCostCodes') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                      </label>
                    </div>
                  </div>
                  <select
                    id="useCostCodes"
                    name="useCostCodes"
                    class="form-control"
                    [(ngModel)]="settingsMap['useCostCodes']"
                    [disabled]="!isSettingEnabled('useCostCodes')">
                    <option value="true">Use Cost Codes</option>
                    <option value="false">Use Traditional Barcodes</option>
                  </select>
                  <small class="form-text text-muted">
                    Enable to use custom cost codes instead of traditional barcodes.
                    <a routerLink="/home/<USER>/cost_code_setup" class="ml-2">
                      <i class="fas fa-cog"></i> Setup Cost Codes
                    </a>
                  </small>
                </div>
              </div>
            </div>



            <!-- Print Layout Settings -->
            <div class="card mb-3">
              <div class="card-header">
                <h6 class="mb-0">Print Layout Settings</h6>
              </div>
              <div class="card-body">
                <!-- Print Columns -->
                <div class="form-group mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <label for="barcodePrintColumns">Number of Columns</label>
                    <div class="custom-control custom-switch">
                      <input type="checkbox" class="custom-control-input" id="barcodePrintColumnsSwitch"
                        [checked]="isSettingEnabled('barcodePrintColumns')"
                        (change)="toggleSetting('barcodePrintColumns')">
                      <label class="custom-control-label" for="barcodePrintColumnsSwitch">
                        {{ isSettingEnabled('barcodePrintColumns') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                      </label>
                    </div>
                  </div>
                  <select
                    id="barcodePrintColumns"
                    name="barcodePrintColumns"
                    class="form-control"
                    [(ngModel)]="settingsMap['barcodePrintColumns']"
                    [disabled]="!isSettingEnabled('barcodePrintColumns')">
                    <option value="1">1 Column</option>
                    <option value="2">2 Columns</option>
                    <option value="3">3 Columns</option>
                    <option value="4">4 Columns</option>
                    <option value="5">5 Columns</option>
                  </select>
                  <small class="form-text text-muted">
                    Number of barcode columns per row when printing
                  </small>
                </div>

                <!-- Paper Size -->
                <div class="form-group mb-0">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <label for="barcodePaperSize">Paper Size</label>
                    <div class="custom-control custom-switch">
                      <input type="checkbox" class="custom-control-input" id="barcodePaperSizeSwitch"
                        [checked]="isSettingEnabled('barcodePaperSize')"
                        (change)="toggleSetting('barcodePaperSize')">
                      <label class="custom-control-label" for="barcodePaperSizeSwitch">
                        {{ isSettingEnabled('barcodePaperSize') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                      </label>
                    </div>
                  </div>
                  <select
                    id="barcodePaperSize"
                    name="barcodePaperSize"
                    class="form-control"
                    [(ngModel)]="settingsMap['barcodePaperSize']"
                    [disabled]="!isSettingEnabled('barcodePaperSize')">
                    <option value="30x20">30×20mm (3 stickers per line)</option>
                    <option value="33x21">33×21mm (3 stickers per line)</option>
                    <option value="38x25">38×25mm (2 stickers per line)</option>
                    <option value="50x25">50×25mm (2 stickers per line)</option>
                    <option value="65x15">65×15mm (1 sticker per line)</option>
                    <option value="100x50">100×50mm (1 sticker per line)</option>
                    <option value="100x150">100×150mm (1 sticker per line)</option>
                  </select>
                  <small class="form-text text-muted">
                    Paper size for barcode printing
                  </small>
                </div>
              </div>
            </div>

          </div>

          <hr class="my-4">

          <!-- Language Settings -->
          <div class="form-group mb-4">
            <label class="mb-3"><strong>{{ 'SETTINGS.APPLICATION_LANGUAGE' | translate }}</strong></label>
            <div class="mb-2">{{ 'SETTINGS.SELECT_LANGUAGE' | translate }}</div>
            <div class="language-switcher">
              <button class="btn btn-light" [class.btn-primary]="currentLang === 'en'" [class.btn-outline-primary]="currentLang !== 'en'" (click)="switchLanguage('en')">
                English
              </button>
              <button class="btn btn-light" [class.btn-primary]="currentLang === 'sn'" [class.btn-outline-primary]="currentLang !== 'sn'" (click)="switchLanguage('sn')">
                සිංහල
              </button>
            </div>
            <small class="form-text text-muted mt-2">
              {{ 'SETTINGS.LANGUAGE_HELP' | translate }}
            </small>
          </div>

          <hr class="my-4">

          <p><strong>{{ 'SETTINGS.HOW_SETTINGS_WORK' | translate }}</strong></p>
          <ul>
            <li>{{ 'SETTINGS.SETTINGS_HELP_1' | translate }}</li>
            <li>{{ 'SETTINGS.SETTINGS_HELP_2' | translate }}</li>
            <li>{{ 'SETTINGS.SETTINGS_HELP_3' | translate }}</li>
            <li>{{ 'SETTINGS.SETTINGS_HELP_4' | translate }}</li>
          </ul>
          <p class="text-muted">{{ 'SETTINGS.SERVER_URL_NOTE' | translate }}</p>
        </div>
      </div>
    </div>
    <div class="d-flex justify-content-between">
      <button type="button" class="btn btn-secondary" (click)="resetToDefaults()">
        <i class="fas fa-undo"></i> {{ 'SETTINGS.RESET_TO_DEFAULTS' | translate }}
      </button>
      <button type="button" class="btn btn-primary" (click)="saveSettings()">
        <i class="fas fa-save"></i> {{ 'SETTINGS.SAVE_SETTINGS' | translate }}
      </button>
    </div>
  </form>
</div>


