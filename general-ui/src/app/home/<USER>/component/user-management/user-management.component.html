<div class="container-fluid px-0">
  <h2 class="component-title">{{ 'USER_MANAGEMENT.TITLE' | translate }}</h2>
  <div class="row">
    <div class="col-md-12">
          <div class="row">
            <div class="col-md-6">
              <input type="text" [(ngModel)]="search" class="form-control " id="search" placeholder="{{ 'USER_MANAGEMENT.SEARCH_PLACEHOLDER' | translate }}"
                     name="search">
            </div>
            <div class="col-md-1">
              <button type="button" class="btn btn-primary " (click)="searchUser()">{{ 'USER_MANAGEMENT.SEARCH_BUTTON' | translate }}</button>
            </div>
          </div>
          <br>
          <div class="col-md-12 m-0 p-0">
            <table class="table  table-striped table-bordered">
              <thead>
              <tr>
                <th>{{ 'USER_MANAGEMENT.FULL_NAME' | translate }}</th>
                <th>{{ 'USER_MANAGEMENT.USER_NAME' | translate }}</th>
                <th>{{ 'USER_MANAGEMENT.EMAIL' | translate }}</th>
                <th>{{ 'USER_MANAGEMENT.USER_ROLE' | translate }}</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let user of users ,let i = index" (click)="userDetail(user,i)"
                  [class.active]="i === selectedRow" (dblclick)="editUser(templateEditUser)">
                <td>{{user.firstName + ' ' + user.lastName}}</td>
                <td>{{user.username}}</td>
                <td>{{user.email}}</td>
                <td><p class="p-0 m-0" *ngFor="let userRole of user.userRoles">{{userRole.name}}</p></td>
              </tr>
              </tbody>
            </table>
            <div class="text-right">
              <button class="btn btn-primary mr-2" (click)="editUserPermissions()" [disabled]="!isUserSelected">
                <i class="fas fa-key mr-1"></i> Edit Permissions
              </button>
              <button class="btn btn-primary mr-2" (click)="manageUserSettings()" [disabled]="!isUserSelected">
                <i class="fas fa-cog mr-1"></i> Manage Settings
              </button>
              <button class="btn btn-danger" (click)="editUser(templateEditUser)" [disabled]="!isUserSelected">
                <i class="fas fa-user-edit mr-1"></i> {{ 'USER_MANAGEMENT.EDIT_USER' | translate }}
              </button>
            </div>

          </div>
    </div>
  </div>
</div>

<ng-template #templateEditUser>
  <div class="modal-header">
    <h4 class="modal-title pull-left">{{ 'USER_MANAGEMENT.ADD_ITEM' | translate }}</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-create-user></app-create-user>
  </div>
</ng-template>
