<div class="container-fluid px-0">
    <h2 class="component-title mb-3">{{ 'DESKTOP_MANAGER.TITLE' | translate }}</h2>

    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle mr-2"></i>
        {{ 'DESKTOP_MANAGER.INFO_TEXT' | translate }}
    </div>

    <div class="row">
      <div class="col-md-4 form-group">
        <label for="enableModules">{{ 'DESKTOP_MANAGER.ENABLED_MODULES' | translate }}</label>
        <select class="form-control" id="enableModules" [(ngModel)]="selectedModule" (change)="findPermsForModule(selectedModule)">
          <option></option>
          <option *ngFor="let module of permListMod" [ngValue]="module">{{module.name}}</option>
        </select>
      </div>  <div class="col-md-4 form-group">
        <label for="addedPerms">{{ 'DESKTOP_MANAGER.ENABLED_PERMISSIONS' | translate }}</label>
        <select class="form-control" id="addedPerms" [(ngModel)]="selectedPerm">
          <option></option>
          <option *ngFor="let perm of availablePerms" [ngValue]="perm">{{perm.name}}</option>
        </select>
      </div>  <div class="col-md-4 form-group">
        <label>&nbsp;</label>
        <button class="btn btn-theme float-right form-control" (click)="addToDesktop(selectedPerm)"> {{ 'DESKTOP_MANAGER.ADD_TO_DESKTOP' | translate }}
        </button>
      </div>
    </div>  <div class="row">
      <div class="col-md-12">
        <h5>{{ 'DESKTOP_MANAGER.CURRENT_DESKTOP_PERMISSIONS' | translate }}</h5>
        <tag-input [(ngModel)]='desktopPerms' [identifyBy]="'id'" [displayBy]="'name'" theme='dark'
                   [editable]="false" [hideForm]="true" [allowDupes]="false"></tag-input>
              </div>
    </div>  <div class="row mt-3">
      <div class="col-md-12">
        <button class="btn btn-theme float-right" (click)="saveDesktopPerms()">{{ 'DESKTOP_MANAGER.SAVE' | translate }}</button>
      </div>
    </div>

</div>

