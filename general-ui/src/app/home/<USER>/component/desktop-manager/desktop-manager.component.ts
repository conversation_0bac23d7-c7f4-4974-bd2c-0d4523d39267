import {Component, OnInit} from '@angular/core';
import {Module} from '../../../admin/model/module';
import {Permission} from '../../../admin/model/permission';
import {PermissionService} from '../../service/permission.service';
import {NotificationService} from '../../service/notification.service';
import {Router} from '@angular/router';
import {User} from "../../../admin/model/user";
import {UserService} from "../../../admin/service/user.service";

@Component({
  selector: 'app-desktop-manager',
  templateUrl: './desktop-manager.component.html',
  styleUrls: ['./desktop-manager.component.css']
})
export class DesktopManagerComponent implements OnInit {


  modules: Array<Module>;
  selectedModules: Array<Module>;
  availablePerms: Array<Permission>;
  desktopPerms: Array<Permission>;
  selectedPerm: Permission = new Permission();
  username: String;
  selectedModule: Module;
  user: any;
  permissions: Array<Permission> = [];
  permListMod: Array<any> = [];

  constructor(private permService: PermissionService,
              private notificationService: NotificationService, private userService: UserService,
              private router: Router) {
    if (null === localStorage.getItem('currentUser')) {
      this.router.navigateByUrl('/login');
    }
    this.username = JSON.parse(localStorage.getItem('currentUser')).user.username;
  }

  ngOnInit() {
    this.modules = [];
    this.selectedModules = [];
    this.availablePerms = [];
    this.desktopPerms = [];
    this.user = new User();
    this.findByName();
  }

  findByName() {
    this.userService.searchByName(this.username).subscribe((user: User) => {
      this.user = user;
      if (null != this.user.desktopPermissions) {
        this.desktopPerms = this.user.desktopPermissions;
      }
      this.findUserModuleAndPermission();
    });
  }

  findUserModuleAndPermission() {
    this.permService.findAvailablePermissions(this.user.username).subscribe((result: Array<Permission>) => {
      this.permissions = result;
      this.permissions.forEach(obj => {
        let duplicate = false;
        for (let idx in this.permListMod) {
          if (this.permListMod[idx].name == obj.module.name) {
            duplicate = true;
            this.permListMod[idx].perms.push({
              'name': obj.name, 'route': obj.route
            })
          }
        }
        if (!duplicate) {
          this.permListMod.push({
            'name': obj.module.name, 'perms': [{
              'name': obj.name, 'route': obj.route
            }]
          })
        }
      });

    });
  }

  addToDesktop(permission) {
    this.permService.findPermissionByName(permission.name).subscribe((perm: Permission) => {
      if (null != perm.id) {
        let foundDup = false;
        for (let dPerm of this.desktopPerms) {
          if (perm.id === dPerm.id) {
            foundDup = true;
            break;
          }
        }
        if (!foundDup) {
          this.desktopPerms.push(perm);
        } else {
          this.notificationService.showError('Already Added');
        }
      } else {
        this.notificationService.showError('Please Select a Permission');
      }
    });

  }

  saveDesktopPerms() {
    this.user.desktopPermissions = this.desktopPerms;
    this.userService.update(this.user).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        this.ngOnInit();
      } else {
        this.notificationService.showWarning(result.message);
      }
    });
  }

  // findPermsForModule(cmModule: Module) {
  //   this.selectedModule = cmModule;
  //   this.permService.findPermsByModule(cmModule.id).subscribe((result: Array<Permission>) => {
  //     this.availablePerms = result;
  //     for (let perms of this.availablePerms) {
  //       if (perms.module.id === this.selectedModule.id) {
  //         this.desktopPerms.push(perms);
  //       }
  //     }
  //   })
  // }


  findPermsForModule(selectedModule) {
    this.availablePerms = selectedModule.perms;
  }
}

