/* Component title styling */
.component-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
}

/* Alert styling */
.alert-info {
  background-color: #e8f4f8;
  border-color: #bee5eb;
  color: #0c5460;
}

/* Form control styling */
.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button styling */
.btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

/* Tag input styling */
tag-input {
  margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .component-title {
    font-size: 1.25rem;
  }

  .alert {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .form-control {
    font-size: 0.9rem;
  }

  .btn {
    font-size: 0.9rem;
  }
}