/* User Settings Component Styles */
.card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #f8f9fa;
}

.custom-switch {
  min-width: 80px;
}

.form-group label {
  font-weight: 500;
}

.form-text {
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
  .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .custom-switch {
    margin-top: 0.5rem;
  }
}
