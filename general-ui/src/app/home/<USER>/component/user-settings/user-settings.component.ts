import { Component, OnInit } from '@angular/core';
import { UserSettingsService } from '../../service/user-settings.service';
import { UserSettings } from '../../model/user-settings';
import { NotificationService } from '../../../core/service/notification.service';

@Component({
  selector: 'app-user-settings',
  templateUrl: './user-settings.component.html',
  styleUrls: ['./user-settings.component.css']
})
export class UserSettingsComponent implements OnInit {
  userSettings: UserSettings;
  loading: boolean = false;

  // Options for dropdown menus
  invoiceCreationModeOptions = [
    { value: 'standard', label: 'Standard Invoice' },
    { value: 'sales_rep', label: 'Sales Rep Invoice (Mobile Optimized)' },
    { value: 'distributor', label: 'Distributor Invoice' }
  ];

  constructor(
    private userSettingsService: UserSettingsService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadUserSettings();
  }

  /**
   * Load user settings
   */
  loadUserSettings(): void {
    this.loading = true;
    this.userSettingsService.loadCurrentUserSettings().subscribe(
      (settingsData: any) => {
        // Create a new UserSettings instance with the methods
        const settings = new UserSettings();

        // Copy properties from the response
        settings.id = settingsData.id;
        settings.user = settingsData.user;
        settings.settings = settingsData.settings || {};
        settings.createdDate = settingsData.createdDate;
        settings.createdBy = settingsData.createdBy;
        settings.lastModifiedDate = settingsData.lastModifiedDate;
        settings.lastModifiedBy = settingsData.lastModifiedBy;

        this.userSettings = settings;
        this.loading = false;

        console.log('User settings loaded:', this.userSettings);

        // Initialize invoice creation mode if not set
        if (!this.getSettingValue('invoiceCreationMode')) {
          this.updateSetting('invoiceCreationMode', 'standard');
        }

        // Initialize minimum markup percentage if not set
        if (!this.getSettingValue('minimumMarkupPercentage')) {
          this.updateSetting('minimumMarkupPercentage', '4');
        }
      },
      error => {
        console.error('Error loading user settings:', error);
        this.notificationService.showError('Error loading settings');
        this.loading = false;
      }
    );
  }

  /**
   * Update a setting
   * @param key Setting key
   * @param value Setting value
   * @param enabled Whether the setting is enabled
   */
  updateSetting(key: string, value: string, enabled: boolean = true): void {
    // Only show loading indicator for initial load, not for every setting update
    // this.loading = true;
    this.userSettingsService.updateSetting(key, value, enabled).subscribe(
      success => {
        // this.loading = false;
        if (success) {
          this.notificationService.showSuccess('Settings saved successfully');
        } else {
          this.notificationService.showError('Error saving settings');
        }
      },
      error => {
        console.error('Error updating setting:', error);
        this.notificationService.showError('Error saving settings');
        // this.loading = false;
      }
    );
  }

  /**
   * Enable or disable a setting
   * @param key Setting key
   * @param enabled Whether the setting should be enabled
   */
  setSettingEnabled(key: string, enabled: boolean): void {
    // Only show loading indicator for initial load, not for every setting update
    // this.loading = true;
    this.userSettingsService.setSettingEnabled(key, enabled).subscribe(
      success => {
        // this.loading = false;
        if (success) {
          this.notificationService.showSuccess('Settings saved successfully');
        } else {
          this.notificationService.showError('Error saving settings');
        }
      },
      error => {
        console.error('Error setting enabled:', error);
        this.notificationService.showError('Error saving settings');
        // this.loading = false;
      }
    );
  }

  /**
   * Get a setting value
   * @param key Setting key
   * @returns Setting value or empty string if not found
   */
  getSettingValue(key: string): string {
    if (!this.userSettings) return '';

    try {
      return this.userSettings.getSettingValue(key) || '';
    } catch (error) {
      console.error('Error getting setting value:', error);
      // Fallback: try to access the settings object directly
      if (this.userSettings.settings &&
          this.userSettings.settings[key] &&
          this.userSettings.settings[key].enabled) {
        return this.userSettings.settings[key].value || '';
      }
      return '';
    }
  }

  /**
   * Check if a setting is enabled
   * @param key Setting key
   * @returns true if setting exists and is enabled, false otherwise
   */
  isSettingEnabled(key: string): boolean {
    if (!this.userSettings) return false;

    try {
      return this.userSettings.isSettingEnabled(key);
    } catch (error) {
      console.error('Error checking if setting is enabled:', error);
      // Fallback: try to access the settings object directly
      return !!(this.userSettings.settings &&
                this.userSettings.settings[key] &&
                this.userSettings.settings[key].enabled);
    }
  }
}
