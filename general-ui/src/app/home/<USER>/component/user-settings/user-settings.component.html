<div class="container-fluid px-0">
  <h2 class="component-title">My Settings</h2>

  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">Personal Preferences</h5>
    </div>
    <div class="card-body">
      <!-- Invoice Creation Mode -->
      <div class="form-group mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <label for="invoiceCreationMode">Invoice Creation Mode</label>
          <div class="custom-control custom-switch">
            <input type="checkbox" class="custom-control-input" id="invoiceCreationModeSwitch"
                   [checked]="isSettingEnabled('invoiceCreationMode')"
                   (change)="setSettingEnabled('invoiceCreationMode', $event.target.checked)">
            <label class="custom-control-label" for="invoiceCreationModeSwitch">
              {{ isSettingEnabled('invoiceCreationMode') ? 'Enabled' : 'Disabled' }}
            </label>
          </div>
        </div>
        <select
          id="invoiceCreationMode"
          name="invoiceCreationMode"
          class="form-control"
          [ngModel]="getSettingValue('invoiceCreationMode')"
          (ngModelChange)="updateSetting('invoiceCreationMode', $event)"
          [disabled]="!isSettingEnabled('invoiceCreationMode')">
          <option *ngFor="let option of invoiceCreationModeOptions" [value]="option.value">{{option.label}}</option>
        </select>
        <small class="form-text text-muted">
          Choose which invoice creation interface to use. Standard is the regular interface, Sales Rep is optimized for mobile use by sales representatives, and Distributor Invoice is for distributor-specific operations.
        </small>
      </div>

      <!-- Minimum Markup Percentage for CASHIER role -->
      <div class="form-group mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <label for="minimumMarkupPercentage">Minimum Markup Percentage</label>
          <div class="custom-control custom-switch">
            <input type="checkbox" class="custom-control-input" id="minimumMarkupPercentageSwitch"
              [checked]="isSettingEnabled('minimumMarkupPercentage')"
              (change)="setSettingEnabled('minimumMarkupPercentage', $event.target.checked)">
            <label class="custom-control-label" for="minimumMarkupPercentageSwitch">
              {{ isSettingEnabled('minimumMarkupPercentage') ? 'Enabled' : 'Disabled' }}
            </label>
          </div>
        </div>
        <div class="input-group">
          <input
            type="number"
            id="minimumMarkupPercentage"
            name="minimumMarkupPercentage"
            class="form-control"
            [ngModel]="getSettingValue('minimumMarkupPercentage')"
            (ngModelChange)="updateSetting('minimumMarkupPercentage', $event)"
            [disabled]="!isSettingEnabled('minimumMarkupPercentage')"
            min="0"
            max="100"
            step="0.1">
          <div class="input-group-append">
            <span class="input-group-text">%</span>
          </div>
        </div>
        <small class="form-text text-muted">
          Minimum percentage markup above cost for users with CASHIER role. Default is 4%.
        </small>
      </div>

      <!-- Add more user-specific settings here -->
    </div>
  </div>



  <!-- Loading indicator -->
  <div *ngIf="loading" class="text-center mt-3">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
</div>
