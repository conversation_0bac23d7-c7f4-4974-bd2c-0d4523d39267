<div class="container-fluid px-0">
  <h2 class="component-title">{{ 'EXPENSE_TYPE.TITLE' | translate }}</h2>
    <div class="row">
      <div class="input-group col-md-6">
        <div class="row">
          <div class="col-md-12">
            <input [(ngModel)]="keyExpType"
                   [typeahead]="expenseTypes"
                   (typeaheadLoading)="loadExpenseTypes()"
                   (typeaheadOnSelect)="setSelectedExpenseType($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   placeholder="{{ 'EXPENSE_TYPE.SEARCH_EXPENSE_TYPE' | translate }}"
                   autocomplete="off"
                   size="16"
                   required
                   class="form-control" name="expenseType">
          </div>

          <table class="table table-striped">
            <thead>
            <th>{{ 'EXPENSE_TYPE.EXPENSE_TYPE_CATEGORY' | translate }}</th>
            <th>{{ 'EXPENSE_TYPE.EXPENSE_TYPE_NAME' | translate }}</th>
            </thead>
            <tbody>
            <tr *ngFor="let exp of expenseTypes,let i=index"
                (click)="selectExpense(exp,i)"
                [class.active]="i === selectedRow">
              <td>{{exp.category.name}}</td>
              <td>{{exp.name}}</td>
            </tr>
            </tbody>
          </table>
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="10"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>  <div class="col-md-6">
        <form #manageExpenseTypeForm="ngForm" (ngSubmit)="saveExpenseType(); manageExpenseTypeForm.reset()">
          <div class="form-group">
            <label>{{ 'EXPENSE_TYPE.EXPENSE_CATEGORY' | translate }}</label>
            <select class="form-control" [(ngModel)]="expenseType.category.id"
                    name="expCat" #expCat="ngModel" id="expCat" required>
              <option></option>
              <option *ngFor="let cat of expCategories" [ngValue]="cat.id">{{cat.name}}</option>
            </select>
            <div *ngIf="expCat.errors && (expCat.invalid || expCat.touched)">
              <small class="text-danger" [class.d-none]="expCat.valid || expCat.untouched">{{ 'EXPENSE_TYPE.EXPENSE_CATEGORY_REQUIRED' | translate }}
              </small>
            </div>
          </div>  <div class="form-group">
            <label>{{ 'EXPENSE_TYPE.EXPENSE_NAME' | translate }}</label>
            <input required #name="ngModel" [class.is-invalid]="name.invalid && name.touched"
                   class="form-control" id="name" [(ngModel)]="expenseType.name" name="name"
                   placeholder="{{ 'EXPENSE_TYPE.EXPENSE_NAME' | translate }}">
            <div *ngIf="name.errors && (name.invalid || name.touched)">
              <small class="text-danger" [class.d-none]="name.valid || name.untouched">{{ 'EXPENSE_TYPE.DESCRIPTION_REQUIRED' | translate }}
              </small>
            </div>
          </div>  <div class="form-check checkbox mr-2">
            <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                   [(ngModel)]="expenseType.active">
            <label class="form-check-label" for="check3">{{ 'EXPENSE_TYPE.ACTIVE' | translate }}</label>
          </div>  <div class="row text-right">
            <div class="col-md-12">
              <button type="button" class="btn btn-warning mr-2" (click)="clear()">{{ 'EXPENSE_TYPE.CLEAR' | translate }}</button>
              <button type="submit" class="btn btn-theme" [disabled]="!manageExpenseTypeForm.form.valid">{{ 'EXPENSE_TYPE.SAVE' | translate }}
              </button>
            </div>
          </div>
        </form>
      </div>
  </div>

