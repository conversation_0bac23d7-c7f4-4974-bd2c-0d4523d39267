import {Component, OnInit} from '@angular/core';
import {ExpenseType} from "../../../custom/trade/model/expense-type";
import {ExpenseTypeService} from "../../../custom/trade/service/expense-type.service";
import {MetaDataService} from "../../../core/service/metaData.service";
import {MetaData} from "../../../core/model/metaData";

@Component({
  selector: 'app-expense-type',
  templateUrl: './expense-type.component.html',
  styleUrls: ['./expense-type.component.css']
})
export class ExpenseTypeComponent implements OnInit {

  expenseType = new ExpenseType();
  expenseTypes: Array<ExpenseType> = [];
  expCategories: Array<MetaData> = [];

  setClickedRow: Function;
  selectedRow: number;
  keyExpType: string;
  collectionSize;
  page;
  pageSize;

  constructor(private expenseTypeService: ExpenseTypeService, private metaDataService: MetaDataService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.expenseType = new ExpenseType();
    this.expenseType.active = true;
    this.expenseType.category = new MetaData();
    this.findAll();
    this.findAllExpCategories();
  }

  saveExpenseType() {
    this.expenseTypeService.save(this.expenseType).subscribe(result => {
      console.log(result);
      this.ngOnInit();
    }, error => {
      console.log(error);
    });
  }

  loadExpenseTypes() {
    this.expenseTypeService.findByName(this.keyExpType).subscribe((data: Array<ExpenseType>) => {
      return this.expenseTypes = data;
    });
  }

  findAll() {
    this.expenseTypeService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.expenseTypes = data.content;
      this.collectionSize = data.totalPages * 8;
    });
  }

  findAllExpCategories() {
    this.metaDataService.findByCategory("ExpenseCategory").subscribe((data: Array<MetaData>) => {
      this.expCategories = data;
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  selectExpense(brand, index) {
    this.expenseType = brand;
    this.selectedRow = index;
  }

  setSelectedExpenseType(event) {
    this.expenseType = event.item;
  }

  updateExpenseType() {
    this.saveExpenseType();
  }

  clear() {
    this.expenseType = new ExpenseType();
  }

}
