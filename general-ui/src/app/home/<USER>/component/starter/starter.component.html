<!-- Welcome Header -->
<div class="welcome-header">
  <div class="row align-items-center">
    <div class="col-md-8">
      <h2 class="welcome-title">
        <i class="fas fa-home mr-2"></i>
        {{ 'Welcome back' | translate }}, {{ user?.firstName || user?.username || 'User' }}!
      </h2>
      <p class="welcome-subtitle">{{ "Here's everything you can do with Viganana" | translate }}</p>
    </div>
    <div class="col-md-4 text-right">
      <div class="header-actions">
        <!-- Language Switcher -->
        <div class="language-switcher mr-3">
          <button class="btn btn-outline-secondary btn-sm"
                  [class.active]="currentLang === 'en'"
                  (click)="switchLanguage('en')"
                  title="{{ 'English' | translate }}">
            EN
          </button>
          <button class="btn btn-outline-secondary btn-sm"
                  [class.active]="currentLang === 'sn'"
                  (click)="switchLanguage('sn')"
                  title="{{ 'Sinhala' | translate }}">
            සිං
          </button>
        </div>

        <!-- Menu View Button -->
        <button class="btn btn-outline-secondary" (click)="toggleSidebar()">
          <i class="fas fa-chevron-down mr-2"></i>{{ 'Menu View' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main Content Row -->
<div class="row">
  <!-- Left Column - Modules and Permissions -->
  <div class="col-lg-8">
    <!-- Module Cards (Horizontal) -->
    <div class="modules-section">
      <div class="content-header">
        <h4><i class="fas fa-th-large mr-2"></i>{{ 'Select a Module' | translate }}</h4>
        <p class="text-muted">{{ 'Click on any module below to view its available functions' | translate }}</p>
      </div>

      <div class="modules-grid">
        <div *ngFor="let module of modulePermissions"
             class="module-card"
             [class.active]="selectedModule === module.name"
             (click)="selectModule(module)">
          <div class="module-card-icon">
            <i [class]="getModuleIcon(module.name)"></i>
          </div>
          <div class="module-card-content">
            <h6 class="module-card-title">{{ module.name }}</h6>
            <span class="module-card-count">{{ module.perms.length }} {{ module.perms.length === 1 ? ('function' | translate) : ('functions' | translate) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Selected Module Permissions -->
    <div *ngIf="selectedModuleData" class="permissions-section">
      <div class="permissions-header">
        <h5><i [class]="getModuleIcon(selectedModuleData.name)" class="mr-2"></i>{{ selectedModuleData.name }} {{ 'Functions' | translate }}</h5>
        <span class="permissions-count">{{ selectedModuleData.perms.length }} {{ 'available functions' | translate }}</span>
      </div>

      <div class="permissions-grid">
        <div *ngFor="let permission of selectedModuleData.perms"
             class="permission-card"
             [routerLink]="'../' + permission.route">
          <div class="permission-card-icon">
            <i [class]="permission.iconCss"></i>
          </div>
          <div class="permission-card-content">
            <span class="permission-card-name">{{ permission.name }}</span>
          </div>
          <div class="permission-card-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- No Module Selected Message -->
    <div *ngIf="!selectedModuleData" class="no-selection-message">
      <div class="no-selection-content">
        <i class="fas fa-hand-pointer fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">{{ 'Select a module above to view its functions' | translate }}</h5>
        <p class="text-muted">{{ 'STARTER_NO_MODULE_SELECTED' | translate }}</p>
      </div>
    </div>
  </div>

  <!-- Right Column - Help and Information -->
  <div class="col-lg-4">
    <!-- Help Documentation -->
    <div class="info-card mb-4">
      <div class="info-header">
        <h5><i class="fas fa-question-circle mr-2"></i>{{ 'Help & Documentation' | translate }}</h5>
      </div>
      <div class="info-content">
        <div class="help-link" (click)="openHelpLink('user-guide')">
          <i class="fas fa-book mr-2"></i>
          <span>{{ 'User Guide' | translate }}</span>
          <i class="fas fa-external-link-alt ml-auto"></i>
        </div>
        <div class="help-link" (click)="openHelpLink('faq')">
          <i class="fas fa-question mr-2"></i>
          <span>{{ 'Frequently Asked Questions' | translate }}</span>
          <i class="fas fa-external-link-alt ml-auto"></i>
        </div>
        <div class="help-link" (click)="openHelpLink('support')">
          <i class="fas fa-headset mr-2"></i>
          <span>{{ 'Contact Support' | translate }}</span>
          <i class="fas fa-external-link-alt ml-auto"></i>
        </div>
      </div>
    </div>

    <!-- Video Tutorials -->
    <div class="info-card mb-4">
      <div class="info-header">
        <h5><i class="fab fa-youtube mr-2"></i>{{ 'Video Tutorials' | translate }}</h5>
      </div>
      <div class="info-content">
        <div class="video-tutorial" (click)="openVideoTutorial('getting-started')">
          <div class="video-thumbnail">
            <i class="fas fa-play-circle"></i>
          </div>
          <div class="video-info">
            <strong>{{ 'Getting Started' | translate }}</strong>
            <div class="text-muted small">{{ 'Learn the basics' | translate }}</div>
          </div>
        </div>
        <div class="video-tutorial" (click)="openVideoTutorial('inventory-management')">
          <div class="video-thumbnail">
            <i class="fas fa-play-circle"></i>
          </div>
          <div class="video-info">
            <strong>{{ 'Inventory Management' | translate }}</strong>
            <div class="text-muted small">{{ 'Manage your items' | translate }}</div>
          </div>
        </div>
        <div class="video-tutorial" (click)="openVideoTutorial('sales-process')">
          <div class="video-thumbnail">
            <i class="fas fa-play-circle"></i>
          </div>
          <div class="video-info">
            <strong>{{ 'Sales Process' | translate }}</strong>
            <div class="text-muted small">{{ 'Create invoices' | translate }}</div>
          </div>
        </div>
        <div class="video-tutorial" (click)="openVideoTutorial('reports-analytics')">
          <div class="video-thumbnail">
            <i class="fas fa-play-circle"></i>
          </div>
          <div class="video-info">
            <strong>{{ 'Reports & Analytics' | translate }}</strong>
            <div class="text-muted small">{{ 'Generate reports' | translate }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- New Features -->
    <div class="info-card">
      <div class="info-header">
        <h5><i class="fas fa-star mr-2"></i>{{ "What's New" | translate }}</h5>
      </div>
      <div class="info-content">
        <div class="feature-item">
          <div class="feature-badge">{{ 'NEW' | translate }}</div>
          <div class="feature-info">
            <strong>{{ 'Enhanced Reporting' | translate }}</strong>
            <div class="text-muted small">{{ 'New filters and export options' | translate }}</div>
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-badge">{{ 'UPDATED' | translate }}</div>
          <div class="feature-info">
            <strong>{{ 'Barcode Printing' | translate }}</strong>
            <div class="text-muted small">{{ 'Improved customization options' | translate }}</div>
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-badge">{{ 'NEW' | translate }}</div>
          <div class="feature-info">
            <strong>{{ 'Supplier Returns' | translate }}</strong>
            <div class="text-muted small">{{ 'Batch return processing' | translate }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

