Here's the complete documentation that you can copy and save as "pos-system-documentation.md":

```markdown
# POS System Documentation for Next.js Migration

## 1. System Overview

This documentation provides a comprehensive overview of the existing Point of Sale (POS) system which consists of a Spring Boot backend (Java) and an Angular frontend. The system is structured as follows:

- **Backend (Spring Boot)**: Located in `general-service` directory
- **Frontend (Angular)**: Located in `general-ui` directory

The system encompasses several business modules:
- Inventory Management
- Sales & Trade Operations
- Reporting
- User Administration & Security
- Customer Management
- HR Management

## 2. Backend Architecture

### 2.1 Module Organization

The Spring Boot backend follows a layered architecture with clear separation of concerns:

```
general-service/src/main/java/lk/sout/
├── Main.java                  # Application entry point
├── config/                    # Configuration classes
├── core/                      # Core functionality shared across modules
│   ├── controller/            # Core controllers including user management
│   ├── entity/                # Core entities such as User, Role, Permission
│   ├── repository/            # Data access repositories
│   └── service/               # Business logic services
├── event/                     # Event handling 
├── general/                   # Business modules
│   ├── dashboard/             # Reporting and analytics
│   ├── hr/                    # Human resources module
│   ├── inventory/             # Inventory management
│   │   ├── controller/        # REST endpoints
│   │   ├── entity/            # Data models
│   │   ├── repository/        # Data access
│   │   ├── service/           # Business logic
│   │   └── service/impl/      # Implementation of service interfaces
│   └── trade/                 # Sales and purchase transactions
│       ├── controller/        # REST endpoints
│       ├── entity/            # Data models
│       ├── repository/        # Data access
│       └── service/           # Business logic
└── util/                      # Utility classes
```

### 2.2 Key Data Models

#### User Administration Models
- **User**: Represents system users with authentication and authorization details
- **UserRole**: Represents roles that can be assigned to users
- **Permission**: Represents access permissions for different system functionalities
- **Module**: Represents system modules that can be accessed with permissions

#### Inventory Models
- **Stock**: Represents inventory items at specific warehouses with quantities and prices
- **Item**: Represents product data with properties, categories, costs, and selling prices
- **Warehouse**: Represents physical locations where stock is stored

#### Sales Models
- **SalesInvoice**: Represents a complete sales transaction including payment details
- **SalesInvoiceRecord**: Represents a line item in a sales invoice with quantity and pricing
- **Customer**: Represents customer information

### 2.3 Key API Endpoints

#### User Administration Endpoints
- `/user/save` - Create or update users
- `/user/findAll` - Get all users
- `/user/findAvailablePermissions` - Get permissions for a user
- `/user/saveDesktopPerms` - Set desktop permissions for a user
- `/role/findAll` - Get all roles
- `/login` - Authenticate users and get JWT token

#### Inventory Endpoints
- `/stock/findAllByWarehouse` - Get stock by warehouse location
- `/stock/transferStock` - Transfer stock between warehouses
- `/stock/adjustStock` - Adjust stock quantities
- `/stock/getDetailReport` - Generate detailed stock report

#### Sales Endpoints
- `/salesInvoice/save` - Create a new sales invoice
- `/salesInvoice/findAllPages` - Retrieve sales invoices (paginated)
- `/salesInvoice/findByInvoiceNo` - Retrieve invoice by number
- `/salesInvoice/payBalance` - Process payment for existing invoice

## 3. Frontend Architecture

### 3.1 Module Organization

The Angular frontend is organized as follows:

```
general-ui/src/app/
├── app-routing.module.ts      # Main routing configuration
├── app.component.*            # Root component
├── error/                     # Error handling components
├── guard/                     # Authentication guards
├── helper/                    # Helper utilities
└── home/                      # Main application
    ├── admin/                 # Admin module
    │   ├── component/         # Admin components
    │   │   ├── create-user/   # User creation component
    │   │   ├── user-management/ # User management interface
    │   │   └── expense-type/  # Expense type management
    │   ├── model/             # Admin-related models
    │   └── service/           # Admin-related services
    ├── core/                  # Core UI components
    ├── custom/                # Business modules
    │   ├── hr/                # Human resources UI
    │   ├── inventory/         # Inventory management UI
    │   │   ├── components/    # UI components
    │   │   ├── model/         # Interface definitions
    │   │   └── service/       # Services for API communication
    │   ├── report/            # Reporting components
    │   └── trade/             # Sales and purchase UI
    │       ├── component/     # UI components
    │       │   ├── cashier/   # CashDrawer operations
    │       │   ├── create-si/ # Sales invoice creation
    │       │   ├── customer/  # Customer management
    │       │   └── invoice-*/ # Various invoice formats
    │       ├── model/         # Interface definitions
    │       └── service/       # Services for API communication
    └── home.component.*       # Main container component
```

### 3.2 Key UI Components

#### Admin Components
- **create-user**: Interface for creating new users with roles and permissions
- **user-management**: Interface for managing existing users
- **user-permissions**: Interface for assigning permissions to users
- **expense-type**: Interface for managing expense types

#### Sales Components
- **create-si**: Main sales interface for creating new invoices
- **manage-si**: Interface for managing existing invoices
- **invoice-80-en**: Invoice printing component (80mm receipt in English)
- **invoice-legal**: Full-page invoice printing component

#### Inventory Components
- **stock-report**: Displays stock summaries across warehouses
- **view-stock**: Interface for viewing and managing stock

### 3.3 Key Services

#### Admin Services
- **UserService**: Manages API calls for user operations
- **RoleService**: Manages API calls for role operations
- **PermissionService**: Manages API calls for permission operations

#### Inventory Services
- **StockService**: Manages API calls for stock operations
- **ItemService**: Manages API calls for item operations

#### Sales Services
- **SalesInvoiceService**: Manages API calls for sales invoice operations
- **CustomerService**: Manages API calls for customer operations

## 4. Key Features

### 4.1 User Administration

#### User Management
- Create, edit, and delete users
- Assign roles and permissions to users
- Set desktop-specific permissions
- Manage module access

#### Authentication & Authorization
- JWT-based authentication
- Role-based access control
- Module-level permissions
- Component-level access control

### 4.2 Inventory Management

#### Stock Management
- Track stock levels across multiple warehouses
- Transfer stock between warehouses
- Adjust stock quantities with audit trail
- Reorder level warnings

#### Reporting
- Generate stock summaries by warehouse
- Detailed stock reports with valuation (cost and sales prices)
- Low stock reports

### 4.3 Sales Processing

#### Invoice Creation
1. Item lookup by barcode or name
2. Quantity and price entry
3. Stock availability checking
4. Discount application (percentage or fixed)
5. Payment processing (cash, card, cheque)
6. Customer selection
7. Invoice printing (multiple formats)

#### Invoice Management
- Search and view past invoices
- Process returns
- Manage payments for existing invoices

### 4.4 Payment Handling
- Cash transactions
- Credit card processing
- Cheque payments with tracking
- Credit sales with balance tracking

## 5. Migration Considerations for Next.js

### 5.1 Architecture Changes

#### Backend Integration
- **API Layer**: Keep Spring Boot backend initially, creating API routes in Next.js to proxy requests
- **Data Fetching**: Replace Angular services with Next.js API routes and React Query/SWR for state management
- **Authentication**: Implement JWT authentication compatible with both systems during transition

#### Frontend Components
- **Component Conversion**: Convert Angular components to React components
- **State Management**: Replace Angular services with React context or state management library
- **Routing**: Implement Next.js file-based routing to replace Angular's router

### 5.2 Key Components to Migrate First

1. **Authentication System**: User login, permissions, and security
2. **Core UI Components**: Layout, navigation, authentication
3. **Stock Management**: Inventory viewing and management
4. **Sales Interface**: Invoice creation workflow
5. **Reporting**: Stock and sales reports

### 5.3 Data Synchronization Strategy

During transition, implement a synchronization mechanism to ensure data consistency between the old and new systems, potentially using:

1. Database-level replication
2. API webhook notifications
3. Scheduled data synchronization jobs

### 5.4 Testing Strategy

1. Implement comprehensive test suite for API endpoints
2. Create UI tests for critical flows (sales process, inventory management)
3. Conduct parallel testing of both systems during transition

## 6. Database Schema

The system uses MongoDB as its database with the following collections:

- **user**: User accounts and authentication info
- **userRole**: Role definitions for authorization
- **permission**: Permission definitions
- **module**: System module definitions
- **stock**: Stock records by warehouse
- **item**: Product information
- **warehouse**: Warehouse locations
- **salesInvoice**: Sales transaction records
- **salesInvoiceRecord**: Line items for sales invoices
- **customer**: Customer information
- **metadata**: System configuration values

This documentation provides a foundation for planning the migration from the existing Angular + Spring Boot system to a Next.js application while maintaining all critical business functionality.
```

You can copy this entire content, create a file named "pos-system-documentation.md" in your project directory, and paste this content into it. This will serve as your comprehensive documentation for the Next.js migration project.