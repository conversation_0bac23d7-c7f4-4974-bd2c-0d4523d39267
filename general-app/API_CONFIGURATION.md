# API Configuration Guide

This document explains how to configure the API endpoints for different environments (development, production, etc.).

## Changing API Endpoints

All API configuration is centralized in the `lib/core/config/app_config.dart` file. To change the API endpoints:

1. Open `lib/core/config/app_config.dart`
2. Update the following values as needed:

```dart
// Development API URL
static const String devApiBaseUrl = 'http://localhost:8080';

// Production API URL
static const String prodApiBaseUrl = 'https://service.viganana.com/general-service-aj-store';

// Set this to false for development, true for production
static const bool isProduction = false;
```

## Switching Between Development and Production

To switch between development and production environments:

1. Open `lib/core/config/app_config.dart`
2. Change the `isProduction` flag:
   - Set to `false` for development
   - Set to `true` for production

## Android App Port Configuration

The default port for the Android app is 8080. To change this:

1. Open `lib/core/config/app_config.dart`
2. Update the `androidAppPort` value:

```dart
static const int androidAppPort = 8080; // Change to your desired port
```

## Testing the Configuration

After changing the API configuration, you should:

1. Run `flutter pub get` to ensure all dependencies are up to date
2. Test the login functionality to verify the API connection
3. Check the console logs to confirm the correct API URL is being used

## Troubleshooting

If you encounter issues after changing the API configuration:

1. Verify the API URL is correct and accessible
2. Check for any network restrictions or firewall issues
3. Ensure the API server is running and accepting connections
4. Look for any CORS issues if testing in a web environment

For additional help, contact the development team.
