#!/bin/bash

# Backup Status Checker Script
# This script helps troubleshoot backup issues

echo "🔍 Backup System Troubleshooting"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if file/directory exists
check_path() {
    if [ -e "$1" ]; then
        echo -e "${GREEN}✅ EXISTS:${NC} $1"
        if [ -f "$1" ]; then
            echo "   📄 File size: $(du -h "$1" | cut -f1)"
            echo "   📅 Modified: $(stat -c %y "$1" 2>/dev/null || stat -f %Sm "$1" 2>/dev/null)"
        elif [ -d "$1" ]; then
            echo "   📁 Directory contents: $(ls -la "$1" | wc -l) items"
        fi
    else
        echo -e "${RED}❌ MISSING:${NC} $1"
    fi
}

# Function to check service status
check_service() {
    echo -e "\n${BLUE}🔧 Checking Service Status${NC}"
    echo "----------------------------"
    
    # Check if Tomcat is running
    if pgrep -f tomcat > /dev/null; then
        echo -e "${GREEN}✅ Tomcat is running${NC}"
        echo "   PID: $(pgrep -f tomcat)"
    else
        echo -e "${RED}❌ Tomcat is not running${NC}"
    fi
    
    # Check if application is responding
    if curl -s -o /dev/null -w "%{http_code}" "http://localhost:8080/actuator/health" | grep -q "200"; then
        echo -e "${GREEN}✅ Application is responding${NC}"
    else
        echo -e "${RED}❌ Application is not responding${NC}"
    fi
}

# Function to check backup API endpoints
check_backup_api() {
    echo -e "\n${BLUE}🌐 Testing Backup API Endpoints${NC}"
    echo "--------------------------------"
    
    # Test backup status endpoint
    echo "Testing backup status..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "http://localhost:8080/api/backup/status" 2>/dev/null)
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ Backup Status API working${NC}"
        echo "   Response: $body" | head -c 200
        echo "..."
    else
        echo -e "${RED}❌ Backup Status API failed (HTTP: $http_code)${NC}"
    fi
    
    # Test backup config endpoint
    echo -e "\nTesting backup config..."
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "http://localhost:8080/api/backup/config" 2>/dev/null)
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ Backup Config API working${NC}"
    else
        echo -e "${RED}❌ Backup Config API failed (HTTP: $http_code)${NC}"
    fi
}

# Function to check recent logs
check_logs() {
    echo -e "\n${BLUE}📋 Checking Recent Logs${NC}"
    echo "------------------------"
    
    # Check for backup-related logs in the last 24 hours
    log_files=(
        "/opt/tomcat11/logs/general-service.log"
        "/opt/tomcat11/logs/catalina.out"
    )
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            echo -e "\n${YELLOW}📄 Checking $log_file${NC}"
            
            # Look for backup-related entries in the last 100 lines
            backup_logs=$(tail -100 "$log_file" | grep -i backup)
            if [ -n "$backup_logs" ]; then
                echo "Recent backup logs found:"
                echo "$backup_logs" | tail -5
            else
                echo "No recent backup logs found"
            fi
            
            # Look for errors in the last 50 lines
            error_logs=$(tail -50 "$log_file" | grep -i error)
            if [ -n "$error_logs" ]; then
                echo -e "${RED}Recent errors found:${NC}"
                echo "$error_logs" | tail -3
            fi
        else
            echo -e "${RED}❌ Log file not found: $log_file${NC}"
        fi
    done
}

# Main execution
echo -e "\n${BLUE}📁 Checking File System${NC}"
echo "------------------------"

# Check backup directories
check_path "/opt/tomcat11/backup"
check_path "/opt/tomcat11/backup/temp-backups"
check_path "/opt/tomcat11/backup/sout-main-439195d6196b.json"

# Check log directories
check_path "/opt/tomcat11/logs"
check_path "/opt/tomcat11/logs/general-service.log"
check_path "/opt/tomcat11/logs/catalina.out"

# Check service status
check_service

# Check backup API
check_backup_api

# Check logs
check_logs

echo -e "\n${BLUE}🎯 Quick Actions${NC}"
echo "----------------"
echo "To trigger manual backup test:"
echo "  curl -X POST 'http://localhost:8080/api/backup/test'"
echo ""
echo "To trigger full manual backup:"
echo "  curl -X POST 'http://localhost:8080/api/backup/manual'"
echo ""
echo "To watch logs in real-time:"
echo "  tail -f /opt/tomcat11/logs/general-service.log"
echo ""
echo "To restart Tomcat:"
echo "  sudo systemctl restart tomcat11"

echo -e "\n${GREEN}✅ Backup troubleshooting check completed!${NC}"
