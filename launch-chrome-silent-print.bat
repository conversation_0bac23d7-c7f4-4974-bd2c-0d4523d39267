@echo off
REM Launch Chrome with kiosk-printing flag to enable silent printing
REM This will bypass the print dialog when window.print() is called

REM Change the path below to match your Chrome installation path
start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" --kiosk-printing http://localhost:4200

echo Chrome launched with silent printing enabled.
echo To use this feature, set useSilentPrint: true in your environment.ts file.
pause
