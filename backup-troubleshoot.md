# Backup Troubleshooting Guide

## Current Backup Configuration

Based on your application.properties, the backup system is configured as follows:

- **Backup Enabled**: `true`
- **Schedule**: Daily at 3:00 AM (`0 0 3 * * ?`)
- **Retention**: 7 days
- **Temp Directory**: `/opt/tomcat11/backup/temp-backups`
- **Google Drive Credentials**: `/opt/tomcat11/backup/sout-main-439195d6196b.json`
- **Google Drive Folder ID**: `1QX-0qNbc9USEMc6zs-fppG5OIR31AjGo`

## Log File Locations

After the recent changes, logs will be written to:
- **Main Log File**: `/opt/tomcat11/logs/general-service.log`
- **Archived Logs**: `/opt/tomcat11/logs/archived/`
- **Tomcat Logs**: `/opt/tomcat11/logs/catalina.out`

## Troubleshooting Steps

### 1. Check Log Files

```bash
# Check if log directory exists
ls -la /opt/tomcat11/logs/

# Check main application log
tail -f /opt/tomcat11/logs/general-service.log

# Check Tomcat startup logs
tail -f /opt/tomcat11/logs/catalina.out

# Search for backup-related logs
grep -i backup /opt/tomcat11/logs/general-service.log
grep -i backup /opt/tomcat11/logs/catalina.out
```

### 2. Check Backup Directories

```bash
# Check if backup temp directory exists
ls -la /opt/tomcat11/backup/
ls -la /opt/tomcat11/backup/temp-backups/

# Check Google Drive credentials file
ls -la /opt/tomcat11/backup/sout-main-439195d6196b.json
```

### 3. Test Backup Manually

You can trigger a manual backup using the REST API:

```bash
# Check backup status
curl -X GET "http://localhost:8080/api/backup/status"

# Trigger manual backup
curl -X POST "http://localhost:8080/api/backup/manual"

# Check backup configuration
curl -X GET "http://localhost:8080/api/backup/config"

# Test backup (creates backup without Google Drive upload)
curl -X POST "http://localhost:8080/api/backup/test"
```

### 4. Check MongoDB Connection

```bash
# Test MongoDB connection
curl -X GET "http://localhost:8080/api/backup/databases"
```

### 5. Common Issues and Solutions

#### Issue 1: Directory Permissions
```bash
# Create directories if they don't exist
sudo mkdir -p /opt/tomcat11/backup/temp-backups
sudo mkdir -p /opt/tomcat11/logs/archived

# Set proper permissions
sudo chown -R tomcat:tomcat /opt/tomcat11/backup/
sudo chown -R tomcat:tomcat /opt/tomcat11/logs/
sudo chmod -R 755 /opt/tomcat11/backup/
sudo chmod -R 755 /opt/tomcat11/logs/
```

#### Issue 2: Google Drive Credentials
```bash
# Check if credentials file exists and is readable
sudo ls -la /opt/tomcat11/backup/sout-main-439195d6196b.json
sudo cat /opt/tomcat11/backup/sout-main-439195d6196b.json | head -5
```

#### Issue 3: MongoDB Access
The backup service needs to list all databases. Check if the MongoDB user has proper permissions:
```bash
# Connect to MongoDB and test
mongo --host *************:27017 --authenticationDatabase admin -u generalWeb -p
# Then run: show dbs
```

### 6. Enable Debug Logging

The logback.xml has been updated to enable DEBUG level logging for backup services. After restarting Tomcat, you should see detailed backup logs.

### 7. Restart Tomcat

After making the logging changes, restart Tomcat to apply the new configuration:
```bash
sudo systemctl restart tomcat11
# or
sudo /opt/tomcat11/bin/shutdown.sh
sudo /opt/tomcat11/bin/startup.sh
```

## Expected Log Messages

When backup runs successfully, you should see logs like:
```
🚀 Starting MongoDB backup at 2024-01-15_03-00-00
📋 Found X databases to backup: [generalWebDemo, generalWebWanigarathna, ...]
📦 Creating MongoDB backup...
☁️ Uploading backup to Google Drive...
🗑️ Cleaning up old backups (keeping 7 days)...
✅ Scheduled backup completed successfully!
```

When backup fails, you should see:
```
❌ Scheduled backup failed: [error message]
```

## Next Steps

1. Restart Tomcat to apply logging changes
2. Check if log files are being created
3. Trigger a manual backup test
4. Check the logs for any error messages
5. Verify directory permissions and file access
