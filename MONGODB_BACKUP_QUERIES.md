# 🔧 MongoDB Admin User - Quick Reference Queries

## 🚀 Essential Setup Queries

### **1. <PERSON> Permissions to Existing Admin User**

```javascript
use admin

// Grant listDatabases permission for backup discovery
db.grantRolesToUser("generalWeb", [
  { role: "listDatabases", db: "admin" }
])

// Grant readWrite access to all tenant databases
db.grantRolesToUser("generalWeb", [
  { role: "readWrite", db: "generalWebDemo" },
  { role: "readWrite", db: "generalWebNewcitymobile" },
  { role: "readWrite", db: "generalWebWanigarathna" }
])
```

### **2. Alternative: Grant Broad Admin Access (Simpler)**

```javascript
use admin

// Grant comprehensive admin permissions (includes listDatabases)
db.grantRolesToUser("generalWeb", [
  { role: "readWriteAnyDatabase", db: "admin" }
])
```

### **3. Verify User Permissions**

```javascript
use admin
db.getUser("generalWeb")
```

### **4. Test Database Listing**

```javascript
use admin
db.runCommand({listDatabases: 1})
```

### **5. Test Tenant Database Access**

```javascript
["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"].forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    print(dbName + ": " + (result.ok ? "✅ OK" : "❌ FAILED"));
  } catch(e) {
    print(dbName + ": ❌ ERROR - " + e.message);
  }
});
```

## 🔄 Adding New Tenant Database

```javascript
use admin
db.grantRolesToUser("generalWeb", [
  { role: "readWrite", db: "generalWebNewTenant" }
])
```

## 🧪 Testing Queries

### **Test generalWeb User Login**

```bash
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb
```

### **Test Database Discovery**

```javascript
use admin
var dbs = db.runCommand({listDatabases: 1});
var tenantDbs = dbs.databases.filter(db => db.name.startsWith('generalWeb'));
print("Found " + tenantDbs.length + " tenant databases:");
tenantDbs.forEach(db => print("  - " + db.name));
```

## 🔧 Alternative: Custom Role Approach

### **Create Custom Role for generalWeb User**

```javascript
use admin

db.createRole({
  role: "multiTenantRole",
  privileges: [
    {
      resource: { cluster: true },
      actions: ["listDatabases"]
    },
    {
      resource: { db: "generalWeb", collection: "" },
      actions: ["find", "insert", "update", "remove", "listCollections"]
    },
    {
      resource: { db: "generalWebDemo", collection: "" },
      actions: ["find", "insert", "update", "remove", "listCollections"]
    },
    {
      resource: { db: "generalWebNewcitymobile", collection: "" },
      actions: ["find", "insert", "update", "remove", "listCollections"]
    },
    {
      resource: { db: "generalWebWanigarathna", collection: "" },
      actions: ["find", "insert", "update", "remove", "listCollections"]
    }
  ],
  roles: []
})
```

### **Grant Custom Role to generalWeb User**

```javascript
use admin

db.grantRolesToUser("generalWeb", [
  { role: "multiTenantRole", db: "admin" }
])
```

## 🚨 Troubleshooting Queries

### **Check User Permissions**

```javascript
use admin
db.runCommand({usersInfo: "generalWeb", showPrivileges: true})
```

### **Test Connection Status**

```javascript
db.runCommand({connectionStatus: 1})
```

### **Debug Database Access**

```javascript
use generalWebDemo
try {
  db.runCommand({listCollections: 1})
  print("✅ Can list collections")
} catch(e) {
  print("❌ Cannot list collections: " + e.message)
}
```

## 📋 Quick Commands Summary

```bash
# Connect as admin
mongo --host localhost:27017 -u admin -p --authenticationDatabase admin

# Connect as generalWeb user
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb

# Test mongodump with generalWeb user
mongodump --host localhost:27017 --username generalWeb --password "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb --db generalWebDemo --out ./test-backup
```

## ⚙️ Application Properties (Already Configured)

```properties
# Current configuration in application.properties
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.authDatabase=generalWeb
spring.data.mongodb.username=generalWeb
spring.data.mongodb.password=awer@#$cdfDDF!@S_+(

# MongoDB configuration for tenant-specific databases
mongodb.host=localhost
mongodb.port=27017
mongodb.username=generalWeb
mongodb.password=awer@#$cdfDDF!@S_+(
```

## 🔐 Security Reminders

- ✅ Keep admin password secure in application.properties
- ✅ Limit permissions to only what's needed
- ✅ Regular permission audits
- ✅ Document all changes
- ✅ Use existing admin user for both app and backup operations
- ❌ Don't expose credentials in logs or code
