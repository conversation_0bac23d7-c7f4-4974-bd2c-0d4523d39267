# 🚀 MongoDB Quick Fix - Essential Queries

## 🎯 Problem
Your backup system can't list databases because the `generalWeb` user lacks `listDatabases` permission.

## ✅ Solution
Grant the necessary permissions to your existing `generalWeb` admin user.

## 📋 Quick Fix Queries

### **Option 1: Grant Specific Permissions (Recommended)**

```javascript
// Connect to MongoDB as admin
mongo --host localhost:27017 -u admin -p --authenticationDatabase admin

// Run these commands:
use admin

// Grant listDatabases permission
db.grantRolesToUser("generalWeb", [
  { role: "listDatabases", db: "admin" }
])

// Grant access to all tenant databases
db.grantRolesToUser("generalWeb", [
  { role: "readWrite", db: "generalWebDemo" },
  { role: "readWrite", db: "generalWebNewcitymobile" },
  { role: "readWrite", db: "generalWebWanigarathna" }
])
```

### **Option 2: Grant Broad Access (Simpler)**

```javascript
// Connect to MongoDB as admin
mongo --host localhost:27017 -u admin -p --authenticationDatabase admin

// Run this command:
use admin

// Grant comprehensive admin permissions
db.grantRolesToUser("generalWeb", [
  { role: "readWriteAnyDatabase", db: "admin" }
])
```

## 🧪 Test the Fix

```javascript
// Test database listing
use admin
db.runCommand({listDatabases: 1})

// Test tenant database access
["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"].forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    print(dbName + ": " + (result.ok ? "✅ OK" : "❌ FAILED"));
  } catch(e) {
    print(dbName + ": ❌ ERROR - " + e.message);
  }
});
```

## 🔄 Adding New Tenant Databases

When you add a new tenant database in the future:

```javascript
use admin
db.grantRolesToUser("generalWeb", [
  { role: "readWrite", db: "generalWebNewTenant" }
])
```

## ✅ That's It!

No application code changes needed. Your backup system should now work with the existing `generalWeb` user and password from `application.properties`.

## 🔍 Verify Everything Works

1. Run the permission queries above
2. Test your backup system
3. Check that database discovery works
4. Verify backup creation succeeds

Your existing configuration in `application.properties` already has the correct credentials:
- Username: `generalWeb`
- Password: `awer@#$cdfDDF!@S_+(`
- Auth Database: `generalWeb`
