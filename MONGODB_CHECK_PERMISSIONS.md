# 🔍 Check generalWeb User Permissions

## 🎯 Let's verify if generalWeb has the required permissions

### **Step 1: Connect as generalWeb User**

```bash
# Connect as generalWeb user
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb
```

### **Step 2: Check User Details and Roles**

```javascript
use admin

// Check what roles generalWeb has
db.runCommand({connectionStatus: 1})

// Get detailed user information
db.runCommand({usersInfo: "generalWeb", showPrivileges: true})
```

### **Step 3: Test listDatabases Permission**

```javascript
use admin

// Test if listDatabases works
try {
  var result = db.runCommand({listDatabases: 1});
  print("✅ SUCCESS: listDatabases works!");
  print("Total databases found: " + result.databases.length);
  
  // Show all databases
  result.databases.forEach(function(db) {
    print("  - " + db.name + " (size: " + db.sizeOnDisk + ")");
  });
  
  // Filter for tenant databases
  var tenantDbs = result.databases.filter(db => db.name.startsWith('generalWeb'));
  print("\n🎯 Tenant databases found: " + tenantDbs.length);
  tenantDbs.forEach(db => print("  ✅ " + db.name));
  
} catch(e) {
  print("❌ FAILED: listDatabases error: " + e.message);
  print("Error code: " + e.code);
}
```

### **Step 4: Test Tenant Database Access**

```javascript
// Test access to each tenant database
var tenantDatabases = ["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"];

print("\n🧪 Testing access to tenant databases:");
tenantDatabases.forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    if (result.ok) {
      print("✅ " + dbName + ": ACCESSIBLE");
      
      // Try to list collections
      try {
        var collections = db.getSiblingDB(dbName).runCommand({listCollections: 1});
        print("   📁 Collections: " + collections.cursor.firstBatch.length);
      } catch(collError) {
        print("   ⚠️ Cannot list collections: " + collError.message);
      }
    } else {
      print("❌ " + dbName + ": FAILED - " + JSON.stringify(result));
    }
  } catch(e) {
    print("❌ " + dbName + ": ERROR - " + e.message);
  }
});
```

### **Step 5: Alternative - Connect as Admin and Check generalWeb User**

```bash
# Connect as admin user
mongo --host localhost:27017 -u admin -p --authenticationDatabase admin
```

```javascript
use admin

// Check generalWeb user details
print("=== generalWeb User Information ===");
var userInfo = db.getUser("generalWeb");
if (userInfo) {
  print("✅ User exists");
  print("Roles:");
  userInfo.roles.forEach(function(role) {
    print("  - Role: " + role.role + " on database: " + role.db);
  });
} else {
  print("❌ User not found");
}

// Check if user has readWriteAnyDatabase role
var hasReadWriteAny = userInfo.roles.some(role => role.role === "readWriteAnyDatabase");
print("\nreadWriteAnyDatabase role: " + (hasReadWriteAny ? "✅ YES" : "❌ NO"));

// Test listDatabases as admin
print("\n=== Testing listDatabases as admin ===");
try {
  var dbs = db.runCommand({listDatabases: 1});
  print("✅ Admin can list " + dbs.databases.length + " databases");
} catch(e) {
  print("❌ Admin cannot list databases: " + e.message);
}
```

### **Step 6: Check Authentication Database Issue**

The issue might be the authentication database. Let's test both:

```bash
# Test 1: Connect with generalWeb as auth database (current config)
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb --eval "print('Auth DB: generalWeb'); db.runCommand({listDatabases: 1})"

# Test 2: Connect with admin as auth database
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase admin --eval "print('Auth DB: admin'); db.runCommand({listDatabases: 1})"
```

## 🔧 If Permissions Are Missing

If the tests show missing permissions, run this as admin:

```javascript
use admin

// Grant comprehensive permissions
db.grantRolesToUser("generalWeb", [
  { role: "readWriteAnyDatabase", db: "admin" },
  { role: "listDatabases", db: "admin" },
  { role: "dbAdminAnyDatabase", db: "admin" }
])

print("✅ Additional permissions granted");
```

## 📋 Quick One-Line Test

```bash
# Quick test - should return list of databases
mongo --host localhost:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase generalWeb --quiet --eval "db.runCommand({listDatabases: 1}).databases.forEach(db => print(db.name))"
```

## 🎯 Expected Results

If everything is working correctly, you should see:
- ✅ listDatabases command succeeds
- ✅ Multiple databases listed (including generalWeb, generalWebDemo, etc.)
- ✅ Access to all tenant databases
- ✅ No permission errors

Run these tests and let me know what output you get!
