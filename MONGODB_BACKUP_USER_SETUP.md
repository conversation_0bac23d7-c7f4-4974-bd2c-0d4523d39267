# 🔧 MongoDB Admin User Setup Guide

## 📋 Quick Setup

This guide provides step-by-step instructions to configure the existing MongoDB admin user for backup operations.

## 🎯 Problem Statement

The current backup system fails because:
- The main application user (`generalWeb`) lacks `listDatabases` permission
- Need to grant additional permissions to the existing admin user

## ✅ Solution: Use Existing Admin User

Since `generalWeb` is already configured as an admin user with password `awer@#$cdfDDF!@S_+(` in `application.properties`, we can simply grant it the additional permissions needed for backup operations.

## 🚀 Step-by-Step Setup

### **Step 1: Connect to MongoDB as Admin**

```bash
# Connect to MongoDB with admin privileges
mongo --host localhost:27017 -u admin -p --authenticationDatabase admin
```

### **Step 2: Grant Additional Permissions to generalWeb User**

```javascript
use admin

// Grant listDatabases permission for backup discovery
db.grantRolesToUser("generalWeb", [
  { role: "listDatabases", db: "admin" }
])

// Grant readWrite access to all tenant databases
db.grantRolesToUser("generalWeb", [
  { role: "readWrite", db: "generalWebDemo" },
  { role: "readWrite", db: "generalWebNewcitymobile" },
  { role: "readWrite", db: "generalWebWanigarathna" }
  // Add more tenant databases as needed
])
```

### **Alternative: Grant Broad Admin Access (Simpler)**

```javascript
use admin

// Grant comprehensive admin permissions (includes listDatabases)
db.grantRolesToUser("generalWeb", [
  { role: "readWriteAnyDatabase", db: "admin" }
])
```

### **Step 3: Verify generalWeb User Permissions**

```javascript
// Check user permissions
db.getUser("generalWeb")

// Test listDatabases permission
db.runCommand({listDatabases: 1})

// Test access to tenant databases
use generalWebDemo
db.runCommand({ping: 1})
```

### **Step 4: Test Database Discovery**

```javascript
// Test the generalWeb user can discover tenant databases
use admin

var dbs = db.runCommand({listDatabases: 1});
print("Total databases: " + dbs.databases.length);

// Filter for tenant databases
var tenantDbs = dbs.databases.filter(db => db.name.startsWith('generalWeb'));
print("Tenant databases found: " + tenantDbs.length);
tenantDbs.forEach(db => print("  ✅ " + db.name));
```

## ⚙️ Application Configuration

### **Current application.properties (No Changes Needed)**

```properties
# Current configuration - already set up correctly
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.authDatabase=generalWeb
spring.data.mongodb.username=generalWeb
spring.data.mongodb.password=awer@#$cdfDDF!@S_+(

# MongoDB configuration for tenant-specific databases
mongodb.host=localhost
mongodb.port=27017
mongodb.username=generalWeb
mongodb.password=awer@#$cdfDDF!@S_+(
```

### **Backup Service Configuration**

The backup service already uses the correct configuration from `application.properties`:

```java
@Value("${spring.data.mongodb.username:#{null}}")
private String mongoUsername;

@Value("${spring.data.mongodb.password:#{null}}")
private String mongoPassword;

@Value("${spring.data.mongodb.authDatabase:admin}")
private String authDatabase;
```

## 🔄 Adding New Tenant Databases

When you add a new tenant database, grant access to the backup user:

```javascript
use admin

// Grant readWrite access to new tenant database
db.grantRolesToUser("backupUser", [
  { role: "readWrite", db: "generalWebNewTenant" }
])

// Verify access
use generalWebNewTenant
db.runCommand({ping: 1})
```

## 🧪 Testing Commands

### **Test Backup User Login**

```bash
# Test backup user can connect
mongo --host localhost:27017 -u backupUser -p SecureBackupPassword123! --authenticationDatabase admin
```

### **Test Database Access**

```javascript
// Test all tenant databases are accessible
["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"].forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    print(dbName + ": " + (result.ok ? "✅ ACCESSIBLE" : "❌ FAILED"));
  } catch(e) {
    print(dbName + ": ❌ ERROR - " + e.message);
  }
});
```

## 🔐 Security Notes

1. **Strong Password**: Use a complex password for the backup user
2. **Limited Scope**: Backup user only has access to tenant databases
3. **No Admin Rights**: Backup user cannot create/drop databases or users
4. **Audit Trail**: All backup user actions are logged

## 🚨 Troubleshooting

### **Issue: "not authorized" error**
```javascript
// Check user roles
use admin
db.getUser("backupUser")

// Verify listDatabases permission
db.runCommand({listDatabases: 1})
```

### **Issue: Cannot access tenant database**
```javascript
// Grant missing database access
use admin
db.grantRolesToUser("backupUser", [
  { role: "readWrite", db: "missingDatabaseName" }
])
```

### **Issue: Backup discovery returns empty list**
```javascript
// Test database listing manually
use admin
var result = db.runCommand({listDatabases: 1});
print("Command result: " + JSON.stringify(result, null, 2));
```

## ✅ Verification Checklist

- [ ] Backup user created successfully
- [ ] User can list all databases
- [ ] User can access all tenant databases
- [ ] Application configuration updated
- [ ] Backup service uses new credentials
- [ ] Automated backup discovery works
- [ ] Manual backup test successful

## 📚 Related Documentation

- [MONGODB_MULTI_TENANT_SETUP.md](./MONGODB_MULTI_TENANT_SETUP.md) - Complete multi-tenant setup
- [MongoDB User Management](https://docs.mongodb.com/manual/tutorial/manage-users-and-roles/)
- [MongoDB Built-in Roles](https://docs.mongodb.com/manual/reference/built-in-roles/)
