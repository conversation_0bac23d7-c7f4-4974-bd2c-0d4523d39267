Multi-Tenancy Transformation Guide (Spring Boot & MongoDB)This guide outlines the steps to convert your existing Spring Boot application from a "one backend per tenant" to a "single backend serving multiple tenants," with each tenant having its own dedicated MongoDB database. This transformation focuses on maintaining data isolation per tenant while consolidating your backend infrastructure.1. Understanding the Core StrategyYour current setup of "one MongoDB database per tenant" is an excellent foundation. The primary objective is to implement a mechanism within your single Spring Boot backend that can identify the specific tenant from an incoming request (via subdomain) and then dynamically route all subsequent MongoDB operations for that request to the correct tenant's database.2. Key Components to Implement in Spring Boot2.1. Tenant Context ManagementThis component provides a thread-safe way to store the current tenant's identifier (ID) for the duration of a request.Action: Create a static utility class to hold the tenant ID using ThreadLocal.File: src/main/java/com/yourcompany/yourapp/multitenancy/TenantContext.javaCode:package com.yourcompany.yourapp.multitenancy;

/**
* Utility class to hold the current tenant ID using ThreadLocal.
* This ensures that the tenant ID is accessible throughout the request's lifecycle.
  */
  public class TenantContext {
  // ThreadLocal to store the tenant ID for the current thread (request).
  private static final ThreadLocal<String> currentTenant = new ThreadLocal<>();

  /**
    * Sets the tenant ID for the current thread.
    * @param tenantId The ID of the current tenant.
      */
      public static void setCurrentTenant(String tenantId) {
      currentTenant.set(tenantId);
      }

  /**
    * Retrieves the tenant ID for the current thread.
    * @return The current tenant ID, or null if not set.
      */
      public static String getCurrentTenant() {
      return currentTenant.get();
      }

  /**
    * Clears the tenant ID from the current thread's ThreadLocal.
    * This is crucial to prevent memory leaks and ensure tenant isolation across requests.
      */
      public static void clear() {
      currentTenant.remove();
      }
      }
      2.2. Tenant Identifier Resolution FilterThis Spring filter will intercept every incoming HTTP request, extract the tenant ID from the request's host header (subdomain), and then set it in the TenantContext.Action: Create a Spring OncePerRequestFilter to extract the tenant ID.File: src/main/java/com/yourcompany/yourapp/multitenancy/TenantFilter.javaCode:package com.yourcompany.yourapp.multitenancy;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
* Spring filter to extract the tenant ID from the request's host header (subdomain).
* It then sets the tenant ID in the TenantContext for the duration of the request.
  */
  @Component
  public class TenantFilter extends OncePerRequestFilter {

  // Define the common domain suffix to correctly extract the tenant ID.
  // Example: for abc.viganana.com, the suffix is ".viganana.com".
  private static final String DOMAIN_SUFFIX = ".viganana.com";

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
  throws ServletException, IOException {
  // Get the server name from the request (e.g., "abc.viganana.com").
  String host = request.getServerName();

       // Extract the tenant ID from the host.
       String tenantId = extractTenantIdFromHost(host);

       // Set the extracted tenant ID in the TenantContext.
       // If no tenant ID is found, you might set a "default" tenant or handle it as an error.
       if (tenantId != null && !tenantId.isEmpty()) {
           TenantContext.setCurrentTenant(tenantId);
       } else {
           // If a request comes in without a tenant-specific subdomain (e.g., direct IP access,
           // or a shared admin URL), you might route it to a default tenant database.
           // Replace "default" with an actual default tenant ID or handle specific logic.
           TenantContext.setCurrentTenant("default");
       }

       try {
           // Continue the filter chain.
           filterChain.doFilter(request, response);
       } finally {
           // IMPORTANT: Clear the ThreadLocal variable after the request has been processed.
           // This prevents tenant ID leakage to subsequent requests processed by the same thread.
           TenantContext.clear();
       }
  }

  /**
    * Extracts the tenant ID from the given host string.
    * Assumes tenant ID is the subdomain before the common domain suffix.
    * @param host The host string from the request (e.g., "abc.viganana.com").
    * @return The extracted tenant ID (e.g., "abc"), or null if not found.
      */
      private String extractTenantIdFromHost(String host) {
      if (host != null && host.endsWith(DOMAIN_SUFFIX)) {
      // Remove the domain suffix to get the tenant ID.
      return host.substring(0, host.length() - DOMAIN_SUFFIX.length());
      }
      return null; // Host does not match the expected pattern.
      }
      }
      2.3. Dynamic MongoDB Database FactoryThis is the core component for MongoDB multi-tenancy. It extends Spring Data MongoDB's SimpleMongoClientDatabaseFactory and overrides the method to provide the correct MongoDatabase instance based on the tenant ID from TenantContext.Action: Create a custom MongoDatabaseFactory.File: src/main/java/com/yourcompany/yourapp/multitenancy/MultiTenantMongoDbFactory.javaCode:package com.yourcompany.yourapp.multitenancy;

import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoDatabase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.dao.DataAccessException;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
* A custom MongoDB Database Factory that routes to different databases
* based on the tenant ID stored in the TenantContext.
  */
  public class MultiTenantMongoDbFactory extends SimpleMongoClientDatabaseFactory implements InitializingBean {

  private final String defaultDatabaseName;
  // This map stores the mapping from a tenant's identifier (e.g., "abc")
  // to their actual MongoDB database name (e.g., "abc_db").
  private final Map<String, String> tenantDatabaseNames = new ConcurrentHashMap<>();

  /**
    * Constructor to initialize the factory.
    * @param mongoClientSettings MongoDB client settings.
    * @param defaultDatabaseName The database name to use if no tenant is identified.
      */
      public MultiTenantMongoDbFactory(MongoClientSettings mongoClientSettings, String defaultDatabaseName) {
      super(mongoClientSettings, defaultDatabaseName);
      this.defaultDatabaseName = defaultDatabaseName;
      }

  @Override
  public MongoDatabase getMongoDatabase() throws DataAccessException {
  // Retrieve the current tenant ID from the TenantContext.
  String tenantId = TenantContext.getCurrentTenant();

       // Determine the database name based on the tenant ID.
       // If the tenant ID is not found in the map, or if tenantId is null,
       // fall back to the default database.
       String databaseName = tenantDatabaseNames.getOrDefault(tenantId, defaultDatabaseName);

       // Get the MongoDB database instance for the determined name.
       return getMongoClient().getDatabase(databaseName);
  }

  @Override
  public MongoDatabase getMongoDatabase(String dbName) throws DataAccessException {
  // This method is part of the interface; typically, you'd route based on the provided dbName.
  // For multi-tenancy, the no-argument getMongoDatabase() is usually invoked by Spring Data.
  return getMongoClient().getDatabase(dbName);
  }

  /**
    * Lifecycle method called by Spring after properties are set.
    * Use this to populate your tenant-to-database mappings.
      */
      @Override
      public void afterPropertiesSet() {
      // **IMPORTANT:** Populate this 'tenantDatabaseNames' map with your actual tenant-to-database mappings.
      // This data needs to be loaded from a reliable source. Examples include:
      // 1. A configuration file (e.g., YAML/properties, JSON).
      // 2. A dedicated "master" MongoDB database that stores tenant metadata.
      // 3. Environment variables.

      // --- Example Population (Replace with your actual logic) ---
      // For tenant 'abc' requests (from abc.viganana.com), connect to 'abc_db'
      tenantDatabaseNames.put("abc", "abc_db");
      // For tenant 'def' requests (from def.viganana.com), connect to 'def_db'
      tenantDatabaseNames.put("def", "def_db");
      // Add mappings for all your other tenants here.
      // tenantDatabaseNames.put("tenant_id_g", "db_name_g");

      // Ensure a mapping for the "default" tenant if you use one.
      tenantDatabaseNames.put("default", defaultDatabaseName);
      // --- End Example Population ---
      }

  /**
    * Allows dynamic addition of a new tenant's database mapping at runtime.
    * Useful for tenant onboarding processes without requiring an application restart.
    * @param tenantId The unique identifier of the new tenant.
    * @param databaseName The MongoDB database name for this tenant.
      */
      public void addTenantDatabase(String tenantId, String databaseName) {
      tenantDatabaseNames.put(tenantId, databaseName);
      }

  /**
    * Allows dynamic removal of a tenant's database mapping at runtime.
    * @param tenantId The unique identifier of the tenant to remove.
      */
      public void removeTenantDatabase(String tenantId) {
      tenantDatabaseNames.remove(tenantId);
      }
      }
      2.4. Spring Data MongoDB ConfigurationThis configuration class wires up the MongoClientSettings, your MultiTenantMongoDbFactory, and the MongoTemplate bean, ensuring that Spring Data repositories use your custom factory.Action: Create a Spring @Configuration class.File: src/main/java/com/yourcompany/yourapp/config/MongoMultiTenancyConfig.javaCode:package com.yourcompany.yourapp.config;

import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.yourcompany.yourapp.multitenancy.MultiTenantMongoDbFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;

import java.util.Collections;

/**
* Spring configuration for MongoDB multi-tenancy.
* This class sets up the MongoClientSettings and registers the custom MultiTenantMongoDbFactory
* to enable dynamic database switching based on the current tenant.
  */
  @Configuration
  public class MongoMultiTenancyConfig {

  // Inject MongoDB connection properties from application.properties/yml
  @Value("${spring.data.mongodb.host:localhost}")
  private String mongoHost;

  @Value("${spring.data.mongodb.port:27017}")
  private int mongoPort;

  @Value("${spring.data.mongodb.username:}") // Optional: MongoDB username for authentication
  private String mongoUsername;

  @Value("${spring.data.mongodb.password:}") // Optional: MongoDB password for authentication
  private String mongoPassword;

  @Value("${spring.data.mongodb.database:default_master_db}") // Default/master DB name
  private String defaultDatabaseName;

  /**
    * Configures the MongoClientSettings, including the server address and optional credentials.
    * @return Configured MongoClientSettings.
      */
      @Bean
      public MongoClientSettings mongoClientSettings() {
      MongoClientSettings.Builder builder = MongoClientSettings.builder()
      .applyToClusterSettings(b -> b.hosts(Collections.singletonList(new ServerAddress(mongoHost, mongoPort))));

      // Apply MongoDB authentication credentials if provided in properties.
      if (!mongoUsername.isEmpty() && !mongoPassword.isEmpty()) {
      builder.credential(MongoCredential.createCredential(mongoUsername, defaultDatabaseName, mongoPassword.toCharArray()));
      }

      return builder.build();
      }

  /**
    * Registers the custom MultiTenantMongoDbFactory as the primary MongoDatabaseFactory.
    * Spring Data MongoDB will use this factory to obtain the correct database for each operation.
    * @param mongoClientSettings The configured MongoClientSettings.
    * @return An instance of MultiTenantMongoDbFactory.
      */
      @Bean
      public MongoDatabaseFactory mongoDbFactory(MongoClientSettings mongoClientSettings) {
      return new MultiTenantMongoDbFactory(mongoClientSettings, defaultDatabaseName);
      }

  /**
    * Creates the MongoTemplate bean, which is the central class for MongoDB operations.
    * It uses the custom MongoDatabaseFactory to ensure multi-tenancy.
    * Spring Data MongoDB repositories will automatically use this MongoTemplate.
    * @param mongoDbFactory The multi-tenant MongoDB factory.
    * @param converter The MappingMongoConverter (auto-configured by Spring Boot).
    * @return An instance of MongoTemplate.
      */
      @Bean
      public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDbFactory, MappingMongoConverter converter) {
      return new MongoTemplate(mongoDbFactory, converter);
      }
      }
      2.5. CORS Configuration (Crucial for Frontend)Since your Angular frontend instances are hosted on different subdomains (abc.viganana.com, def.viganana.com), and your single backend might be on a different host or subdomain (e.g., api.viganana.com), you must configure Cross-Origin Resource Sharing (CORS) in your Spring Boot application.Action: Configure a Spring CORS filter to allow requests from your tenant subdomains.File: src/main/java/com/yourcompany/yourapp/config/CorsConfig.javaCode:package com.yourcompany.yourapp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
* Configuration for Cross-Origin Resource Sharing (CORS).
* This allows your Angular frontend applications on different subdomains to
* make requests to your single Spring Boot backend.
  */
  @Configuration
  public class CorsConfig {

  @Bean
  public CorsFilter corsFilter() {
  UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
  CorsConfiguration config = new CorsConfiguration();

       // Allow sending of cookies and authentication headers (e.g., JWT).
       config.setAllowCredentials(true);

       // Specify allowed origins. Using a pattern to allow all subdomains of 'viganana.com'.
       // IMPORTANT: Adjust this pattern to match your actual domain.
       config.addAllowedOriginPattern("*.viganana.com");

       // Allow all common HTTP headers.
       config.addAllowedHeader("*");

       // Allow all standard HTTP methods (GET, POST, PUT, DELETE, etc.).
       config.addAllowedMethod("*");

       // Register the CORS configuration for all paths ("/**").
       source.registerCorsConfiguration("/**", config);
       return new CorsFilter(source);
  }
  }
3. Update Application PropertiesConfigure your src/main/resources/application.properties (or application.yml) file with the base MongoDB connection details that your MongoMultiTenancyConfig will use.Action: Add or modify MongoDB connection properties.File: src/main/resources/application.propertiesCode:# Core MongoDB connection details for your single MongoDB server.
# Replace 'your_mongodb_server_ip_or_hostname' with the actual IP address or hostname of your MongoDB server.
spring.data.mongodb.host=your_mongodb_server_ip_or_hostname
spring.data.mongodb.port=27017

# This database name will be used as the default fallback if no tenant is identified,
# or it can be a 'master' database for shared application-wide data (e.g., tenant metadata).
spring.data.mongodb.database=master_app_db

# Optional: Uncomment and fill if your MongoDB requires authentication.
# spring.data.mongodb.username=your_mongodb_username
# spring.data.mongodb.password=your_mongodb_password

# Server port for your Spring Boot application (e.g., if running on Tomcat).
server.port=8080
4. Frontend (Angular) and Infrastructure (Cloudflare/VPS)This section details the necessary configurations outside the Spring Boot application to ensure the multi-tenancy setup functions end-to-end.4.1. Cloudflare DNS / VPS Reverse Proxy ConfigurationFor your single Spring Boot backend to correctly identify tenants based on subdomains, your network infrastructure needs to route all tenant-specific subdomain requests to your single backend instance, crucially preserving the original Host header.Action: Configure your Cloudflare DNS and/or your VPS reverse proxy (e.g., Nginx, Apache) accordingly.Cloudflare DNS:Create A records (or CNAMEs) for each tenant subdomain (abc.viganana.com, def.viganana.com, etc.) pointing to the public IP address of your VPS where the Spring Boot backend is hosted.Ensure any Cloudflare proxying (orange cloud) is configured to forward all necessary headers, especially the Host header, to your origin server.Nginx Example (on your VPS, acting as a reverse proxy to Tomcat):This configuration catches all requests for *.viganana.com and forwards them to your Tomcat server (assumed to be running on localhost:8080), ensuring the original Host header is passed along.server {
   listen 80;
   listen [::]:80;
   server_name *.viganana.com; # This directive catches all subdomains under viganana.com

   location / {
   # Proxy requests to your Spring Boot application (e.g., running on Tomcat at port 8080).
   proxy_pass http://localhost:8080;

        # IMPORTANT: This line ensures the original Host header (e.g., abc.viganana.com)
        # is passed to your Spring Boot application's TenantFilter.
        proxy_set_header Host $host;

        # Standard headers for proxying
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Optional: Adjust client body size, timeouts etc.
        client_max_body_size 100M;
        proxy_connect_timeout 90s;
        proxy_send_timeout 90s;
        proxy_read_timeout 90s;
   }

   # If you also have a main domain like viganana.com for a shared admin panel or landing page
   # you might add another server block or a specific location block for it.
   # Example:
   # server {
   #     listen 80;
   #     server_name viganana.com;
   #     location / {
   #         proxy_pass http://localhost:8080;
   #         proxy_set_header Host $host;
   #         # ... other proxy headers
   #     }
   # }
}
4.2. Angular Frontend API CallsAction: Ensure your Angular applications are configured to make API calls to a backend URL that uses the same tenant-specific subdomain as the frontend itself.Example:If your Angular app is accessed at https://abc.viganana.com, its API calls should be directed to https://abc.viganana.com/api/... (assuming your Spring Boot backend handles requests on this path).This simplifies the setup as the Host header will naturally contain the tenant's subdomain.5. Testing and ValidationThorough testing is crucial to ensure tenant data isolation and correct routing.Action: Deploy your single Spring Boot backend and perform comprehensive tests.Checklist:Deployment: Successfully deploy the single Spring Boot JAR/WAR to your Tomcat server on the VPS.Access Tenant 1: Open https://abc.viganana.com in your browser. Perform operations (e.g., create, read, update data).Verify Tenant 1 Data: Using a MongoDB client, connect to abc_db and confirm that the data created via abc.viganana.com is present ONLY in abc_db and not in def_db or master_app_db.Access Tenant 2: Open https://def.viganana.com in your browser. Perform similar operations.Verify Tenant 2 Data: Connect to def_db and confirm that data created via def.viganana.com is present ONLY in def_db. Ensure it cannot access or modify data in abc_db.Edge Cases:Try accessing the backend via a non-tenant-specific URL (e.g., directly your_vps_ip:8080/api/... if exposed, or viganana.com/api/... if configured). Verify it routes to your master_app_db or handles the "default" tenant appropriately.Attempt to forge a request by manually setting the Host header to another tenant's domain (e.g., from abc.viganana.com, send a request with Host: def.viganana.com). While the TenantFilter might pick it up, ensure your application-level security and authentication prevent unauthorized cross-tenant access.Monitoring: Monitor your Spring Boot application logs and MongoDB server logs to observe the database connections being established and queries being executed against the correct tenant databases.6. Post-Transformation ConsiderationsTenant Onboarding Process:You will need to develop a process for onboarding new tenants. This involves:Creating a new MongoDB database for the new tenant (e.g., newtenant_db).Populating any initial schema/data in newtenant_db.Dynamically adding the newtenant_id -> newtenant_db mapping to the tenantDatabaseNames map in your MultiTenantMongoDbFactory (using the addTenantDatabase method).Configuring the new subdomain (newtenant.viganana.com) in Cloudflare DNS.Shared Data/Collections:If you have any data that truly needs to be shared across all tenants (e.g., a list of available plans, a global admin user table), this data should reside in your master_app_db or a similarly designated shared database. Your application logic would explicitly connect to this shared database when needed.Scalability & Performance:Monitor the performance of your single backend. If it becomes a bottleneck, consider scaling options for the Spring Boot application (e.g., deploying multiple instances behind a load balancer).For MongoDB, ensure proper indexing within each tenant database. Sharding your MongoDB instance could be a consideration for very high-scale multi-tenancy, but this is a more advanced topic beyond this initial transformation.Security:While "database per tenant" provides strong isolation, always ensure robust authentication and authorization within your Spring Boot application to prevent any unauthorized cross-tenant data access, especially for any shared resources or administration panels.Backup & Restore:Plan your backup and restore strategy per tenant database to ensure data integrity and recovery capabilities.