# 🔍 Actuator Endpoints - Monitoring & Management

## 📋 **Available Endpoints**

Your application now has comprehensive monitoring and management capabilities through Spring Boot Actuator.

## 🌐 **Public Endpoints (No Authentication Required)**

### **Health Check**
```bash
GET https://service.viganana.com/general-service/actuator/health
```
**Response:**
```json
{
  "status": "UP",
  "components": {
    "mongo": {"status": "UP"},
    "tenantDatabase": {
      "status": "UP",
      "details": {
        "tenant_databases": "All tenant databases are healthy",
        "healthy_tenants": 2,
        "total_tenants": 2,
        "tenant_demo_database": "generalWebDemo",
        "tenant_demo_status": "UP",
        "tenant_wanigarathna_database": "generalWebWanigarathna",
        "tenant_wanigarathna_status": "UP"
      }
    }
  }
}
```

### **Application Info**
```bash
GET https://service.viganana.com/general-service/actuator/info
```
**Response:**
```json
{
  "app": {
    "name": "General Service",
    "description": "Multi-tenant ERP General Service",
    "version": "1.0",
    "encoding": "UTF-8",
    "java": {"version": "17"}
  },
  "tenant": {
    "current_tenant": "demo",
    "current_database": "generalWebDemo",
    "multi_tenant_enabled": true,
    "database_pattern": "generalWeb + TenantName",
    "supported_domains": ["*.viganana.com", "*.vaganana.com"],
    "tenant_resolution": "subdomain-based",
    "configured_tenants": ["demo", "wanigarathna"]
  }
}
```

## 🔒 **Admin Endpoints (Requires ADMIN Role)**

### **Tenant Management**
```bash
GET https://service.viganana.com/general-service/actuator/tenants
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```
**Response:**
```json
{
  "current_tenant": "demo",
  "active_tenant_count": 2,
  "total_requests": 150,
  "tenant_request_counts": {
    "demo": 100,
    "wanigarathna": 50
  },
  "tenant_status": {
    "demo": {
      "database": "generalWebDemo",
      "status": "UP",
      "requests": 100
    },
    "wanigarathna": {
      "database": "generalWebWanigarathna", 
      "status": "UP",
      "requests": 50
    }
  },
  "database_pattern": "generalWeb + TenantName"
}
```

### **Specific Tenant Info**
```bash
GET https://service.viganana.com/general-service/actuator/tenants/demo
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```
**Response:**
```json
{
  "tenant_id": "demo",
  "requests": 100,
  "database": "generalWebDemo",
  "status": "UP",
  "collections": ["user", "item", "category", "brand"],
  "collection_count": 4,
  "user_count": 25,
  "item_count": 1500
}
```

### **Application Metrics**
```bash
GET https://service.viganana.com/general-service/actuator/metrics
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```

### **Tenant-Specific Metrics**
```bash
GET https://service.viganana.com/general-service/actuator/metrics/tenant.requests
GET https://service.viganana.com/general-service/actuator/metrics/tenant.active.count
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```

### **Environment Information**
```bash
GET https://service.viganana.com/general-service/actuator/env
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```

### **Configuration Properties**
```bash
GET https://service.viganana.com/general-service/actuator/configprops
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```

### **Application Beans**
```bash
GET https://service.viganana.com/general-service/actuator/beans
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```

### **Thread Dump**
```bash
GET https://service.viganana.com/general-service/actuator/threaddump
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```

### **Heap Dump**
```bash
GET https://service.viganana.com/general-service/actuator/heapdump
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```

## 🎯 **Custom Features**

### **1. Tenant Database Health Monitoring**
- Automatically checks connectivity to all tenant databases
- Reports status of each tenant's database
- Includes database naming pattern verification

### **2. Tenant Usage Metrics**
- Tracks requests per tenant
- Monitors active tenant count
- Records total tenant requests

### **3. Tenant Information**
- Current tenant context
- Database routing information
- Multi-tenant configuration details

### **4. Tenant Management Endpoint**
- View all tenant statuses
- Get specific tenant details
- Monitor tenant database collections and counts

## 🔧 **Configuration**

### **Security Configuration**
- `/actuator/health` and `/actuator/info` are public
- All other endpoints require ADMIN role
- CORS enabled for cross-origin requests

### **Exposed Endpoints**
```properties
management.endpoints.web.exposure.include=health,info,metrics,env,beans,configprops,httptrace,loggers,threaddump,heapdump,tenants
```

## 📊 **Monitoring Dashboard Integration**

These endpoints can be integrated with monitoring tools like:
- **Prometheus** (for metrics collection)
- **Grafana** (for visualization)
- **Spring Boot Admin** (for management UI)
- **Custom monitoring dashboards**

## 🚀 **Usage Examples**

### **Health Check Script**
```bash
#!/bin/bash
HEALTH=$(curl -s https://service.viganana.com/general-service/actuator/health | jq -r '.status')
if [ "$HEALTH" = "UP" ]; then
    echo "✅ Service is healthy"
else
    echo "❌ Service is down"
    exit 1
fi
```

### **Tenant Monitoring Script**
```bash
#!/bin/bash
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://service.viganana.com/general-service/actuator/tenants | jq
```

Your application now has comprehensive monitoring and management capabilities! 🎉
