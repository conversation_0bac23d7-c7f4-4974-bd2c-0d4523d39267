# 🏢 Multi-Tenant Configuration

## 🎯 **Overview**

This application is now configured as a **dynamic multi-tenant system**:
- ✅ **Subdomain-based routing**: Database determined by subdomain
- ✅ **Automatic tenant detection**: Extracts tenant from Host header
- ✅ **Dynamic database selection**: Each tenant uses separate database
- ✅ **Fallback support**: Uses default database when tenant cannot be determined

## 🔧 **How It Works**

### **Tenant Resolution Flow:**
1. **Request arrives** with Host header (e.g., `demo.viganana.com`)
2. **TenantContextFilter** extracts subdomain (`demo`)
3. **TenantContext** stores tenant in ThreadLocal
4. **TenantAwareMongoDbFactory** maps tenant to database (`generalWebDemo`)
5. **All database operations** use the tenant-specific database

### **Database Mapping:**
The database mapping is based on the configured default database in `application.properties`:

**With default configuration (`spring.data.mongodb.database=generalWeb`):**
- `demo.viganana.com` → `generalWebDemo`
- `wanigarathna.viganana.com` → `generalWebWanigarathna`
- `newcitymobile.viganana.com` → `generalWebNewcitymobile`
- `localhost:8080` → `generalWeb` (fallback)

**With custom configuration (`spring.data.mongodb.database=myapp`):**
- `demo.viganana.com` → `myappDemo`
- `wanigarathna.viganana.com` → `myappWanigarathna`
- `newcitymobile.viganana.com` → `myappNewcitymobile`
- `localhost:8080` → `myapp` (fallback)

## 📁 **Implementation Components**

### **Core Classes:**
- `TenantContextFilter` - Extracts subdomain from requests
- `TenantContext` - ThreadLocal holder for current tenant
- `TenantAwareMongoDbFactory` - Dynamic database factory
- `TenantConfiguration` - Filter registration configuration
- `TenantTestController` - Testing and debugging endpoints

### **Modified Classes:**
- `DbConfig.java` - Updated to use TenantAwareMongoDbFactory
- `application.properties` - Documented multi-tenant setup

## 🚀 **Usage Examples**

### **Frontend Requests:**
```javascript
// Demo tenant
fetch('https://demo.viganana.com/api/items')
// → Uses generalWebDemo database

// Wanigarathna tenant  
fetch('https://wanigarathna.viganana.com/api/items')
// → Uses generalWebWanigarathna database

// Localhost (fallback)
fetch('http://localhost:8080/api/items')
// → Uses generalWeb database
```

### **Testing Tenant Resolution:**
```bash
# Test tenant info
curl -H "Host: demo.viganana.com" http://localhost:8080/api/tenant/info

# Test database connectivity
curl -H "Host: wanigarathna.viganana.com" http://localhost:8080/api/tenant/test-db
```

## 🗄️ **Database Setup**

### **Required Databases:**
Each tenant needs a separate MongoDB database with the same schema:
- `generalWeb` - Default/fallback database
- `generalWebDemo` - Demo tenant database
- `generalWebWanigarathna` - Wanigarathna tenant database
- `generalWebNewcitymobile` - Newcitymobile tenant database

### **Database Creation:**
```javascript
// MongoDB shell commands
use generalWebDemo
db.createCollection("users")

use generalWebWanigarathna  
db.createCollection("users")

use generalWebNewcitymobile
db.createCollection("users")
```

## 🔍 **Testing & Debugging**

### **Test Endpoints:**
- `GET /api/tenant/info` - Current tenant information
- `GET /api/tenant/test-db` - Database connectivity test

### **Example Response:**
```json
{
  "host": "demo.viganana.com",
  "tenant": "demo",
  "database": "generalWebDemo",
  "actualDatabase": "generalWebDemo",
  "databaseConnected": true,
  "collectionCount": 15,
  "status": "success"
}
```

### **Logging:**
The system logs tenant resolution at DEBUG level:
```
🔍 Request: /api/items | Host: demo.viganana.com | Tenant: demo
🗄️ Current tenant DB: generalWebDemo
```

## ⚙️ **Configuration**

### **Enable/Disable Multi-Tenancy:**
Multi-tenancy is enabled by default. To disable:
1. Remove `@Component` from `TenantContextFilter`
2. Use `SimpleMongoClientDatabaseFactory` in `DbConfig`

### **Configurable Default Database:**
The default database and tenant database naming can be configured in `application.properties`:

```properties
# Default configuration
spring.data.mongodb.database=generalWeb

# Custom configuration examples
spring.data.mongodb.database=mycompany
spring.data.mongodb.database=erp
spring.data.mongodb.database=production
```

**How it works:**
- The configured database serves as both the fallback database and the base name for tenant databases
- Tenant databases are created by appending the capitalized tenant name to the base database name
- Example: If `spring.data.mongodb.database=mycompany`, then:
  - Default: `mycompany`
  - Demo tenant: `mycompanyDemo`
  - Wanigarathna tenant: `mycompanyWanigarathna`

## 🛡️ **Security & Hardening**

### **Tenant Validation:**
- Only alphanumeric subdomains are accepted
- Invalid subdomains fall back to default database
- ThreadLocal is always cleared to prevent memory leaks

### **Database Access:**
- Each tenant database requires proper MongoDB permissions
- Authentication uses shared credentials with database-specific auth

## 🎯 **Benefits**

✅ **Data Isolation**: Each tenant has completely separate data  
✅ **Scalability**: Easy to add new tenants by creating new databases  
✅ **Performance**: No tenant filtering in queries  
✅ **Flexibility**: Different tenants can have different schemas  
✅ **Backup**: Each tenant can be backed up independently  

## 🔄 **Migration from Single-Tenant**

If migrating from single-tenant setup:
1. ✅ **Backup existing data**
2. ✅ **Create tenant-specific databases**
3. ✅ **Copy data to appropriate tenant databases**
4. ✅ **Update DNS to point subdomains to same backend**
5. ✅ **Test tenant resolution**

## 🧪 **Testing Checklist**

- [ ] Test with valid subdomains
- [ ] Test with localhost (fallback)
- [ ] Test with invalid subdomains
- [ ] Test database operations per tenant
- [ ] Test concurrent requests from different tenants
- [ ] Verify ThreadLocal cleanup
- [ ] Check logging output
