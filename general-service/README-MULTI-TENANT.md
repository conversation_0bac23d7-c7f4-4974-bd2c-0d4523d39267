# Multi-Tenant Architecture Documentation

## Overview

This system implements a multi-tenant architecture using a database-per-tenant approach with MongoDB. Each tenant has its own dedicated MongoDB database, providing strong data isolation between tenants.

## How It Works

### Tenant Identification

Tenants are identified by subdomain in the request's Host header:

- `demo.viganana.com` → tenant: "demo" → database: "generalWebDemo"
- `wanigarathna.viganana.com` → tenant: "wanigarathna" → database: "generalWebWanigarathna" 
- `localhost:8080` → tenant: "default" → database: "generalWeb"

### Request Flow

1. **TenantContextFilter** extracts tenant ID from subdomain
2. **TenantContext** stores tenant ID in ThreadLocal
3. **TenantDatabaseService** creates tenant database if needed
4. **TenantAwareMongoDatabaseFactory** dynamically switches to tenant database
5. **Repository methods** automatically use the correct tenant database

## Configuration

The tenant databases use the naming convention: `[baseName][TenantName]`

Example: If your default database is `generalWeb`, then:
- Tenant "demo" uses database "generalWebDemo"
- Tenant "wanigarathna" uses database "generalWebWanigarathna"

## Testing

A unit test `MultiTenantTest` verifies proper data isolation between tenant databases.

## Administration

The `TenantAdminController` provides REST endpoints for tenant management:

- `GET /api/admin/tenants` - List all tenants
- `POST /api/admin/tenants` - Create a new tenant

## Health Monitoring

The `TenantMongoHealthIndicator` integrates with Spring Boot Actuator to provide health status of all tenant databases through the `/actuator/health` endpoint.

## Adding New Tenants

To add a new tenant:

1. **Automatic Method**: The first request to a new subdomain automatically creates the tenant database
2. **Manual Method**: Use the admin API to create the tenant database in advance

## Security Considerations

- Each tenant has complete database isolation
- Admin endpoints are secured with role-based access control
- Tenant database names are derived from subdomain names, ensuring consistency

## Limitations

- Adding tenants requires DNS configuration for the new subdomain
- Cross-tenant operations require explicit switching of the tenant context
