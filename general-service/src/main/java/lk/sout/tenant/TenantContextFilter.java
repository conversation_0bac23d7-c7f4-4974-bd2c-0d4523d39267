package lk.sout.tenant;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;

import java.io.IOException;

/**
 * Filter to extract tenant information from HTTP requests
 * 
 * This filter runs early in the request processing chain and extracts
 * the tenant identifier from the subdomain of the Host header.
 * The tenant information is stored in ThreadLocal for use throughout
 * the request processing.
 */
@Order(1) // Execute early in filter chain
public class TenantContextFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(TenantContextFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("🏢 TenantContextFilter initialized - Multi-tenant mode enabled");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        try {
            // Extract tenant from Host header
            String tenant = extractTenantFromHost(httpRequest);
            
            // Store in ThreadLocal
            TenantContext.setCurrentTenant(tenant);
            
            logger.debug("🔍 Request: {} | Host: {} | Tenant: {}", 
                httpRequest.getRequestURI(), 
                httpRequest.getHeader("Host"), 
                tenant);
            
            // Continue with request processing
            chain.doFilter(request, response);
            
        } finally {
            // Always clear ThreadLocal to prevent memory leaks
            TenantContext.clear();
        }
    }

    @Override
    public void destroy() {
        logger.info("🏢 TenantContextFilter destroyed");
    }

    /**
     * Extract tenant identifier from request headers
     *
     * Priority order:
     * 1. Origin header (frontend URL)
     * 2. Referer header (fallback)
     * 3. Host header (last resort)
     *
     * Examples:
     * - "https://udayabuffet.viganana.com" -> "udayabuffet"
     * - "demo.viganana.com" -> "demo"
     * - "localhost" -> "default"
     *
     * @param request HTTP request
     * @return tenant identifier (subdomain or "default")
     */
    private String extractTenantFromHost(HttpServletRequest request) {
        String tenantUrl = null;
        String source = null;

        // 1. Try Origin header first (most reliable for CORS requests)
        String origin = request.getHeader("Origin");
        if (origin != null && !origin.trim().isEmpty()) {
            tenantUrl = origin;
            source = "Origin";
            logger.debug("🌐 Using Origin header: {}", origin);
        }

        // 2. Try Referer header as fallback
        if (tenantUrl == null) {
            String referer = request.getHeader("Referer");
            if (referer != null && !referer.trim().isEmpty()) {
                tenantUrl = referer;
                source = "Referer";
                logger.debug("🔗 Using Referer header: {}", referer);
            }
        }

        // 3. Fall back to Host header (backend service URL)
        if (tenantUrl == null) {
            String host = request.getHeader("Host");
            if (host != null && !host.trim().isEmpty()) {
                tenantUrl = host;
                source = "Host";
                logger.debug("🏠 Using Host header: {}", host);
            }
        }

        if (tenantUrl == null) {
            logger.warn("⚠️ No Origin, Referer, or Host header found, using default tenant");
            return "default";
        }

        // Extract hostname from URL if it's a full URL
        String hostname = extractHostnameFromUrl(tenantUrl);

        // Remove port if present (e.g., "localhost:8080" -> "localhost")
        if (hostname.contains(":")) {
            hostname = hostname.substring(0, hostname.indexOf(":"));
        }

        // Handle localhost and IP addresses
        if (hostname.equals("localhost") || hostname.matches("\\d+\\.\\d+\\.\\d+\\.\\d+")) {
            logger.debug("🏠 Localhost/IP detected: {}, using default tenant", hostname);
            return "default";
        }

        // Extract subdomain from hostname (e.g., "udayabuffet.viganana.com" -> "udayabuffet")
        String[] parts = hostname.split("\\.");

        if (parts.length >= 2) {
            String subdomain = parts[0];

            // Validate subdomain (only alphanumeric and hyphens)
            if (subdomain.matches("[a-zA-Z0-9-]+")) {
                logger.debug("✅ Extracted tenant: {} from {} header: {}", subdomain, source, tenantUrl);
                return subdomain;
            } else {
                logger.warn("⚠️ Invalid subdomain format: {}, using default tenant", subdomain);
                return "default";
            }
        }

        logger.warn("⚠️ Could not extract subdomain from {}: {}, using default tenant", source, tenantUrl);
        return "default";
    }

    /**
     * Extract hostname from URL string
     *
     * @param url URL string (can be full URL or just hostname)
     * @return hostname
     */
    private String extractHostnameFromUrl(String url) {
        if (url.startsWith("http://") || url.startsWith("https://")) {
            try {
                // Remove protocol and path, keep only hostname
                url = url.substring(url.indexOf("://") + 3);
                if (url.contains("/")) {
                    url = url.substring(0, url.indexOf("/"));
                }
                if (url.contains("?")) {
                    url = url.substring(0, url.indexOf("?"));
                }
                return url;
            } catch (Exception e) {
                logger.warn("⚠️ Error parsing URL: {}", url);
                return url;
            }
        }
        return url;
    }
}
