package lk.sout.tenant;

import lk.sout.config.InitDataRunner;
import lk.sout.core.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Simple controller to provide tenant information
 * Used for checking tenant database setup and debugging multi-tenancy
 */
@RestController
@RequestMapping("/api/tenant")
public class TenantInfoController {

    private static final Logger logger = LoggerFactory.getLogger(TenantInfoController.class);

    @Autowired
    private InitDataRunner initDataRunner;

    @Autowired
    private UserRepository userRepository;

    /**
     * Get information about the current tenant context
     *
     * @return Map with tenant information
     */
    @GetMapping("/info")
    public Map<String, Object> getTenantInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("tenant", TenantContext.getCurrentTenant());
        info.put("database", TenantContext.getCurrentDatabase());
        info.put("timestamp", System.currentTimeMillis());
        info.put("status", "active");
        info.put("multiTenancy", TenantContext.isMultiTenancyEnabled() ? "enabled" : "disabled");

        // Check if database has users (indicates if it's initialized)
        try {
            long userCount = userRepository.count();
            info.put("userCount", userCount);
            info.put("initialized", userCount > 0);
        } catch (Exception e) {
            info.put("userCount", 0);
            info.put("initialized", false);
            info.put("error", e.getMessage());
        }

        return info;
    }

    /**
     * Test endpoint to verify multi-tenancy is working
     *
     * @return detailed tenant information for debugging
     */
    @GetMapping("/debug")
    public Map<String, Object> debugTenantInfo() {
        Map<String, Object> debug = new HashMap<>();
        debug.put("currentTenant", TenantContext.getCurrentTenant());
        debug.put("currentDatabase", TenantContext.getCurrentDatabase());
        debug.put("multiTenancyEnabled", TenantContext.isMultiTenancyEnabled());
        debug.put("timestamp", System.currentTimeMillis());
        debug.put("threadId", Thread.currentThread().getId());
        debug.put("threadName", Thread.currentThread().getName());
        return debug;
    }

    /**
     * Initialize the current tenant database with default data
     * This endpoint can be used to manually trigger database initialization
     * for new tenants
     *
     * @return initialization result
     */
    @PostMapping("/initialize")
    public ResponseEntity<Map<String, Object>> initializeTenant() {
        Map<String, Object> result = new HashMap<>();
        String tenant = TenantContext.getCurrentTenant();
        String database = TenantContext.getCurrentDatabase();

        try {
            logger.info("🚀 Manual tenant initialization requested for tenant: {} (database: {})", tenant, database);

            // Check if already initialized
            long userCount = userRepository.count();
            if (userCount > 0) {
                result.put("status", "already_initialized");
                result.put("message", "Tenant database already contains " + userCount + " users");
                result.put("tenant", tenant);
                result.put("database", database);
                return ResponseEntity.ok(result);
            }

            // Run initialization
            initDataRunner.run();

            // Verify initialization
            userCount = userRepository.count();

            result.put("status", "success");
            result.put("message", "Tenant database initialized successfully");
            result.put("tenant", tenant);
            result.put("database", database);
            result.put("userCount", userCount);

            logger.info("✅ Tenant initialization completed for tenant: {} (database: {}), created {} users",
                       tenant, database, userCount);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("❌ Tenant initialization failed for tenant: {} (database: {}): {}",
                        tenant, database, e.getMessage(), e);

            result.put("status", "error");
            result.put("message", "Initialization failed: " + e.getMessage());
            result.put("tenant", tenant);
            result.put("database", database);

            return ResponseEntity.status(500).body(result);
        }
    }
}
