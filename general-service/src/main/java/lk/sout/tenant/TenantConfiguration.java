package lk.sout.tenant;

import jakarta.servlet.Filter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * Configuration for multi-tenant functionality
 * 
 * This configuration ensures that the TenantContextFilter is properly
 * registered and executed early in the filter chain when multi-tenancy
 * is enabled.
 */
@Configuration
public class TenantConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(TenantConfiguration.class);

    /**
     * Register TenantContextFilter with high priority
     *
     * This filter is only registered when multi-tenancy is enabled.
     * When disabled, no tenant context processing occurs.
     */
    @Bean
    @ConditionalOnProperty(name = "multitenancy.enabled", havingValue = "true", matchIfMissing = false)
    public FilterRegistrationBean<Filter> tenantContextFilter() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();

        registrationBean.setFilter(new TenantContextFilter());
        registrationBean.addUrlPatterns("/*"); // Apply to all URLs
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE); // Execute first
        registrationBean.setName("tenantContextFilter");

        logger.info("🔧 Registered TenantContextFilter with highest precedence (Multi-tenancy ENABLED)");

        return registrationBean;
    }
}
