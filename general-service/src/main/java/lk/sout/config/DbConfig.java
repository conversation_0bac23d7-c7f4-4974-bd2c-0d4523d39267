package lk.sout.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lk.sout.event.CascadeSaveMongoEventListener;
import lk.sout.tenant.TenantAwareMongoDbFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.context.annotation.Primary;

/**
 * Created by Madhawa Weerasinghe on 7/27/2017.
 */
@Configuration
@EnableTransactionManagement
@EnableMongoAuditing
@ComponentScan(basePackages = {"lk.sout.core", "lk.sout.general"})
@EnableMongoRepositories(basePackages = {"lk.sout.core.repository", "lk.sout.general.inventory.repository",
        "lk.sout.general.hr.repository", "lk.sout.general.trade.repository"})


public class DbConfig extends AbstractMongoClientConfiguration {

    @Bean
    public CascadeSaveMongoEventListener cascadingMongoEventListener() {
        return new CascadeSaveMongoEventListener();
    }

    @Bean
    public AuditorAware<String> auditorProvider() {
        return new SpringSecurityAuditAwareImpl();
    }

    @Bean
    @Qualifier("mongoDbFactory")
    public MongoDatabaseFactory mongoDbFactory(@Qualifier("defaultMongoClient") MongoClient mongoClient,
                                               @Value("${multitenancy.enabled:true}") boolean multiTenancyEnabled) {
        if (multiTenancyEnabled) {
            // Create tenant-aware database factory that dynamically determines database based on subdomain
            return new TenantAwareMongoDbFactory(mongoClient, getDatabaseName());
        } else {
            // Use standard database factory for single-tenant mode
            return new SimpleMongoClientDatabaseFactory(mongoClient, getDatabaseName());
        }
    }

    @Bean
    public org.springframework.data.mongodb.MongoTransactionManager transactionManager(@Qualifier("mongoDbFactory") MongoDatabaseFactory dbFactory) {
        return new org.springframework.data.mongodb.MongoTransactionManager(dbFactory);
    }

    @Value("${spring.data.mongodb.host}")
    private String host;

    @Value("${spring.data.mongodb.database}")
    private String database;

    @Value("${spring.data.mongodb.authDatabase}")
    private String authDb;

    @Value("${spring.data.mongodb.port}")
    private int port;

    @Value("${spring.data.mongodb.username}")
    private String username;

    @Value("${spring.data.mongodb.password}")
    private String password;

    @Value("${multitenancy.auth.enabled:true}")
    private boolean tenantAuthEnabled;

    @Override
    protected String getDatabaseName() {
        // Return configured database name
        return database;
    }

    @Override
    @Bean("defaultMongoClient")
    public MongoClient mongoClient() {
        // Create connection without specifying database (will be determined per request)
        final ConnectionString connectionString = new ConnectionString("mongodb://" + host + ":" + port);
        final MongoClientSettings mongoClientSettings = MongoClientSettings.builder()
                .applyConnectionString(connectionString)
                .credential(MongoCredential.createCredential(username, authDb, password.toCharArray()))
                .applyToConnectionPoolSettings(builder ->
                        builder.maxSize(20)
                                .minSize(5)
                                .maxWaitTime(30, java.util.concurrent.TimeUnit.SECONDS)
                                .maxConnectionLifeTime(30, java.util.concurrent.TimeUnit.MINUTES)
                                .maxConnectionIdleTime(10, java.util.concurrent.TimeUnit.MINUTES))
                .applyToServerSettings(builder ->
                        builder.heartbeatFrequency(10, java.util.concurrent.TimeUnit.SECONDS)
                                .minHeartbeatFrequency(500, java.util.concurrent.TimeUnit.MILLISECONDS))
                .build();
        return MongoClients.create(mongoClientSettings);
    }

    @Bean
    @Primary
    public MongoClient mongoClientPrimary(@Qualifier("defaultMongoClient") MongoClient mongoClient) {
        // Provide unqualified MongoClient bean for other services like backup
        return mongoClient;
    }

    @Bean
    @Primary
    public MongoTemplate mongoTemplate(@Qualifier("mongoDbFactory") MongoDatabaseFactory dbFactory) {
        // Use standard database factory
        return new MongoTemplate(dbFactory);
    }

    @Override
    protected boolean autoIndexCreation() {
        return true;
    }

}
