package lk.sout.config;

import lk.sout.core.entity.DailyStatistics;
import lk.sout.core.repository.DailyStatisticsRepository;
import lk.sout.core.service.StatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.time.LocalDate;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Initializer to generate statistics for past days if they don't exist
 */
@Configuration
public class StatisticsInitializer {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatisticsInitializer.class);

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private DailyStatisticsRepository dailyStatisticsRepository;

    /**
     * Command line runner to generate statistics for past days
     * This runs after the application has started
     * @return CommandLineRunner
     */
    @Bean
    @Order(2) // Run after InitDataRunner
    public CommandLineRunner initializeStatistics() {
        return args -> {
            LOGGER.info("Starting statistics initialization...");
            
            try {
                // Check if we need to generate statistics for past days
                if (dailyStatisticsRepository.count() == 0) {
                    LOGGER.info("No statistics found. Generating statistics for the past 30 days...");
                    generatePastStatistics(30);
                } else {
                    // Generate statistics for today if it doesn't exist
                    LocalDate today = LocalDate.now();
                    DailyStatistics todayStats = dailyStatisticsRepository.findByDate(today);
                    
                    if (todayStats == null) {
                        LOGGER.info("Generating statistics for today...");
                        statisticsService.generateDailyStatistics();
                    }
                    
                    // Generate statistics for yesterday if it doesn't exist
                    LocalDate yesterday = today.minusDays(1);
                    DailyStatistics yesterdayStats = dailyStatisticsRepository.findByDate(yesterday);
                    
                    if (yesterdayStats == null) {
                        LOGGER.info("Generating statistics for yesterday...");
                        statisticsService.generateStatisticsForDate(yesterday);
                    }
                }
                
                LOGGER.info("Statistics initialization completed.");
            } catch (Exception e) {
                LOGGER.error("Error initializing statistics: {}", e.getMessage(), e);
            }
        };
    }
    
    /**
     * Generate statistics for past days
     * @param days Number of past days to generate statistics for
     */
    private void generatePastStatistics(int days) {
        ExecutorService executor = Executors.newFixedThreadPool(3); // Use 3 threads
        LocalDate today = LocalDate.now();
        
        for (int i = days; i >= 0; i--) {
            final int dayOffset = i;
            executor.submit(() -> {
                try {
                    LocalDate date = today.minusDays(dayOffset);
                    LOGGER.info("Generating statistics for {}", date);
                    statisticsService.generateStatisticsForDate(date);
                } catch (Exception e) {
                    LOGGER.error("Error generating statistics for day -{}: {}", dayOffset, e.getMessage());
                }
            });
        }
        
        executor.shutdown();
        try {
            // Wait for all tasks to complete or timeout after 5 minutes
            executor.awaitTermination(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            LOGGER.error("Statistics generation interrupted: {}", e.getMessage());
            Thread.currentThread().interrupt();
        }
    }
}
