package lk.sout.core.service;

import lk.sout.core.entity.DailyStatistics;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * Service interface for managing business statistics
 */
public interface StatisticsService {

    /**
     * Generate statistics for the current date
     * @return The generated statistics
     */
    DailyStatistics generateDailyStatistics();


    /**
     * Generate statistics for a specific date
     * @param date The date to generate statistics for
     * @return The generated statistics
     */
    DailyStatistics generateStatisticsForDate(LocalDate date);

    /**
     * Find statistics for a specific date
     * @param date The date
     * @return DailyStatistics for the date or null if not found
     */
    DailyStatistics findByDate(LocalDate date);

    /**
     * Find statistics for a date range
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @return List of DailyStatistics for the date range
     */
    List<DailyStatistics> findByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * Find statistics for a date range with pagination
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @param pageable Pagination information
     * @return Page of DailyStatistics for the date range
     */
    Page<DailyStatistics> findByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Find latest statistics with pagination
     * @param pageable Pagination information
     * @return Page of DailyStatistics ordered by date descending
     */
    Page<DailyStatistics> findLatest(Pageable pageable);

    /**
     * Save statistics
     * @param statistics The statistics to save
     * @return true if successful, false otherwise
     */
    boolean save(DailyStatistics statistics);

    /**
     * Calculate stock statistics
     * @param statistics The statistics object to update
     */
    void calculateStockStatistics(DailyStatistics statistics);

    /**
     * Calculate sales statistics for a specific date
     * @param statistics The statistics object to update
     * @param date The date to calculate statistics for
     */
    void calculateSalesStatistics(DailyStatistics statistics, LocalDate date);

    /**
     * Calculate pending bills statistics
     * @param statistics The statistics object to update
     */
    void calculatePendingBillsStatistics(DailyStatistics statistics);

    /**
     * Calculate cheque statistics
     * @param statistics The statistics object to update
     */
    void calculateChequeStatistics(DailyStatistics statistics);

    /**
     * Calculate transaction statistics for a specific date
     * @param statistics The statistics object to update
     * @param date The date to calculate statistics for
     */
    void calculateTransactionStatistics(DailyStatistics statistics, LocalDate date);
}
