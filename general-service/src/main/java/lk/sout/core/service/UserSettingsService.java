package lk.sout.core.service;

import lk.sout.core.entity.UserSettings;

import java.util.Map;

/**
 * Service interface for managing user settings
 */
public interface UserSettingsService {

    /**
     * Save user settings
     * @param userSettings The user settings to save
     * @return true if successful, false otherwise
     */
    boolean save(UserSettings userSettings);

    /**
     * Find settings for the current user
     * @return UserSettings for the current user or null if not found
     */
    UserSettings findFor<PERSON>urrentUser();

    /**
     * Find settings for a specific user
     * @param username The username
     * @return UserSettings for the user or null if not found
     */
    UserSettings findByUsername(String username);

    /**
     * Update a setting for the current user
     * @param key Setting key
     * @param value Setting value
     * @param enabled Whether the setting is enabled
     * @return true if successful, false otherwise
     */
    boolean updateSetting(String key, String value, boolean enabled);

    /**
     * Update a setting for a specific user (admin only)
     * @param username The username
     * @param key Setting key
     * @param value Setting value
     * @param enabled Whether the setting is enabled
     * @return true if successful, false otherwise
     */
    boolean updateSettingForUser(String username, String key, String value, boolean enabled);

    /**
     * Update multiple settings for the current user
     * @param settings Map of setting keys to values
     * @param enabled Whether the settings should be enabled
     * @return true if successful, false otherwise
     */
    boolean updateSettings(Map<String, String> settings, boolean enabled);

    /**
     * Update multiple settings for a specific user (admin only)
     * @param username The username
     * @param settings Map of setting keys to values
     * @param enabled Whether the settings should be enabled
     * @return true if successful, false otherwise
     */
    boolean updateSettingsForUser(String username, Map<String, String> settings, boolean enabled);

    /**
     * Enable or disable a setting for the current user
     * @param key Setting key
     * @param enabled Whether the setting should be enabled
     * @return true if successful, false otherwise
     */
    boolean setSettingEnabled(String key, boolean enabled);

    /**
     * Enable or disable a setting for a specific user (admin only)
     * @param username The username
     * @param key Setting key
     * @param enabled Whether the setting should be enabled
     * @return true if successful, false otherwise
     */
    boolean setSettingEnabledForUser(String username, String key, boolean enabled);

    /**
     * Get a setting value for the current user
     * @param key Setting key
     * @return Setting value or null if not found or disabled
     */
    String getSettingValue(String key);

    /**
     * Get a setting value for a specific user (admin only)
     * @param username The username
     * @param key Setting key
     * @return Setting value or null if not found or disabled
     */
    String getSettingValueForUser(String username, String key);

    /**
     * Check if a setting is enabled for the current user
     * @param key Setting key
     * @return true if setting exists and is enabled, false otherwise
     */
    boolean isSettingEnabled(String key);

    /**
     * Check if a setting is enabled for a specific user (admin only)
     * @param username The username
     * @param key Setting key
     * @return true if setting exists and is enabled, false otherwise
     */
    boolean isSettingEnabledForUser(String username, String key);

    /**
     * Get all settings for the current user
     * @return Map of setting keys to values (only enabled settings)
     */
    Map<String, String> getAllSettings();

    /**
     * Get all settings for a specific user (admin only)
     * @param username The username
     * @return Map of setting keys to values (only enabled settings)
     */
    Map<String, String> getAllSettingsForUser(String username);

    /**
     * Initialize default user settings for a specific user
     * @param username The username to initialize settings for
     * @return true if successful, false otherwise
     */
    boolean initializeDefaultSettingsForUser(String username);

    /**
     * Initialize default user settings for all users
     * This method should be called during application startup
     */
    void initializeDefaultSettings();
}
