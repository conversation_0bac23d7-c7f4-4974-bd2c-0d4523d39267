package lk.sout.core.service.impl;

import lk.sout.core.entity.Module;
import lk.sout.core.repository.ModuleRepository;
import lk.sout.core.service.ModuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ModuleServiceImpl implements ModuleService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModuleServiceImpl.class);

    @Autowired
    ModuleRepository moduleRepository;

    @Override
    public boolean save(Module module) {
        try {
            moduleRepository.save(module);
            LOGGER.info(module.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Save Metadata failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public void saveIfUnavailable(String moduleName, String description) {
        try {
            Module mod = searchModule(moduleName);
            if (null == mod) {
                Module module = new Module(moduleName, description, true);
                moduleRepository.save(module);
            }
        } catch (Exception ex) {
            LOGGER.error("Save Metadata failed: " + ex.getMessage());
        }
    }

    @Override
    public Module searchModule(String moduleName) {
        try {
            return moduleRepository.findByName(moduleName);
        } catch (Exception ex) {
            LOGGER.error("searchMetaData failed: " + ex.getMessage());
            return null;
        }

    }

    @Override
    public List<Module> findAll() {
        return moduleRepository.findAll();
    }

}
