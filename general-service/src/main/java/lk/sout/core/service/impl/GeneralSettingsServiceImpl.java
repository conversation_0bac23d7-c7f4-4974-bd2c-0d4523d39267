package lk.sout.core.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.core.entity.Settings;
import lk.sout.core.entity.UserSettings;
import lk.sout.core.repository.SettingsRepository;
import lk.sout.core.service.GeneralSettingsService;
import lk.sout.core.service.UserSettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Implementation of the GeneralSettingsService interface
 * Manages general application settings that apply to all users
 */
@Service
public class GeneralSettingsServiceImpl implements GeneralSettingsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(GeneralSettingsServiceImpl.class);

    @Autowired
    private SettingsRepository settingsRepository;

    @Autowired
    private Response response;

    @Autowired(required = false)
    private UserSettingsService userSettingsService;

    @Override
    public Response save(Settings settings) {
        try {
            // Check if a setting with the same key and category already exists
            Optional<Settings> existingSetting = settingsRepository.findByKeyAndCategory(settings.getKey(), settings.getCategory());

            // If an existing setting is found
            if (existingSetting.isPresent()) {
                // Always update the existing setting
                // This ensures we don't get conflicts when saving settings with the same key and category
                Settings existing = existingSetting.get();
                LOGGER.info("Found existing setting with ID: {}, key: {}, category: {}",
                           existing.getId(), existing.getKey(), existing.getCategory());

                // Keep the existing ID to update the record
                settings.setId(existing.getId());
                LOGGER.info("Using existing setting ID for update: {}", settings.getId());
            }

            // Save the setting
            Settings savedSetting = settingsRepository.save(settings);
            LOGGER.info("Setting saved successfully: {}", savedSetting);

            // Update user settings in localStorage
            updateUserSettings(savedSetting);

            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Setting saved successfully");
            // Don't set the entire object as it can't be properly serialized
            // Instead, set the ID so the frontend can retrieve the setting if needed
            response.setData(savedSetting.getId());
            return response;
        } catch (Exception e) {
            LOGGER.error("Error saving setting: {}", e.getMessage(), e);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Error saving setting");
            response.setData(e.getMessage());
            return response;
        }
    }

    @Override
    public List<Settings> findAll() {
        try {
            return settingsRepository.findAll();
        } catch (Exception e) {
            LOGGER.error("Error finding all settings: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Settings> findByCategory(String category) {
        try {
            return settingsRepository.findByCategory(category);
        } catch (Exception e) {
            LOGGER.error("Error finding settings by category {}: {}", category, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Settings findByKey(String key) {
        try {
            LOGGER.debug("Finding setting by key: {}", key);
            Optional<Settings> setting = settingsRepository.findByKey(key);
            if (setting.isPresent()) {
                LOGGER.debug("Found setting: {}", setting.get());
                return setting.get();
            } else {
                LOGGER.warn("Setting not found with key: {}", key);
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("Error finding setting by key {}: {}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Settings findById(String id) {
        try {
            LOGGER.debug("Finding setting by ID: {}", id);
            Optional<Settings> setting = settingsRepository.findById(id);
            if (setting.isPresent()) {
                LOGGER.debug("Found setting: {}", setting.get());
                return setting.get();
            } else {
                LOGGER.warn("Setting not found with ID: {}", id);
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("Error finding setting by ID {}: {}", id, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Response delete(String id) {
        try {
            if (!settingsRepository.existsById(id)) {
                response.setCode(404);
                response.setSuccess(false);
                response.setMessage("Setting not found");
                return response;
            }

            // Get the setting before deleting it
            Optional<Settings> settingToDelete = settingsRepository.findById(id);

            settingsRepository.deleteById(id);

            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Setting deleted successfully");

            // Include the deleted setting ID and key in the response
            if (settingToDelete.isPresent()) {
                Settings deletedSetting = settingToDelete.get();
                response.setData(deletedSetting.toString());
                LOGGER.info("Setting deleted: {}", deletedSetting);
            } else {
                response.setData(id); // Just include the ID if we couldn't get the full setting
            }

            return response;
        } catch (Exception e) {
            LOGGER.error("Error deleting setting: {}", e.getMessage(), e);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Error deleting setting");
            response.setData(e.getMessage());
            return response;
        }
    }

    @Override
    public boolean saveIfUnavailable(String key, String value, String description, String category) {
        try {
            // Check if setting already exists
            Optional<Settings> existingSetting = settingsRepository.findByKeyAndCategory(key, category);

            if (existingSetting.isPresent()) {
                LOGGER.debug("Setting already exists: {} in category {}", key, category);
                return false;
            }

            // Create and save new setting
            Settings setting = new Settings(key, value, description, category);
            Settings savedSetting = settingsRepository.save(setting);
            LOGGER.info("Saved new setting: {} in category {}", key, category);

            // Update user settings in localStorage
            updateUserSettings(savedSetting);

            return true;
        } catch (Exception e) {
            LOGGER.error("Error saving setting: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void initializeDefaultSettings() {
        LOGGER.info("Initializing default settings");

        // Essential settings only as per requirements

        // 1. Default Discount Mode
        saveIfUnavailable("defaultDiscountMode", "percentage", "Default discount mode (percentage or flat)", "Invoice");

        // 2. Silent Printing
        saveIfUnavailable("useSilentPrint", "false", "Whether to use silent printing (no print dialog)", "Printing");

        // 3. Printer Template
        saveIfUnavailable("printerTemplate", "58mm_English", "Default printer template", "Printing");

        // Available printer templates (needed for dropdown options)
        saveIfUnavailable("printerTemplates", "58mm_English,76mm_English,80mm_English,80mm_Sinhala,Legal_English,Legal_Customised_English",
                         "Available printer templates", "Printing");

        // 4. Minimum Markup Percentage (DEPRECATED - Now moved to user settings, kept for backward compatibility)
        saveIfUnavailable("minimumMarkupPercentage", "4", "Minimum markup percentage above cost for CASHIER role (DEPRECATED - Now in user settings)", "Invoice");

        // 5. Application Language
        saveIfUnavailable("defaultLanguage", "en", "Default system language (en or sn)", "System");

        // 6. Allow Selling Under Cost
        saveIfUnavailable("allowSellingUnderCost", "false", "Whether to allow selling items below cost", "Invoice");

        // 7. Currency Settings
        saveIfUnavailable("defaultCurrency", "LKR", "Default currency code (LKR, USD, EUR, etc.)", "System");
        saveIfUnavailable("currencySymbol", "රු", "Currency symbol to display", "System");
        saveIfUnavailable("currencyPosition", "before", "Currency symbol position (before or after amount)", "System");

        // Available currency options (needed for dropdown options)
        String currencyOptionsJson = "[" +
            "{\"value\":\"LKR\",\"label\":\"Sri Lankan Rupee (LKR)\",\"symbol\":\"රු\"}," +
            "{\"value\":\"USD\",\"label\":\"US Dollar (USD)\",\"symbol\":\"$\"}," +
            "{\"value\":\"EUR\",\"label\":\"Euro (EUR)\",\"symbol\":\"€\"}," +
            "{\"value\":\"GBP\",\"label\":\"British Pound (GBP)\",\"symbol\":\"£\"}," +
            "{\"value\":\"INR\",\"label\":\"Indian Rupee (INR)\",\"symbol\":\"₹\"}," +
            "{\"value\":\"JPY\",\"label\":\"Japanese Yen (JPY)\",\"symbol\":\"¥\"}," +
            "{\"value\":\"AUD\",\"label\":\"Australian Dollar (AUD)\",\"symbol\":\"A$\"}," +
            "{\"value\":\"CAD\",\"label\":\"Canadian Dollar (CAD)\",\"symbol\":\"C$\"}" +
            "]";
        saveIfUnavailable("currencyOptions", currencyOptionsJson, "Available currency options", "System");

        // 8. Custom Invoice Footer Text
        saveIfUnavailable("customInvoiceFooter", "", "Custom text to display at the bottom of invoices", "Printing");

        // 9. Cost Code Letter Mapping
        String defaultCostCodeMapping = "{\"0\":\"A\",\"1\":\"B\",\"2\":\"C\",\"3\":\"D\",\"4\":\"E\",\"5\":\"F\",\"6\":\"G\",\"7\":\"H\",\"8\":\"I\",\"9\":\"J\"}";
        saveIfUnavailable("costCodeLetterMapping", defaultCostCodeMapping, "Number to letter mapping for cost codes in barcode printing", "Barcode");

        LOGGER.info("Default settings initialization complete");
    }

    @Override
    public void updateUserSettings(Settings setting) {
        try {
            LOGGER.debug("Updating user settings for key: {}", setting.getKey());

            // If we have access to UserSettingsService, update default values for new users
            if (userSettingsService != null) {
                LOGGER.debug("Updating default user settings for key: {}", setting.getKey());

                // For specific settings that should be synchronized with user settings
                if ("minimumMarkupPercentage".equals(setting.getKey()) ||
                    "defaultDiscountMode".equals(setting.getKey()) ||
                    "printerTemplate".equals(setting.getKey()) ||
                    "useSilentPrint".equals(setting.getKey()) ||
                    "defaultLanguage".equals(setting.getKey()) ||
                    "allowSellingUnderCost".equals(setting.getKey())) {

                    // Note: The frontend will handle updating localStorage when it receives the updated setting
                    // from the API response. No additional action needed here for localStorage.
                    LOGGER.info("Setting {} updated. Frontend will update localStorage accordingly.", setting.getKey());
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error updating user settings for key {}: {}", setting.getKey(), e.getMessage(), e);
        }
    }
}
