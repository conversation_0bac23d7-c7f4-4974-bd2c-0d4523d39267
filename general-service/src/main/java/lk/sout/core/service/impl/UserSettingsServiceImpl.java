package lk.sout.core.service.impl;

import lk.sout.core.entity.User;
import lk.sout.core.entity.UserSettings;
import lk.sout.core.repository.UserRepository;
import lk.sout.core.repository.UserSettingsRepository;
import lk.sout.core.service.UserService;
import lk.sout.core.service.UserSettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Implementation of UserSettingsService
 */
@Service
public class UserSettingsServiceImpl implements UserSettingsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserSettingsServiceImpl.class);

    @Autowired
    private UserSettingsRepository userSettingsRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @Override
    public boolean save(UserSettings userSettings) {
        try {
            userSettingsRepository.save(userSettings);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Save user settings failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public UserSettings findForCurrentUser() {
        try {
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return null;
            }

            UserSettings settings = userSettingsRepository.findByUser(currentUser);
            if (settings == null) {
                // Create new settings for the user if not found
                settings = new UserSettings(currentUser);
                userSettingsRepository.save(settings);
            }
            return settings;
        } catch (Exception ex) {
            LOGGER.error("Find settings for current user failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public UserSettings findByUsername(String username) {
        try {
            User user = userRepository.findByUsername(username);
            if (user == null) {
                return null;
            }

            UserSettings settings = userSettingsRepository.findByUser(user);
            if (settings == null) {
                // Create new settings for the user if not found
                settings = new UserSettings(user);
                userSettingsRepository.save(settings);
            }
            return settings;
        } catch (Exception ex) {
            LOGGER.error("Find settings by username failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean updateSetting(String key, String value, boolean enabled) {
        try {
            UserSettings settings = findForCurrentUser();
            if (settings == null) {
                return false;
            }

            settings.addSetting(key, value, enabled);
            userSettingsRepository.save(settings);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Update setting failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean updateSettingForUser(String username, String key, String value, boolean enabled) {
        try {
            UserSettings settings = findByUsername(username);
            if (settings == null) {
                return false;
            }

            settings.addSetting(key, value, enabled);
            userSettingsRepository.save(settings);
            LOGGER.info("Admin updated setting for user {}: key={}, value={}, enabled={}", username, key, value, enabled);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Update setting for user failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean updateSettings(Map<String, String> settings, boolean enabled) {
        try {
            UserSettings userSettings = findForCurrentUser();
            if (userSettings == null) {
                return false;
            }

            for (Map.Entry<String, String> entry : settings.entrySet()) {
                userSettings.addSetting(entry.getKey(), entry.getValue(), enabled);
            }

            userSettingsRepository.save(userSettings);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Update settings failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean updateSettingsForUser(String username, Map<String, String> settings, boolean enabled) {
        try {
            UserSettings userSettings = findByUsername(username);
            if (userSettings == null) {
                return false;
            }

            for (Map.Entry<String, String> entry : settings.entrySet()) {
                userSettings.addSetting(entry.getKey(), entry.getValue(), enabled);
            }

            userSettingsRepository.save(userSettings);
            LOGGER.info("Admin updated settings for user {}: {} settings", username, settings.size());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Update settings for user failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean setSettingEnabled(String key, boolean enabled) {
        try {
            UserSettings settings = findForCurrentUser();
            if (settings == null) {
                return false;
            }

            boolean updated = settings.setSettingEnabled(key, enabled);
            if (updated) {
                userSettingsRepository.save(settings);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Set setting enabled failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean setSettingEnabledForUser(String username, String key, boolean enabled) {
        try {
            UserSettings settings = findByUsername(username);
            if (settings == null) {
                return false;
            }

            boolean updated = settings.setSettingEnabled(key, enabled);
            if (updated) {
                userSettingsRepository.save(settings);
                LOGGER.info("Admin set setting enabled for user {}: key={}, enabled={}", username, key, enabled);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Set setting enabled for user failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public String getSettingValue(String key) {
        try {
            UserSettings settings = findForCurrentUser();
            if (settings == null) {
                return null;
            }

            return settings.getSettingValue(key);
        } catch (Exception ex) {
            LOGGER.error("Get setting value failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public String getSettingValueForUser(String username, String key) {
        try {
            UserSettings settings = findByUsername(username);
            if (settings == null) {
                return null;
            }

            return settings.getSettingValue(key);
        } catch (Exception ex) {
            LOGGER.error("Get setting value for user failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean isSettingEnabled(String key) {
        try {
            UserSettings settings = findForCurrentUser();
            if (settings == null) {
                return false;
            }

            return settings.isSettingEnabled(key);
        } catch (Exception ex) {
            LOGGER.error("Check if setting is enabled failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean isSettingEnabledForUser(String username, String key) {
        try {
            UserSettings settings = findByUsername(username);
            if (settings == null) {
                return false;
            }

            return settings.isSettingEnabled(key);
        } catch (Exception ex) {
            LOGGER.error("Check if setting is enabled for user failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Map<String, String> getAllSettings() {
        try {
            UserSettings settings = findForCurrentUser();
            if (settings == null) {
                return new HashMap<>();
            }

            Map<String, String> result = new HashMap<>();
            for (Map.Entry<String, UserSettings.SettingValue> entry : settings.getSettings().entrySet()) {
                if (entry.getValue().isEnabled()) {
                    result.put(entry.getKey(), entry.getValue().getValue());
                }
            }
            return result;
        } catch (Exception ex) {
            LOGGER.error("Get all settings failed: " + ex.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, String> getAllSettingsForUser(String username) {
        try {
            UserSettings settings = findByUsername(username);
            if (settings == null) {
                return new HashMap<>();
            }

            Map<String, String> result = new HashMap<>();
            for (Map.Entry<String, UserSettings.SettingValue> entry : settings.getSettings().entrySet()) {
                if (entry.getValue().isEnabled()) {
                    result.put(entry.getKey(), entry.getValue().getValue());
                }
            }
            return result;
        } catch (Exception ex) {
            LOGGER.error("Get all settings for user failed: " + ex.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public boolean initializeDefaultSettingsForUser(String username) {
        try {
            LOGGER.info("Initializing default user settings for user: {}", username);

            User user = userRepository.findByUsername(username);
            if (user == null) {
                LOGGER.error("User not found: {}", username);
                return false;
            }

            UserSettings settings = userSettingsRepository.findByUser(user);
            if (settings == null) {
                settings = new UserSettings(user);
            }

            // Initialize invoice creation mode
            settings.addSetting("invoiceCreationMode", "standard", true);

            // Initialize minimum markup percentage (moved from general settings)
            settings.addSetting("minimumMarkupPercentage", "4", true);

            // Add more default user settings here as needed
            // Note: Available invoice creation modes: standard, sales_rep, distributor

            userSettingsRepository.save(settings);
            LOGGER.info("Default user settings initialized for user: {}", username);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Initialize default settings for user failed: {}", ex.getMessage());
            return false;
        }
    }

    @Override
    public void initializeDefaultSettings() {
        LOGGER.info("Initializing default user settings for all users");

        try {
            // Get all users
            Iterable<User> users = userRepository.findAll();

            // Initialize settings for each user
            for (User user : users) {
                initializeDefaultSettingsForUser(user.getUsername());
            }

            LOGGER.info("Default user settings initialization complete");
        } catch (Exception ex) {
            LOGGER.error("Initialize default settings failed: {}", ex.getMessage());
        }
    }
}
