package lk.sout.core.service.impl;

import lk.sout.core.entity.LoggedUser;
import lk.sout.core.repository.LoggedUserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 9/25/2018
 */
@Service
public class LoggedUserServiceImpl {

    final static Logger LOGGER = LoggerFactory.getLogger(LoggedUserServiceImpl.class);

    @Autowired
    LoggedUserRepository loggedUserRepository;

    public String save(LoggedUser loggedUser) {
        try {
            loggedUserRepository.save(loggedUser);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Logged user saving failed" + ex.getMessage());
            return ex.getMessage();
        }
    }
}
