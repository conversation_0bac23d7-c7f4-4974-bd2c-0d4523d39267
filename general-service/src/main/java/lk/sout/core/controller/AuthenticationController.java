package lk.sout.core.controller;

import lk.sout.config.JwtTokenUtil;
import lk.sout.core.entity.LoggedUser;
import lk.sout.core.entity.LoginUser;
import lk.sout.core.entity.User;
import lk.sout.core.service.UserService;
import lk.sout.tenant.TenantContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Madhawa Weerasinghe on 6/21/2018
 */
@RestController
@RequestMapping("/")
public class AuthenticationController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private LoggedUser loggedUser;

    @Autowired
    private MongoTemplate mongoTemplate;

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationController.class);

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public ResponseEntity register(@RequestBody LoginUser loginUser) throws AuthenticationException {

        try {
            logger.info("Login attempt for user: {}", loginUser.getUsername());

            final Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginUser.getUsername(),
                            loginUser.getPassword()
                    )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            final User user = userService.findOne(loginUser.getUsername());
            if (user == null) {
                logger.warn("User not found: {}", loginUser.getUsername());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("message","User not found."));
            }

            final String token = jwtTokenUtil.generateToken(user);

            loggedUser.setUser(user);
            loggedUser.setToken(token);
            loggedUser.setLastLogin(new Date());

            logger.info("Login successful for user: {}", loginUser.getUsername());
            return ResponseEntity.ok(loggedUser);

        } catch (BadCredentialsException e) {
            logger.warn("Bad credentials for user: {}", loginUser.getUsername());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("message","Incorrect username or password."));
        } catch (AuthenticationException e) {
            logger.error("Authentication error for username: {}", loginUser.getUsername(), e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("message","Authentication failed. Please try again."));
        } catch (Exception e) {
            logger.error("An unexpected error occurred during login for username: {}", loginUser.getUsername(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("message","An unexpected error occurred. Please try again later."));
        }
    }

    /**
     * Debug login endpoint with detailed information
     * This provides comprehensive debugging for login issues
     */
    @RequestMapping(value = "/login/debug-full", method = RequestMethod.POST)
    public ResponseEntity<?> debugLogin(@RequestBody LoginUser loginUser) {
        Map<String, Object> debugInfo = new HashMap<>();

        try {
            // 🔍 TENANT DEBUG INFO
            String currentTenant = TenantContext.getCurrentTenant();
            String currentDatabase = TenantContext.getCurrentDatabase();
            boolean multiTenancyEnabled = TenantContext.isMultiTenancyEnabled();
            String actualDatabaseName = mongoTemplate.getDb().getName();

            debugInfo.put("tenant", currentTenant);
            debugInfo.put("expectedDatabase", currentDatabase);
            debugInfo.put("actualDatabase", actualDatabaseName);
            debugInfo.put("multiTenancyEnabled", multiTenancyEnabled);
            debugInfo.put("username", loginUser.getUsername());
            debugInfo.put("timestamp", new Date());

            // Check if user exists in current database
            final User userBeforeAuth = userService.findOne(loginUser.getUsername());
            debugInfo.put("userFoundBeforeAuth", userBeforeAuth != null);
            if (userBeforeAuth != null) {
                debugInfo.put("userIdBeforeAuth", userBeforeAuth.getId());
                debugInfo.put("userActiveBeforeAuth", userBeforeAuth.isActive());
                debugInfo.put("userEnabledBeforeAuth", userBeforeAuth.getEnabled());
            }

            try {
                final Authentication authentication = authenticationManager.authenticate(
                        new UsernamePasswordAuthenticationToken(
                                loginUser.getUsername(),
                                loginUser.getPassword()
                        )
                );
                debugInfo.put("authenticationSuccess", true);

                final User user = userService.findOne(loginUser.getUsername());
                debugInfo.put("userFoundAfterAuth", user != null);
                if (user != null) {
                    debugInfo.put("userIdAfterAuth", user.getId());
                }

            } catch (BadCredentialsException e) {
                debugInfo.put("authenticationSuccess", false);
                debugInfo.put("error", "Bad credentials");
                debugInfo.put("errorType", "BadCredentialsException");
            } catch (AuthenticationException e) {
                debugInfo.put("authenticationSuccess", false);
                debugInfo.put("error", "Authentication failed");
                debugInfo.put("errorType", "AuthenticationException");
                debugInfo.put("errorMessage", e.getMessage());
            }

            return ResponseEntity.ok(debugInfo);

        } catch (Exception e) {
            debugInfo.put("error", e.getMessage());
            debugInfo.put("errorType", e.getClass().getSimpleName());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(debugInfo);
        }
    }

    /**
     * Debug endpoint to check user existence across databases
     * This helps troubleshoot multi-tenant login issues
     */
    @RequestMapping(value = "/login/debug", method = RequestMethod.POST)
    public ResponseEntity<?> debugUserLookup(@RequestBody Map<String, String> request) {
        String username = request.get("username");

        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "Username is required"));
        }

        Map<String, Object> debugInfo = new HashMap<>();

        try {
            // Current tenant info
            String currentTenant = TenantContext.getCurrentTenant();
            String currentDatabase = TenantContext.getCurrentDatabase();
            boolean multiTenancyEnabled = TenantContext.isMultiTenancyEnabled();
            String actualDatabaseName = mongoTemplate.getDb().getName();

            debugInfo.put("tenant", currentTenant);
            debugInfo.put("expectedDatabase", currentDatabase);
            debugInfo.put("actualDatabase", actualDatabaseName);
            debugInfo.put("multiTenancyEnabled", multiTenancyEnabled);
            debugInfo.put("username", username);
            debugInfo.put("timestamp", new Date());

            // Check user in current database
            User user = userService.findOne(username);
            debugInfo.put("userFoundInCurrentDB", user != null);

            if (user != null) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("username", user.getUsername());
                userInfo.put("active", user.isActive());
                userInfo.put("enabled", user.getEnabled());
                userInfo.put("firstName", user.getFirstName());
                userInfo.put("lastName", user.getLastName());
                userInfo.put("email", user.getEmail());
                userInfo.put("hasPassword", user.getPassword() != null && !user.getPassword().isEmpty());
                userInfo.put("passwordLength", user.getPassword() != null ? user.getPassword().length() : 0);
                userInfo.put("userRoles", user.getUserRoles() != null ? user.getUserRoles().size() : 0);
                debugInfo.put("userDetails", userInfo);
            }

            // Check in default database (generalWeb) if we're not already there
            if (!actualDatabaseName.equals("generalWeb")) {
                try {
                    // Temporarily switch to default database for lookup
                    TenantContext.setCurrentTenant("default");
                    User defaultUser = userService.findOne(username);
                    debugInfo.put("userFoundInDefaultDB", defaultUser != null);

                    if (defaultUser != null) {
                        Map<String, Object> defaultUserInfo = new HashMap<>();
                        defaultUserInfo.put("id", defaultUser.getId());
                        defaultUserInfo.put("active", defaultUser.isActive());
                        defaultUserInfo.put("enabled", defaultUser.getEnabled());
                        debugInfo.put("defaultUserDetails", defaultUserInfo);
                    }
                } catch (Exception e) {
                    debugInfo.put("defaultDBCheckError", e.getMessage());
                } finally {
                    // Restore original tenant context
                    TenantContext.setCurrentTenant(currentTenant);
                }
            }

            // Database collection stats
            try {
                long userCount = mongoTemplate.getCollection("user").countDocuments();
                debugInfo.put("totalUsersInCurrentDB", userCount);
            } catch (Exception e) {
                debugInfo.put("userCountError", e.getMessage());
            }

            logger.info("🔍 USER DEBUG - Username: {}, Current DB: {}, User found: {}",
                       username, actualDatabaseName, user != null);

            return ResponseEntity.ok(debugInfo);

        } catch (Exception e) {
            logger.error("❌ Error in user debug lookup", e);
            debugInfo.put("error", e.getMessage());
            debugInfo.put("errorType", e.getClass().getSimpleName());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(debugInfo);
        }
    }

}
