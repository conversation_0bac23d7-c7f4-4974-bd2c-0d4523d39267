package lk.sout.core.controller;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import lk.sout.tenant.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Debug controller for database and multi-tenancy troubleshooting
 */
@RestController
@RequestMapping("/api/debug")
public class DatabaseDebugController {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseDebugController.class);

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MongoClient mongoClient;

    @Value("${spring.data.mongodb.database}")
    private String defaultDatabase;

    @Value("${spring.data.mongodb.username}")
    private String mongoUsername;

    @Value("${spring.data.mongodb.authDatabase}")
    private String authDatabase;

    /**
     * Get comprehensive database and tenant information
     */
    @GetMapping("/database-info")
    public ResponseEntity<?> getDatabaseInfo() {
        Map<String, Object> info = new HashMap<>();
        
        try {
            // Current tenant context
            String currentTenant = TenantContext.getCurrentTenant();
            String currentDatabase = TenantContext.getCurrentDatabase();
            boolean multiTenancyEnabled = TenantContext.isMultiTenancyEnabled();
            String actualDatabaseName = mongoTemplate.getDb().getName();
            
            info.put("tenant", currentTenant);
            info.put("expectedDatabase", currentDatabase);
            info.put("actualDatabase", actualDatabaseName);
            info.put("multiTenancyEnabled", multiTenancyEnabled);
            info.put("configuredDefaultDatabase", defaultDatabase);
            info.put("mongoUsername", mongoUsername);
            info.put("authDatabase", authDatabase);
            info.put("timestamp", new Date());

            // Database collections info
            try {
                Set<String> collectionNames = mongoTemplate.getDb().listCollectionNames().into(new HashSet<>());
                info.put("collections", collectionNames);
                info.put("collectionCount", collectionNames.size());
                
                // User collection stats
                if (collectionNames.contains("user")) {
                    long userCount = mongoTemplate.getCollection("user").countDocuments();
                    info.put("userCount", userCount);
                } else {
                    info.put("userCount", "Collection 'user' not found");
                }
            } catch (Exception e) {
                info.put("collectionsError", e.getMessage());
            }

            // List available databases (requires admin privileges)
            try {
                List<String> databaseNames = new ArrayList<>();
                mongoClient.listDatabaseNames().forEach(databaseNames::add);
                info.put("availableDatabases", databaseNames);
                info.put("databaseCount", databaseNames.size());
            } catch (Exception e) {
                info.put("databaseListError", e.getMessage());
                info.put("databaseListNote", "Admin privileges required to list all databases");
            }

            logger.info("🔍 DATABASE INFO - Current: {}, Expected: {}, Multi-tenancy: {}", 
                       actualDatabaseName, currentDatabase, multiTenancyEnabled);

            return ResponseEntity.ok(info);

        } catch (Exception e) {
            logger.error("❌ Error getting database info", e);
            info.put("error", e.getMessage());
            info.put("errorType", e.getClass().getSimpleName());
            return ResponseEntity.status(500).body(info);
        }
    }

    /**
     * Check user existence in specific database
     */
    @PostMapping("/check-user-in-database")
    public ResponseEntity<?> checkUserInDatabase(@RequestBody Map<String, String> request) {
        String username = request.get("username");
        String databaseName = request.get("database");
        
        if (username == null || username.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "Username is required"));
        }
        
        if (databaseName == null || databaseName.trim().isEmpty()) {
            databaseName = defaultDatabase;
        }

        Map<String, Object> result = new HashMap<>();
        
        try {
            // Get the specific database
            MongoDatabase database = mongoClient.getDatabase(databaseName);
            
            result.put("username", username);
            result.put("database", databaseName);
            result.put("timestamp", new Date());

            // Check if user collection exists
            boolean userCollectionExists = database.listCollectionNames()
                    .into(new ArrayList<>()).contains("user");
            result.put("userCollectionExists", userCollectionExists);

            if (userCollectionExists) {
                // Count total users
                long totalUsers = database.getCollection("user").countDocuments();
                result.put("totalUsers", totalUsers);

                // Check specific user
                org.bson.Document userQuery = new org.bson.Document("username", username);
                org.bson.Document userDoc = database.getCollection("user").find(userQuery).first();
                
                result.put("userFound", userDoc != null);
                
                if (userDoc != null) {
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("id", userDoc.getObjectId("_id").toString());
                    userInfo.put("username", userDoc.getString("username"));
                    userInfo.put("active", userDoc.getBoolean("active", false));
                    userInfo.put("enabled", userDoc.getBoolean("enabled", false));
                    userInfo.put("firstName", userDoc.getString("firstName"));
                    userInfo.put("lastName", userDoc.getString("lastName"));
                    userInfo.put("email", userDoc.getString("email"));
                    userInfo.put("hasPassword", userDoc.getString("password") != null);
                    result.put("userDetails", userInfo);
                }
            }

            logger.info("🔍 USER CHECK - Username: {}, Database: {}, Found: {}", 
                       username, databaseName, result.get("userFound"));

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("❌ Error checking user in database: {}", databaseName, e);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * Test database connectivity and permissions
     */
    @GetMapping("/test-connectivity")
    public ResponseEntity<?> testConnectivity() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("timestamp", new Date());
            
            // Test current database connection
            String currentDbName = mongoTemplate.getDb().getName();
            result.put("currentDatabase", currentDbName);
            result.put("currentDatabaseConnected", true);

            // Test admin database access
            try {
                MongoDatabase adminDb = mongoClient.getDatabase("admin");
                long adminCollectionCount = adminDb.listCollectionNames().into(new ArrayList<>()).size();
                result.put("adminDatabaseAccess", true);
                result.put("adminCollectionCount", adminCollectionCount);
            } catch (Exception e) {
                result.put("adminDatabaseAccess", false);
                result.put("adminDatabaseError", e.getMessage());
            }

            // Test default database access
            try {
                MongoDatabase defaultDb = mongoClient.getDatabase(defaultDatabase);
                long defaultCollectionCount = defaultDb.listCollectionNames().into(new ArrayList<>()).size();
                result.put("defaultDatabaseAccess", true);
                result.put("defaultDatabaseName", defaultDatabase);
                result.put("defaultCollectionCount", defaultCollectionCount);
            } catch (Exception e) {
                result.put("defaultDatabaseAccess", false);
                result.put("defaultDatabaseError", e.getMessage());
            }

            // Test tenant context
            result.put("tenantContext", TenantContext.getCurrentTenant());
            result.put("tenantDatabase", TenantContext.getCurrentDatabase());
            result.put("multiTenancyEnabled", TenantContext.isMultiTenancyEnabled());

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("❌ Error testing connectivity", e);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            return ResponseEntity.status(500).body(result);
        }
    }
}
