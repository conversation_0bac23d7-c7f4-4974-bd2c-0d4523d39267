package lk.sout.core.controller;

import lk.sout.core.entity.DailyStatistics;
import lk.sout.core.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * REST controller for accessing business statistics
 */
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;

    /**
     * Generate statistics for the current date
     * @return The generated statistics
     */
    @RequestMapping(value = "/generate", method = RequestMethod.GET)
    public ResponseEntity<?> generateStatistics() {
        try {
            DailyStatistics statistics = statisticsService.generateDailyStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate statistics for a specific date
     * @param date The date to generate statistics for (format: yyyy-MM-dd)
     * @return The generated statistics
     */
    @RequestMapping(value = "/generateForDate", method = RequestMethod.GET)
    public ResponseEntity<?> generateStatisticsForDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            DailyStatistics statistics = statisticsService.generateStatisticsForDate(date);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get statistics for a specific date
     * @param date The date (format: yyyy-MM-dd)
     * @return DailyStatistics for the date
     */
    @RequestMapping(value = "/getByDate", method = RequestMethod.GET)
    public ResponseEntity<?> getStatisticsByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            DailyStatistics statistics = statisticsService.findByDate(date);
            if (statistics == null) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get statistics for a date range
     * @param startDate Start date (inclusive, format: yyyy-MM-dd)
     * @param endDate End date (inclusive, format: yyyy-MM-dd)
     * @return List of DailyStatistics for the date range
     */
    @RequestMapping(value = "/getByDateRange", method = RequestMethod.GET)
    public ResponseEntity<?> getStatisticsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            List<DailyStatistics> statistics = statisticsService.findByDateRange(startDate, endDate);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get statistics for a date range with pagination
     * @param startDate Start date (inclusive, format: yyyy-MM-dd)
     * @param endDate End date (inclusive, format: yyyy-MM-dd)
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of DailyStatistics for the date range
     */
    @RequestMapping(value = "/getByDateRangePaged", method = RequestMethod.GET)
    public ResponseEntity<?> getStatisticsByDateRangePaged(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "date"));
            Page<DailyStatistics> statistics = statisticsService.findByDateRange(startDate, endDate, pageable);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get latest statistics with pagination
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of DailyStatistics ordered by date descending
     */
    @RequestMapping(value = "/getLatest", method = RequestMethod.GET)
    public ResponseEntity<?> getLatestStatistics(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<DailyStatistics> statistics = statisticsService.findLatest(pageable);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get today's statistics
     * @return DailyStatistics for today
     */
    @RequestMapping(value = "/getToday", method = RequestMethod.GET)
    public ResponseEntity<?> getTodayStatistics() {
        try {
            DailyStatistics statistics = statisticsService.findByDate(LocalDate.now());
            if (statistics == null) {
                // Generate statistics if not found
                statistics = statisticsService.generateDailyStatistics();
            }
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
