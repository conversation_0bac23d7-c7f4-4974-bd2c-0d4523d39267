package lk.sout.core.controller;

import lk.sout.core.entity.UserSettings;
import lk.sout.core.service.UserService;
import lk.sout.core.service.UserSettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST controller for managing user settings
 */
@RestController
@RequestMapping("/userSettings")
public class UserSettingsController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserSettingsController.class);

    @Autowired
    private UserSettingsService userSettingsService;

    @Autowired
    private UserService userService;

    /**
     * Get settings for the current user
     * @return UserSettings for the current user
     */
    @RequestMapping(value = "/getCurrentUserSettings", method = RequestMethod.GET)
    public ResponseEntity<?> getCurrentUserSettings() {
        try {
            return ResponseEntity.ok(userSettingsService.findForCurrentUser());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get settings for a specific user
     * @param username The username
     * @return UserSettings for the user
     */
    @RequestMapping(value = "/getUserSettings", method = RequestMethod.GET)
    public ResponseEntity<?> getUserSettings(@RequestParam String username) {
        try {
            return ResponseEntity.ok(userSettingsService.findByUsername(username));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update a setting for the current user
     * @param key Setting key
     * @param value Setting value
     * @param enabled Whether the setting is enabled
     * @return true if successful
     */
    @RequestMapping(value = "/updateSetting", method = RequestMethod.POST)
    public ResponseEntity<?> updateSetting(
            @RequestParam String key,
            @RequestParam String value,
            @RequestParam(required = false, defaultValue = "true") boolean enabled) {
        try {
            return ResponseEntity.ok(userSettingsService.updateSetting(key, value, enabled));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update multiple settings for the current user
     * @param request Map of setting keys to values and enabled status
     * @return true if successful
     */
    @RequestMapping(value = "/updateSettings", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<?> updateSettings(@RequestBody UpdateSettingsRequest request) {
        try {
            return ResponseEntity.ok(userSettingsService.updateSettings(request.getSettings(), request.isEnabled()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Enable or disable a setting for the current user
     * @param key Setting key
     * @param enabled Whether the setting should be enabled
     * @return true if successful
     */
    @RequestMapping(value = "/setSettingEnabled", method = RequestMethod.POST)
    public ResponseEntity<?> setSettingEnabled(@RequestParam String key, @RequestParam boolean enabled) {
        try {
            return ResponseEntity.ok(userSettingsService.setSettingEnabled(key, enabled));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get a setting value for the current user
     * @param key Setting key
     * @return Setting value
     */
    @RequestMapping(value = "/getSettingValue", method = RequestMethod.GET)
    public ResponseEntity<?> getSettingValue(@RequestParam String key) {
        try {
            return ResponseEntity.ok(userSettingsService.getSettingValue(key));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Check if a setting is enabled for the current user
     * @param key Setting key
     * @return true if setting exists and is enabled
     */
    @RequestMapping(value = "/isSettingEnabled", method = RequestMethod.GET)
    public ResponseEntity<?> isSettingEnabled(@RequestParam String key) {
        try {
            return ResponseEntity.ok(userSettingsService.isSettingEnabled(key));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get all settings for the current user
     * @return Map of setting keys to values (only enabled settings)
     */
    @RequestMapping(value = "/getAllSettings", method = RequestMethod.GET)
    public ResponseEntity<?> getAllSettings() {
        try {
            return ResponseEntity.ok(userSettingsService.getAllSettings());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update a setting for a specific user (admin only)
     * @param username The username
     * @param key Setting key
     * @param value Setting value
     * @param enabled Whether the setting is enabled
     * @return true if successful
     */
    @RequestMapping(value = "/updateSettingForUser", method = RequestMethod.POST)
    public ResponseEntity<?> updateSettingForUser(
            @RequestParam String username,
            @RequestParam String key,
            @RequestParam String value,
            @RequestParam(required = false, defaultValue = "true") boolean enabled) {
        try {
            // Check if current user is admin
            if (!userService.isAdmin()) {
                LOGGER.warn("Non-admin user attempted to update settings for user: {}", username);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            LOGGER.info("Admin updating setting for user: {}, key: {}, value: {}, enabled: {}", username, key, value, enabled);
            return ResponseEntity.ok(userSettingsService.updateSettingForUser(username, key, value, enabled));
        } catch (Exception e) {
            LOGGER.error("Error updating setting for user: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update multiple settings for a specific user (admin only)
     * @param request Map of setting keys to values and enabled status
     * @return true if successful
     */
    @RequestMapping(value = "/updateSettingsForUser", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<?> updateSettingsForUser(@RequestBody UpdateSettingsForUserRequest request) {
        try {
            // Check if current user is admin
            if (!userService.isAdmin()) {
                LOGGER.warn("Non-admin user attempted to update settings for user: {}", request.getUsername());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            LOGGER.info("Admin updating settings for user: {}", request.getUsername());
            return ResponseEntity.ok(userSettingsService.updateSettingsForUser(
                    request.getUsername(), request.getSettings(), request.isEnabled()));
        } catch (Exception e) {
            LOGGER.error("Error updating settings for user: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Enable or disable a setting for a specific user (admin only)
     * @param username The username
     * @param key Setting key
     * @param enabled Whether the setting should be enabled
     * @return true if successful
     */
    @RequestMapping(value = "/setSettingEnabledForUser", method = RequestMethod.POST)
    public ResponseEntity<?> setSettingEnabledForUser(
            @RequestParam String username,
            @RequestParam String key,
            @RequestParam boolean enabled) {
        try {
            // Check if current user is admin
            if (!userService.isAdmin()) {
                LOGGER.warn("Non-admin user attempted to set setting enabled for user: {}", username);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            LOGGER.info("Admin setting enabled for user: {}, key: {}, enabled: {}", username, key, enabled);
            return ResponseEntity.ok(userSettingsService.setSettingEnabledForUser(username, key, enabled));
        } catch (Exception e) {
            LOGGER.error("Error setting enabled for user: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get a setting value for a specific user (admin only)
     * @param username The username
     * @param key Setting key
     * @return Setting value or null if not found or disabled
     */
    @RequestMapping(value = "/getSettingValueForUser", method = RequestMethod.GET)
    public ResponseEntity<?> getSettingValueForUser(@RequestParam String username, @RequestParam String key) {
        try {
            // Check if current user is admin
            if (!userService.isAdmin()) {
                LOGGER.warn("Non-admin user attempted to get setting value for user: {}", username);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            return ResponseEntity.ok(userSettingsService.getSettingValueForUser(username, key));
        } catch (Exception e) {
            LOGGER.error("Error getting setting value for user: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Check if a setting is enabled for a specific user (admin only)
     * @param username The username
     * @param key Setting key
     * @return true if setting exists and is enabled, false otherwise
     */
    @RequestMapping(value = "/isSettingEnabledForUser", method = RequestMethod.GET)
    public ResponseEntity<?> isSettingEnabledForUser(@RequestParam String username, @RequestParam String key) {
        try {
            // Check if current user is admin
            if (!userService.isAdmin()) {
                LOGGER.warn("Non-admin user attempted to check if setting is enabled for user: {}", username);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            return ResponseEntity.ok(userSettingsService.isSettingEnabledForUser(username, key));
        } catch (Exception e) {
            LOGGER.error("Error checking if setting is enabled for user: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get all settings for a specific user (admin only)
     * @param username The username
     * @return Map of setting keys to values (only enabled settings)
     */
    @RequestMapping(value = "/getAllSettingsForUser", method = RequestMethod.GET)
    public ResponseEntity<?> getAllSettingsForUser(@RequestParam String username) {
        try {
            // Check if current user is admin
            if (!userService.isAdmin()) {
                LOGGER.warn("Non-admin user attempted to get all settings for user: {}", username);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            return ResponseEntity.ok(userSettingsService.getAllSettingsForUser(username));
        } catch (Exception e) {
            LOGGER.error("Error getting all settings for user: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Request class for updating multiple settings
     */
    public static class UpdateSettingsRequest {
        private Map<String, String> settings;
        private boolean enabled = true;

        public Map<String, String> getSettings() {
            return settings;
        }

        public void setSettings(Map<String, String> settings) {
            this.settings = settings;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }

    /**
     * Request class for updating multiple settings for a specific user
     */
    public static class UpdateSettingsForUserRequest {
        private String username;
        private Map<String, String> settings;
        private boolean enabled = true;

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public Map<String, String> getSettings() {
            return settings;
        }

        public void setSettings(Map<String, String> settings) {
            this.settings = settings;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }
}
