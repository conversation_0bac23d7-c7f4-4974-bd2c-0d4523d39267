package lk.sout.core.entity;

import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> on 3/21/2019
 */
@Component
public class Response {

    int code;

    String message;

    boolean success;

    private String data;

    /**
     * Default no-argument constructor required by Spring
     */
    public Response() {
        // Default constructor required by Spring
    }

    public Response(int code, String message, boolean success, String data) {
        this.code = code;
        this.message = message;
        this.success = success;
        this.data = data;
    }

    /**
     * Constructor with code and message only
     */
    public Response(int code, String message) {
        this.code = code;
        this.message = message;
        this.success = false;
        this.data = null;
    }

    /**
     * Constructor with code, message and success flag
     */
    public Response(int code, String message, boolean success) {
        this.code = code;
        this.message = message;
        this.success = success;
        this.data = null;
    }

    /**
     * Constructor with code, message and generic data object
     * This constructor handles the case where data is null
     */
    public Response(int code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.success = true;
        this.data = data != null ? data.toString() : null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
