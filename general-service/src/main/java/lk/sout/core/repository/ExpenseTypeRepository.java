package lk.sout.core.repository;

import lk.sout.core.entity.ExpenseType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExpenseTypeRepository extends MongoRepository<ExpenseType, String> {

    Page<ExpenseType> findAll(Pageable pageable);

    List<ExpenseType> findAllByNameLikeIgnoreCase(String name);

    List<ExpenseType> findAllByCategory(String categoryId);

}

