package lk.sout.core.repository;


import lk.sout.core.entity.MetaData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 4/18/2018
 */
@Repository
public interface MetaDataRepository extends MongoRepository<MetaData, String> {

    MetaData findByValueAndCategory(String value, String category);

    MetaData findByValue(String value);

    List<MetaData> findByCategory(String category);

    MetaData findMetaDataById(String s);
}
