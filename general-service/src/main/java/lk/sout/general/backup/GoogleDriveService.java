package lk.sout.general.backup;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.FileContent;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.model.File;
import com.google.api.services.drive.model.FileList;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

/**
 * Service for Google Drive operations
 */
@Service
public class GoogleDriveService {

    private static final Logger logger = LoggerFactory.getLogger(GoogleDriveService.class);
    private static final String APPLICATION_NAME = "ERP Database Backup";

    @Value("${google.drive.credentials.path:#{null}}")
    private String credentialsPath;

    @Value("${google.drive.folder.id:#{null}}")
    private String backupFolderId;

    private Drive driveService;

    /**
     * Initialize Google Drive service
     */
    private Drive getDriveService() throws IOException, GeneralSecurityException {
        if (driveService == null) {
            if (credentialsPath == null) {
                throw new IllegalStateException("Google Drive credentials path not configured");
            }

            GoogleCredentials credentials = GoogleCredentials
                    .fromStream(new FileInputStream(credentialsPath))
                    .createScoped(Collections.singletonList("https://www.googleapis.com/auth/drive.file"));

            driveService = new Drive.Builder(
                    GoogleNetHttpTransport.newTrustedTransport(),
                    GsonFactory.getDefaultInstance(),
                    new HttpCredentialsAdapter(credentials))
                    .setApplicationName(APPLICATION_NAME)
                    .build();
        }
        return driveService;
    }

    /**
     * Upload file to Google Drive
     */
    public String uploadFile(java.io.File localFile, String fileName) throws IOException, GeneralSecurityException {
        Drive service = getDriveService();

        File fileMetadata = new File();
        fileMetadata.setName(fileName);
        
        if (backupFolderId != null) {
            fileMetadata.setParents(Collections.singletonList(backupFolderId));
        }

        FileContent mediaContent = new FileContent("application/gzip", localFile);

        File uploadedFile = service.files().create(fileMetadata, mediaContent)
                .setFields("id,name,createdTime")
                .execute();

        logger.info("✅ File uploaded to Google Drive: {} (ID: {})", fileName, uploadedFile.getId());
        return uploadedFile.getId();
    }

    /**
     * Delete files older than specified days
     */
    public void deleteOldFiles(int daysToKeep) throws IOException, GeneralSecurityException {
        Drive service = getDriveService();

        // Calculate cutoff date
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);
        String cutoffDateStr = cutoffDate.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

        // Search for backup files older than cutoff date
        String query = "name contains 'backup-' and createdTime < '" + cutoffDateStr + "'";
        if (backupFolderId != null) {
            query += " and '" + backupFolderId + "' in parents";
        }

        FileList result = service.files().list()
                .setQ(query)
                .setFields("files(id,name,createdTime)")
                .execute();

        List<File> files = result.getFiles();
        
        for (File file : files) {
            try {
                service.files().delete(file.getId()).execute();
                logger.info("🗑️ Deleted old backup file: {} (Created: {})", file.getName(), file.getCreatedTime());
            } catch (Exception e) {
                logger.error("❌ Failed to delete file: {} - {}", file.getName(), e.getMessage());
            }
        }

        logger.info("📊 Cleanup completed. Deleted {} old backup files", files.size());
    }

    /**
     * List backup files in Google Drive
     */
    public List<File> listBackupFiles() throws IOException, GeneralSecurityException {
        Drive service = getDriveService();

        String query = "name contains 'backup-'";
        if (backupFolderId != null) {
            query += " and '" + backupFolderId + "' in parents";
        }

        FileList result = service.files().list()
                .setQ(query)
                .setOrderBy("createdTime desc")
                .setFields("files(id,name,createdTime,size)")
                .execute();

        return result.getFiles();
    }
}
