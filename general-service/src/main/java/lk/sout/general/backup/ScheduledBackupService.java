package lk.sout.general.backup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Scheduled backup service that runs daily at 3:00 AM
 */
@Service
public class ScheduledBackupService {

    private static final Logger logger = LoggerFactory.getLogger(ScheduledBackupService.class);

    @Autowired
    private MongoBackupService mongoBackupService;

    @Autowired
    private GoogleDriveService googleDriveService;

    @Value("${backup.retention.days:7}")
    private int retentionDays;

    @Value("${backup.enabled:true}")
    private boolean backupEnabled;

    /**
     * Scheduled backup task - runs daily at 3:00 AM
     * Cron expression: "0 0 3 * * ?" = second minute hour day month weekday
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void performScheduledBackup() {
        if (!backupEnabled) {
            logger.info("⏸️ Backup is disabled via configuration");
            return;
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
        logger.info("🕒 Starting scheduled backup at {}", timestamp);

        try {
            // Step 1: Create MongoDB backup
            logger.info("📦 Creating MongoDB backup...");
            File backupFile = mongoBackupService.createBackup();

            // Step 2: Upload to Google Drive
            logger.info("☁️ Uploading backup to Google Drive...");
            String fileName = backupFile.getName();
            String fileId = googleDriveService.uploadFile(backupFile, fileName);

            // Step 3: Delete old backups from Google Drive
            logger.info("🗑️ Cleaning up old backups (keeping {} days)...", retentionDays);
            googleDriveService.deleteOldFiles(retentionDays);

            // Step 4: Delete local backup file
            if (backupFile.delete()) {
                logger.info("🗑️ Local backup file deleted: {}", fileName);
            } else {
                logger.warn("⚠️ Failed to delete local backup file: {}", fileName);
            }

            logger.info("✅ Scheduled backup completed successfully!");
            logger.info("📊 Backup Summary:");
            logger.info("   📁 File: {}", fileName);
            logger.info("   🆔 Google Drive ID: {}", fileId);
            logger.info("   📅 Retention: {} days", retentionDays);

        } catch (Exception e) {
            logger.error("❌ Scheduled backup failed: {}", e.getMessage(), e);
            // TODO: Send alert notification (email, Slack, etc.)
        }
    }

    /**
     * Manual backup trigger (for testing)
     */
    public void performManualBackup() {
        logger.info("🔧 Manual backup triggered");
        performScheduledBackup();
    }

    /**
     * Get backup status
     */
    public BackupStatus getBackupStatus() {
        try {
            BackupStatus status = new BackupStatus();
            status.setEnabled(backupEnabled);
            status.setRetentionDays(retentionDays);
            status.setDatabases(mongoBackupService.getDatabaseList());
            status.setLastCheck(LocalDateTime.now());
            
            // Get list of backup files from Google Drive
            status.setBackupFiles(googleDriveService.listBackupFiles());
            
            return status;
        } catch (Exception e) {
            logger.error("Failed to get backup status: {}", e.getMessage());
            BackupStatus errorStatus = new BackupStatus();
            errorStatus.setEnabled(false);
            errorStatus.setError(e.getMessage());
            return errorStatus;
        }
    }

    /**
     * Backup status data class
     */
    public static class BackupStatus {
        private boolean enabled;
        private int retentionDays;
        private String[] databases;
        private LocalDateTime lastCheck;
        private java.util.List<com.google.api.services.drive.model.File> backupFiles;
        private String error;

        // Getters and setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public int getRetentionDays() { return retentionDays; }
        public void setRetentionDays(int retentionDays) { this.retentionDays = retentionDays; }

        public String[] getDatabases() { return databases; }
        public void setDatabases(String[] databases) { this.databases = databases; }

        public LocalDateTime getLastCheck() { return lastCheck; }
        public void setLastCheck(LocalDateTime lastCheck) { this.lastCheck = lastCheck; }

        public java.util.List<com.google.api.services.drive.model.File> getBackupFiles() { return backupFiles; }
        public void setBackupFiles(java.util.List<com.google.api.services.drive.model.File> backupFiles) { this.backupFiles = backupFiles; }

        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}
