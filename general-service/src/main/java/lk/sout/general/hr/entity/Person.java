package lk.sout.general.hr.entity;

import lk.sout.config.CascadeSave;
import lk.sout.core.entity.MetaData;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Created by <PERSON><PERSON>a Weerasinghe on 4/24/2018
 */
@Document
@Component
public class Person {

    @Id
    private String id;

    private String firstName;

    private String lastName;

    private String nic;

    private String drivingLicenseNo;

    private Date dob;

    private String gender;

    private double outStanding;

    @CascadeSave
    @DBRef
    private Contact contact;

    @DBRef
    private MetaData personType;

    private String licenseFrontView;
    private String licenseBackView;

    private boolean active;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNic() {
        return nic;
    }

    public void setNic(String nic) {
        this.nic = nic;
    }

    public Date getDob() {
        return dob;
    }

    public void setDob(Date dob) {
        this.dob = dob;
    }

    public Contact getContact() {
        return contact;
    }

    public void setContact(Contact contact) {
        this.contact = contact;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public MetaData getPersonType() {
        return personType;
    }

    public void setPersonType(MetaData personType) {
        this.personType = personType;
    }

    public boolean getActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getDrivingLicenseNo() {
        return drivingLicenseNo;
    }

    public void setDrivingLicenseNo(String drivingLicenseNo) {
        this.drivingLicenseNo = drivingLicenseNo;
    }

    public double getOutStanding() {
        return outStanding;
    }

    public void setOutStanding(double outStanding) {
        this.outStanding = outStanding;
    }

    public String getLicenseFrontView() {
        return licenseFrontView;
    }

    public void setLicenseFrontView(String licenseFrontView) {
        this.licenseFrontView = licenseFrontView;
    }

    public String getLicenseBackView() {
        return licenseBackView;
    }

    public void setLicenseBackView(String licenseBackView) {
        this.licenseBackView = licenseBackView;
    }


}
