package lk.sout.general.hr.service;

import lk.sout.general.hr.entity.Department;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface DepartmentService {
    boolean save(Department department);
    Optional<Department> findDepartment(String s);
    Iterable<Department> findAll(Pageable pageable);
    List<Department> findAllDepartment();
    boolean findByDepartmentName(String name);
}
