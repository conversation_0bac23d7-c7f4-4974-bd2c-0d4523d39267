package lk.sout.general.hr.repository;

import lk.sout.general.hr.entity.Employee;
import lk.sout.general.hr.entity.LeaveRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Date;
import java.util.List;

public interface LeaveRequestRepository extends MongoRepository<LeaveRequest, String> {
    Page<LeaveRequest> findAll(Pageable pageable);


    List<LeaveRequest> findByEpfLikeIgnoreCase(String any);

    List<LeaveRequest> findByFromAfterAndToBeforeAndEmployee(Date from, Date to, Employee employee);
}
