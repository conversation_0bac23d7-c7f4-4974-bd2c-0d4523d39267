package lk.sout.general.hr.controller;

import lk.sout.general.hr.entity.Leave;
import lk.sout.general.hr.service.LeaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/addLeaveTypes")
public class LeaveController {

    @Autowired
    LeaveService leaveService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Leave leave){
        try{
            return  ResponseEntity.ok(leaveService.save(leave));
        }catch (Exception e){
            return  ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }

    @RequestMapping(value = "/findAll" ,method = RequestMethod.GET)
    private ResponseEntity<?>findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(leaveService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getAll" ,method = RequestMethod.GET)
    private ResponseEntity<?>getAll() {
        try {
            return ResponseEntity.ok(leaveService.findAll());
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    private ResponseEntity<?> findById (@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(leaveService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByName", method = RequestMethod.GET)
    private ResponseEntity<?> findByName (@RequestParam("leaveType") String leaveType) {
        try {
            return ResponseEntity.ok(leaveService.findByName(leaveType));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }





}
