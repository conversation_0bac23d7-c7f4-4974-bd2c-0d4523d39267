package lk.sout.general.hr.repository;


import lk.sout.general.hr.entity.Designation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DesignationRepository extends MongoRepository<Designation, String> {
//  List<Designation> findAllByDesignationNameLikeIgnoreCase(String name);

    Page<Designation> findAll(Pageable pageable);


    List<Designation> findAllByDesignationNameLikeIgnoreCase(String name);

    Designation findByDesignationName(String name);
}
