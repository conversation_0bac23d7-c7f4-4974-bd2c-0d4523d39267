package lk.sout.general.hr.controller;

import lk.sout.general.hr.entity.Department;
import lk.sout.general.hr.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/department")
public class DepartmentController {

    @Autowired
    DepartmentService departmentService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody

    private ResponseEntity<?> save(@RequestBody Department department){
        try{
            return  ResponseEntity.ok(departmentService.save(department));
        }catch (Exception e){
            return  ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }
    @RequestMapping(value = "/findAll" ,method = RequestMethod.GET)
    private ResponseEntity<?>findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(departmentService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/getAll" ,method = RequestMethod.GET)
    private ResponseEntity<?>getAll() {
        try {
            return ResponseEntity.ok(departmentService.findAllDepartment());
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/findById" ,method = RequestMethod.GET)
    private ResponseEntity<?>findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(departmentService.findDepartment(id));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDepartmentName", method = RequestMethod.GET)
    private ResponseEntity<?> findByDepartmentName (@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(departmentService.findByDepartmentName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
