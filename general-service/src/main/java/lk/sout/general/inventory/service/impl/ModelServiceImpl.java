package lk.sout.general.inventory.service.impl;

import lk.sout.general.inventory.entity.Model;
import lk.sout.general.inventory.repository.ModelRepository;
import lk.sout.general.inventory.service.BrandService;
import lk.sout.general.inventory.service.ModelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ModelServiceImpl implements ModelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrandService.class);

    @Autowired
    ModelRepository modelRepository;

    public boolean save(Model model) {
        try {
            model.setCode(String.valueOf(System.currentTimeMillis()));
            modelRepository.save(model);
            LOGGER.info("Brand saved. " + model.getName());
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("Saving Brand Failed. " + model.getName());
            return false;
        }
    }

    public boolean remove(Model model) {
        try {
            modelRepository.delete(model);
            LOGGER.info("Brand removed. " + model.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Brand Failed. " + model.getName());
            return false;
        }
    }

    public Iterable<Model> findAll(Integer page, Integer pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("id").descending());
            return modelRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All brands failed");
            return null;
        }
    }

    public Model findOne(String id) {
        try {
            Optional<Model> model = modelRepository.findById(id);
            return model.get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving a brand failed");
            return null;
        }
    }

    @Override
    public List<Model> findByName(String key) {
        try {
            return modelRepository.findByNameLikeIgnoreCaseAndActive(key, true);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a brand failed");
            return null;
        }
    }

    @Override
    public String delete(String id) {
        try {
            modelRepository.deleteById(id);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing brand failed " + id + ". " + ex.getMessage());
            return "failed";
        }
    }

}
