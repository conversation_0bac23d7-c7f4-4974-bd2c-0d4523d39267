package lk.sout.general.inventory.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.general.inventory.entity.SerialNumber;
import lk.sout.general.inventory.repository.SerialNumberRepository;
import lk.sout.general.inventory.service.SerialNumberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service implementation for SerialNumber management
 */
@Service
public class SerialNumberServiceImpl implements SerialNumberService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SerialNumberServiceImpl.class);

    @Autowired
    private SerialNumberRepository serialNumberRepository;

    @Autowired
    private Response response;

    @Override
    @Transactional
    public Response save(SerialNumber serialNumber) {
        try {
            // Check if serial number already exists
            if (serialNumberRepository.existsBySerialNumber(serialNumber.getSerialNumber())) {
                response.setCode(409);
                response.setSuccess(false);
                response.setMessage("Serial number already exists: " + serialNumber.getSerialNumber());
                return response;
            }

            serialNumberRepository.save(serialNumber);
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial number saved successfully");
            response.setData(serialNumber.getSerialNumber());
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error saving serial number: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to save serial number: " + ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response saveAll(List<SerialNumber> serialNumbers) {
        try {
            // Check for duplicate serial numbers
            List<String> serialNumberStrings = serialNumbers.stream()
                    .map(SerialNumber::getSerialNumber)
                    .collect(Collectors.toList());

            List<String> existingSerials = serialNumberRepository.findBySerialNumberIn(serialNumberStrings)
                    .stream()
                    .map(SerialNumber::getSerialNumber)
                    .collect(Collectors.toList());

            if (!existingSerials.isEmpty()) {
                response.setCode(409);
                response.setSuccess(false);
                response.setMessage("Duplicate serial numbers found: " + String.join(", ", existingSerials));
                return response;
            }

            List<SerialNumber> savedSerials = serialNumberRepository.saveAll(serialNumbers);
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers saved successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error saving serial numbers: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to save serial numbers: " + ex.getMessage());
            return response;
        }
    }

    @Override
    public Optional<SerialNumber> findById(String id) {
        try {
            return serialNumberRepository.findById(id);
        } catch (Exception ex) {
            LOGGER.error("Error finding serial number by ID: " + ex.getMessage(), ex);
            return Optional.empty();
        }
    }

    @Override
    public Optional<SerialNumber> findBySerialNumber(String serialNumber) {
        try {
            return serialNumberRepository.findBySerialNumber(serialNumber);
        } catch (Exception ex) {
            LOGGER.error("Error finding serial number: " + ex.getMessage(), ex);
            return Optional.empty();
        }
    }

    @Override
    public boolean existsBySerialNumber(String serialNumber) {
        try {
            return serialNumberRepository.existsBySerialNumber(serialNumber);
        } catch (Exception ex) {
            LOGGER.error("Error checking serial number existence: " + ex.getMessage(), ex);
            return false;
        }
    }

    @Override
    public List<SerialNumber> findByItemCode(String itemCode) {
        try {
            return serialNumberRepository.findByItemCode(itemCode);
        } catch (Exception ex) {
            LOGGER.error("Error finding serial numbers by item code: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SerialNumber> findByItemCodeAndStatus(String itemCode, String status) {
        try {
            return serialNumberRepository.findByItemCodeAndStatus(itemCode, status);
        } catch (Exception ex) {
            LOGGER.error("Error finding serial numbers by item code and status: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SerialNumber> findAvailableByItemCodeAndWarehouse(String itemCode, int warehouseCode) {
        try {
            return serialNumberRepository.findAvailableByItemCodeAndWarehouse(itemCode, warehouseCode);
        } catch (Exception ex) {
            LOGGER.error("Error finding available serial numbers: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SerialNumber> findSoldByItemCode(String itemCode) {
        try {
            return serialNumberRepository.findSoldByItemCode(itemCode);
        } catch (Exception ex) {
            LOGGER.error("Error finding sold serial numbers: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public long countAvailableByItemCodeAndWarehouse(String itemCode, int warehouseCode) {
        try {
            return serialNumberRepository.countAvailableByItemCodeAndWarehouse(itemCode, warehouseCode);
        } catch (Exception ex) {
            LOGGER.error("Error counting available serial numbers: " + ex.getMessage(), ex);
            return 0;
        }
    }

    @Override
    @Transactional
    public Response addSerialNumbersForPurchase(String itemCode, List<String> serialNumbers, 
                                              String purchaseInvoiceId, Double purchasePrice, 
                                              int warehouseCode, LocalDateTime warrantyExpiryDate) {
        try {
            List<SerialNumber> serialNumberEntities = new ArrayList<>();
            
            for (String serialNum : serialNumbers) {
                if (serialNumberRepository.existsBySerialNumber(serialNum)) {
                    response.setCode(409);
                    response.setSuccess(false);
                    response.setMessage("Serial number already exists: " + serialNum);
                    return response;
                }

                SerialNumber serialNumber = new SerialNumber();
                serialNumber.setSerialNumber(serialNum);
                serialNumber.setItemCode(itemCode);
                serialNumber.setStatus("AVAILABLE");
                serialNumber.setDateAdded(LocalDateTime.now());
                serialNumber.setPurchaseInvoiceId(purchaseInvoiceId);
                serialNumber.setPurchasePrice(purchasePrice);
                serialNumber.setWarehouseCode(warehouseCode);
                serialNumber.setWarrantyExpiryDate(warrantyExpiryDate);
                
                serialNumberEntities.add(serialNumber);
            }

            serialNumberRepository.saveAll(serialNumberEntities);
            
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers added successfully for purchase");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error adding serial numbers for purchase: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to add serial numbers: " + ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response markSerialNumbersAsSold(List<String> serialNumbers, String salesInvoiceId,
                                          Double sellingPrice, LocalDateTime dateSold,
                                          String customerId, String customerName, String customerNumber) {
        try {
            List<SerialNumber> serialNumberEntities = serialNumberRepository.findBySerialNumberIn(serialNumbers);

            if (serialNumberEntities.size() != serialNumbers.size()) {
                response.setCode(404);
                response.setSuccess(false);
                response.setMessage("Some serial numbers not found");
                return response;
            }

            for (SerialNumber serialNumber : serialNumberEntities) {
                if (!"AVAILABLE".equals(serialNumber.getStatus())) {
                    response.setCode(400);
                    response.setSuccess(false);
                    response.setMessage("Serial number not available for sale: " + serialNumber.getSerialNumber());
                    return response;
                }

                serialNumber.setStatus("SOLD");
                serialNumber.setSalesInvoiceId(salesInvoiceId);
                serialNumber.setSellingPrice(sellingPrice);
                serialNumber.setDateSold(dateSold);
                serialNumber.setCustomerId(customerId);
                serialNumber.setCustomerName(customerName);
                serialNumber.setCustomerNumber(customerNumber);
            }

            serialNumberRepository.saveAll(serialNumberEntities);

            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers marked as sold successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error marking serial numbers as sold: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to mark serial numbers as sold: " + ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response markSerialNumbersAsReturned(List<String> serialNumbers, String returnInvoiceId) {
        try {
            List<SerialNumber> serialNumberEntities = serialNumberRepository.findBySerialNumberIn(serialNumbers);
            
            for (SerialNumber serialNumber : serialNumberEntities) {
                serialNumber.setStatus("RETURNED");
                serialNumber.setReturnInvoiceId(returnInvoiceId);
            }

            serialNumberRepository.saveAll(serialNumberEntities);
            
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers marked as returned successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error marking serial numbers as returned: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to mark serial numbers as returned: " + ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response markSerialNumbersAsDamaged(List<String> serialNumbers, String notes) {
        try {
            List<SerialNumber> serialNumberEntities = serialNumberRepository.findBySerialNumberIn(serialNumbers);
            
            for (SerialNumber serialNumber : serialNumberEntities) {
                serialNumber.setStatus("DAMAGED");
                serialNumber.setNotes(notes);
            }

            serialNumberRepository.saveAll(serialNumberEntities);
            
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers marked as damaged successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error marking serial numbers as damaged: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to mark serial numbers as damaged: " + ex.getMessage());
            return response;
        }
    }

    @Override
    public Response validateSerialNumbersForQuantity(List<String> serialNumbers, Double quantity, String itemCode) {
        try {
            if (serialNumbers.size() != quantity.intValue()) {
                response.setCode(400);
                response.setSuccess(false);
                response.setMessage("Serial number count (" + serialNumbers.size() + 
                                  ") does not match quantity (" + quantity.intValue() + ")");
                return response;
            }

            // Check for duplicates in the provided list
            long distinctCount = serialNumbers.stream().distinct().count();
            if (distinctCount != serialNumbers.size()) {
                response.setCode(400);
                response.setSuccess(false);
                response.setMessage("Duplicate serial numbers found in the list");
                return response;
            }

            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers validation successful");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error validating serial numbers: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to validate serial numbers: " + ex.getMessage());
            return response;
        }
    }

    @Override
    public long getAvailableStockCount(String itemCode, int warehouseCode) {
        try {
            return serialNumberRepository.countAvailableByItemCodeAndWarehouse(itemCode, warehouseCode);
        } catch (Exception ex) {
            LOGGER.error("Error getting available stock count: " + ex.getMessage(), ex);
            return 0;
        }
    }

    @Override
    public List<SerialNumber> searchSerialNumbers(String searchTerm) {
        try {
            return serialNumberRepository.findBySerialNumberContainingIgnoreCase(searchTerm);
        } catch (Exception ex) {
            LOGGER.error("Error searching serial numbers: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SerialNumber> findWarrantyExpiringSoon(int daysAhead) {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime futureDate = now.plusDays(daysAhead);
            return serialNumberRepository.findByWarrantyExpiringBetween(now, futureDate);
        } catch (Exception ex) {
            LOGGER.error("Error finding warranty expiring soon: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SerialNumber> findExpiredWarranty() {
        try {
            return serialNumberRepository.findByWarrantyExpiredBefore(LocalDateTime.now());
        } catch (Exception ex) {
            LOGGER.error("Error finding expired warranty: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public Page<SerialNumber> findAllPaginated(Pageable pageable) {
        try {
            return serialNumberRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Error finding all paginated serial numbers: " + ex.getMessage(), ex);
            return Page.empty();
        }
    }

    @Override
    public Page<SerialNumber> findByItemCodePaginated(String itemCode, Pageable pageable) {
        try {
            return serialNumberRepository.findByItemCode(itemCode, pageable);
        } catch (Exception ex) {
            LOGGER.error("Error finding paginated serial numbers by item code: " + ex.getMessage(), ex);
            return Page.empty();
        }
    }

    @Override
    public Page<SerialNumber> findByStatusPaginated(String status, Pageable pageable) {
        try {
            return serialNumberRepository.findByStatus(status, pageable);
        } catch (Exception ex) {
            LOGGER.error("Error finding paginated serial numbers by status: " + ex.getMessage(), ex);
            return Page.empty();
        }
    }

    @Override
    public Page<SerialNumber> findByWarehousePaginated(int warehouseCode, Pageable pageable) {
        try {
            return serialNumberRepository.findByWarehouseCode(warehouseCode, pageable);
        } catch (Exception ex) {
            LOGGER.error("Error finding paginated serial numbers by warehouse: " + ex.getMessage(), ex);
            return Page.empty();
        }
    }

    @Override
    @Transactional
    public Response delete(String id) {
        try {
            if (!serialNumberRepository.existsById(id)) {
                response.setCode(404);
                response.setSuccess(false);
                response.setMessage("Serial number not found");
                return response;
            }

            serialNumberRepository.deleteById(id);
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial number deleted successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error deleting serial number: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to delete serial number: " + ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response deleteByItemCode(String itemCode) {
        try {
            serialNumberRepository.deleteByItemCode(itemCode);
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers deleted successfully for item: " + itemCode);
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error deleting serial numbers by item code: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to delete serial numbers: " + ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response updateStatus(String serialNumber, String newStatus) {
        try {
            Optional<SerialNumber> serialNumberOpt = serialNumberRepository.findBySerialNumber(serialNumber);
            if (!serialNumberOpt.isPresent()) {
                response.setCode(404);
                response.setSuccess(false);
                response.setMessage("Serial number not found: " + serialNumber);
                return response;
            }

            SerialNumber serialNumberEntity = serialNumberOpt.get();
            serialNumberEntity.setStatus(newStatus);
            serialNumberRepository.save(serialNumberEntity);

            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial number status updated successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error updating serial number status: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to update serial number status: " + ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response updateWarranty(String serialNumber, LocalDateTime warrantyExpiryDate) {
        try {
            Optional<SerialNumber> serialNumberOpt = serialNumberRepository.findBySerialNumber(serialNumber);
            if (!serialNumberOpt.isPresent()) {
                response.setCode(404);
                response.setSuccess(false);
                response.setMessage("Serial number not found: " + serialNumber);
                return response;
            }

            SerialNumber serialNumberEntity = serialNumberOpt.get();
            serialNumberEntity.setWarrantyExpiryDate(warrantyExpiryDate);
            serialNumberRepository.save(serialNumberEntity);

            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial number warranty updated successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error updating serial number warranty: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to update serial number warranty: " + ex.getMessage());
            return response;
        }
    }

    @Override
    public SerialNumberStats getSerialNumberStats(String itemCode) {
        try {
            List<SerialNumber> allSerials = serialNumberRepository.findByItemCode(itemCode);
            
            long totalCount = allSerials.size();
            long availableCount = allSerials.stream().mapToLong(s -> "AVAILABLE".equals(s.getStatus()) ? 1 : 0).sum();
            long soldCount = allSerials.stream().mapToLong(s -> "SOLD".equals(s.getStatus()) ? 1 : 0).sum();
            long returnedCount = allSerials.stream().mapToLong(s -> "RETURNED".equals(s.getStatus()) ? 1 : 0).sum();
            long damagedCount = allSerials.stream().mapToLong(s -> "DAMAGED".equals(s.getStatus()) ? 1 : 0).sum();

            return new SerialNumberStats(totalCount, availableCount, soldCount, returnedCount, damagedCount);
        } catch (Exception ex) {
            LOGGER.error("Error getting serial number stats: " + ex.getMessage(), ex);
            return new SerialNumberStats(0, 0, 0, 0, 0);
        }
    }

    @Override
    public Response validateSerialNumbersForStockOperation(String itemCode, List<String> serialNumbers, 
                                                         Double quantity, String operation) {
        try {
            // Validate quantity matches serial number count
            Response quantityValidation = validateSerialNumbersForQuantity(serialNumbers, quantity, itemCode);
            if (!quantityValidation.isSuccess()) {
                return quantityValidation;
            }

            // Additional validation based on operation type
            if ("STOCK_IN".equals(operation)) {
                // For stock in, ensure serial numbers don't already exist
                for (String serialNum : serialNumbers) {
                    if (serialNumberRepository.existsBySerialNumber(serialNum)) {
                        response.setCode(409);
                        response.setSuccess(false);
                        response.setMessage("Serial number already exists: " + serialNum);
                        return response;
                    }
                }
            } else if ("STOCK_OUT".equals(operation)) {
                // For stock out, ensure serial numbers exist and are available
                List<SerialNumber> existingSerials = serialNumberRepository.findBySerialNumberIn(serialNumbers);
                if (existingSerials.size() != serialNumbers.size()) {
                    response.setCode(404);
                    response.setSuccess(false);
                    response.setMessage("Some serial numbers not found");
                    return response;
                }

                for (SerialNumber serial : existingSerials) {
                    if (!"AVAILABLE".equals(serial.getStatus())) {
                        response.setCode(400);
                        response.setSuccess(false);
                        response.setMessage("Serial number not available: " + serial.getSerialNumber());
                        return response;
                    }
                }
            }

            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers validation successful for " + operation);
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error validating serial numbers for stock operation: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to validate serial numbers: " + ex.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response transferSerialNumbers(List<String> serialNumbers, int sourceWarehouse, int targetWarehouse) {
        try {
            List<SerialNumber> serialNumberEntities = serialNumberRepository.findBySerialNumberIn(serialNumbers);
            
            for (SerialNumber serialNumber : serialNumberEntities) {
                if (serialNumber.getWarehouseCode() != sourceWarehouse) {
                    response.setCode(400);
                    response.setSuccess(false);
                    response.setMessage("Serial number not in source warehouse: " + serialNumber.getSerialNumber());
                    return response;
                }
                
                if (!"AVAILABLE".equals(serialNumber.getStatus())) {
                    response.setCode(400);
                    response.setSuccess(false);
                    response.setMessage("Serial number not available for transfer: " + serialNumber.getSerialNumber());
                    return response;
                }

                serialNumber.setWarehouseCode(targetWarehouse);
            }

            serialNumberRepository.saveAll(serialNumberEntities);
            
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Serial numbers transferred successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Error transferring serial numbers: " + ex.getMessage(), ex);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Failed to transfer serial numbers: " + ex.getMessage());
            return response;
        }
    }

    @Override
    public List<SerialNumber> findByCriteria(String itemCode, String status, Integer warehouseCode, 
                                           LocalDateTime startDate, LocalDateTime endDate) {
        try {
            // This is a simplified implementation. In a real scenario, you might want to use 
            // Spring Data JPA Specifications or custom queries for more complex criteria
            List<SerialNumber> results = serialNumberRepository.findByItemCode(itemCode);
            
            return results.stream()
                    .filter(s -> status == null || status.equals(s.getStatus()))
                    .filter(s -> warehouseCode == null || warehouseCode == s.getWarehouseCode())
                    .filter(s -> startDate == null || s.getDateAdded().isAfter(startDate))
                    .filter(s -> endDate == null || s.getDateAdded().isBefore(endDate))
                    .collect(Collectors.toList());
        } catch (Exception ex) {
            LOGGER.error("Error finding serial numbers by criteria: " + ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }
}
