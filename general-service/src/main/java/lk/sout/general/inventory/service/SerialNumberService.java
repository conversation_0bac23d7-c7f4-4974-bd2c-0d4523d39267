package lk.sout.general.inventory.service;

import lk.sout.core.entity.Response;
import lk.sout.general.inventory.entity.SerialNumber;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for SerialNumber management
 */
public interface SerialNumberService {

    /**
     * Save a serial number
     */
    Response save(SerialNumber serialNumber);

    /**
     * Save multiple serial numbers
     */
    Response saveAll(List<SerialNumber> serialNumbers);

    /**
     * Find serial number by ID
     */
    Optional<SerialNumber> findById(String id);

    /**
     * Find serial number by serial number string
     */
    Optional<SerialNumber> findBySerialNumber(String serialNumber);

    /**
     * Check if serial number exists
     */
    boolean existsBySerialNumber(String serialNumber);

    /**
     * Find all serial numbers for an item
     */
    List<SerialNumber> findByItemCode(String itemCode);

    /**
     * Find serial numbers by item code and status
     */
    List<SerialNumber> findByItemCodeAndStatus(String itemCode, String status);

    /**
     * Find available serial numbers for an item in a warehouse
     */
    List<SerialNumber> findAvailableByItemCodeAndWarehouse(String itemCode, int warehouseCode);

    /**
     * Find sold serial numbers for an item
     */
    List<SerialNumber> findSoldByItemCode(String itemCode);

    /**
     * Count available serial numbers for an item in a warehouse
     */
    long countAvailableByItemCodeAndWarehouse(String itemCode, int warehouseCode);

    /**
     * Add serial numbers for purchase invoice
     */
    Response addSerialNumbersForPurchase(String itemCode, List<String> serialNumbers, 
                                       String purchaseInvoiceId, Double purchasePrice, 
                                       int warehouseCode, LocalDateTime warrantyExpiryDate);

    /**
     * Mark serial numbers as sold
     */
    Response markSerialNumbersAsSold(List<String> serialNumbers, String salesInvoiceId,
                                   Double sellingPrice, LocalDateTime dateSold,
                                   String customerId, String customerName, String customerNumber);

    /**
     * Mark serial numbers as returned
     */
    Response markSerialNumbersAsReturned(List<String> serialNumbers, String returnInvoiceId);

    /**
     * Mark serial numbers as damaged
     */
    Response markSerialNumbersAsDamaged(List<String> serialNumbers, String notes);

    /**
     * Validate serial numbers for quantity
     */
    Response validateSerialNumbersForQuantity(List<String> serialNumbers, Double quantity, String itemCode);

    /**
     * Get available stock count for serialized item
     */
    long getAvailableStockCount(String itemCode, int warehouseCode);

    /**
     * Search serial numbers
     */
    List<SerialNumber> searchSerialNumbers(String searchTerm);

    /**
     * Find serial numbers with warranty expiring soon
     */
    List<SerialNumber> findWarrantyExpiringSoon(int daysAhead);

    /**
     * Find serial numbers with expired warranty
     */
    List<SerialNumber> findExpiredWarranty();

    /**
     * Find all serial numbers with pagination
     */
    Page<SerialNumber> findAllPaginated(Pageable pageable);

    /**
     * Find paginated serial numbers by item code
     */
    Page<SerialNumber> findByItemCodePaginated(String itemCode, Pageable pageable);

    /**
     * Find paginated serial numbers by status
     */
    Page<SerialNumber> findByStatusPaginated(String status, Pageable pageable);

    /**
     * Find paginated serial numbers by warehouse
     */
    Page<SerialNumber> findByWarehousePaginated(int warehouseCode, Pageable pageable);

    /**
     * Delete serial number
     */
    Response delete(String id);

    /**
     * Delete serial numbers by item code
     */
    Response deleteByItemCode(String itemCode);

    /**
     * Update serial number status
     */
    Response updateStatus(String serialNumber, String newStatus);

    /**
     * Update serial number warranty
     */
    Response updateWarranty(String serialNumber, LocalDateTime warrantyExpiryDate);

    /**
     * Get serial number statistics for item
     */
    SerialNumberStats getSerialNumberStats(String itemCode);

    /**
     * Validate serial numbers for stock operation
     */
    Response validateSerialNumbersForStockOperation(String itemCode, List<String> serialNumbers, 
                                                  Double quantity, String operation);

    /**
     * Transfer serial numbers between warehouses
     */
    Response transferSerialNumbers(List<String> serialNumbers, int sourceWarehouse, 
                                 int targetWarehouse);

    /**
     * Get serial numbers by multiple criteria
     */
    List<SerialNumber> findByCriteria(String itemCode, String status, Integer warehouseCode, 
                                    LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Inner class for serial number statistics
     */
    class SerialNumberStats {
        private long totalCount;
        private long availableCount;
        private long soldCount;
        private long returnedCount;
        private long damagedCount;

        // Constructors, getters and setters
        public SerialNumberStats() {}

        public SerialNumberStats(long totalCount, long availableCount, long soldCount, 
                               long returnedCount, long damagedCount) {
            this.totalCount = totalCount;
            this.availableCount = availableCount;
            this.soldCount = soldCount;
            this.returnedCount = returnedCount;
            this.damagedCount = damagedCount;
        }

        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }

        public long getAvailableCount() { return availableCount; }
        public void setAvailableCount(long availableCount) { this.availableCount = availableCount; }

        public long getSoldCount() { return soldCount; }
        public void setSoldCount(long soldCount) { this.soldCount = soldCount; }

        public long getReturnedCount() { return returnedCount; }
        public void setReturnedCount(long returnedCount) { this.returnedCount = returnedCount; }

        public long getDamagedCount() { return damagedCount; }
        public void setDamagedCount(long damagedCount) { this.damagedCount = damagedCount; }
    }
}
