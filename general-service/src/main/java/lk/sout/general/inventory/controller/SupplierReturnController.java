package lk.sout.general.inventory.controller;

import lk.sout.general.inventory.entity.SupplierReturn;
import lk.sout.general.inventory.service.SupplierReturnService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@RestController
@RequestMapping("/supplierReturn")
public class SupplierReturnController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierReturnController.class);

    @Autowired
    private SupplierReturnService supplierReturnService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<?> save(@RequestBody SupplierReturn supplierReturn) {
        try {
            return ResponseEntity.ok(supplierReturnService.save(supplierReturn));
        } catch (Exception e) {
            LOGGER.error("Error saving supplier return: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAll(@RequestParam("page") Integer page, @RequestParam("pageSize") Integer pageSize) {
        try {
            return ResponseEntity.ok(supplierReturnService.findAll(
                    PageRequest.of(page, pageSize, Sort.by("returnDate").descending())));
        } catch (Exception e) {
            LOGGER.error("Error finding all supplier returns: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findBySupplier", method = RequestMethod.GET)
    public ResponseEntity<?> findBySupplier(@RequestParam("supplierCode") String supplierCode) {
        try {
            return ResponseEntity.ok(supplierReturnService.findBySupplierCode(supplierCode));
        } catch (Exception e) {
            LOGGER.error("Error finding supplier returns by supplier: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findByItem", method = RequestMethod.GET)
    public ResponseEntity<?> findByItem(@RequestParam("itemCode") String itemCode) {
        try {
            return ResponseEntity.ok(supplierReturnService.findByItemCode(itemCode));
        } catch (Exception e) {
            LOGGER.error("Error finding supplier returns by item: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findByDateRange", method = RequestMethod.GET)
    public ResponseEntity<?> findByDateRange(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);
            return ResponseEntity.ok(supplierReturnService.findByDateRange(startDateTime, endDateTime));
        } catch (Exception e) {
            LOGGER.error("Error finding supplier returns by date range: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findBySupplierAndDateRange", method = RequestMethod.GET)
    public ResponseEntity<?> findBySupplierAndDateRange(
            @RequestParam("supplierCode") String supplierCode,
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);
            return ResponseEntity.ok(supplierReturnService.findBySupplierAndDateRange(supplierCode, startDateTime, endDateTime));
        } catch (Exception e) {
            LOGGER.error("Error finding supplier returns by supplier and date range: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    public ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(supplierReturnService.findById(id));
        } catch (Exception e) {
            LOGGER.error("Error finding supplier return by id: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
