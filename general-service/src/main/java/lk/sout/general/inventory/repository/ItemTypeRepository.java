package lk.sout.general.inventory.repository;


import lk.sout.general.inventory.entity.ItemType;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/18/2018
 */
@Repository
public interface ItemTypeRepository extends MongoRepository<ItemType, String> {

    List<ItemType> findItemTypeByNameLikeIgnoreCaseAndActive(String name, Boolean b);

    ItemType findByName(String param);
}
