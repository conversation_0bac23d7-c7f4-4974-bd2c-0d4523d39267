
package lk.sout.general.inventory.repository;

import lk.sout.general.inventory.entity.StockMovement;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface StockMovementRepository extends MongoRepository<StockMovement, String> {

    List<StockMovement> findByBarcodeAndWhCodeAndDateTimeBetween(String barcode, int whCode, LocalDateTime fromDate,
                                                                 LocalDateTime toDate);
}
