package lk.sout.general.inventory.repository;

import lk.sout.general.inventory.entity.SerialNumber;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for SerialNumber entity
 */
@Repository
public interface SerialNumberRepository extends MongoRepository<SerialNumber, String> {

    /**
     * Find serial number by serial number string
     */
    Optional<SerialNumber> findBySerialNumber(String serialNumber);

    /**
     * Check if serial number exists
     */
    boolean existsBySerialNumber(String serialNumber);

    /**
     * Find all serial numbers for an item
     */
    List<SerialNumber> findByItemCode(String itemCode);

    /**
     * Find serial numbers by item code and status
     */
    List<SerialNumber> findByItemCodeAndStatus(String itemCode, String status);

    /**
     * Find serial numbers by item code, status and warehouse
     */
    List<SerialNumber> findByItemCodeAndStatusAndWarehouseCode(String itemCode, String status, int warehouseCode);

    /**
     * Find available serial numbers for an item in a warehouse
     */
    @Query("{'itemCode': ?0, 'status': 'AVAILABLE', 'warehouseCode': ?1}")
    List<SerialNumber> findAvailableByItemCodeAndWarehouse(String itemCode, int warehouseCode);

    /**
     * Find sold serial numbers for an item
     */
    @Query("{'itemCode': ?0, 'status': 'SOLD'}")
    List<SerialNumber> findSoldByItemCode(String itemCode);

    /**
     * Find serial numbers by status
     */
    List<SerialNumber> findByStatus(String status);

    /**
     * Find serial numbers by warehouse
     */
    List<SerialNumber> findByWarehouseCode(int warehouseCode);

    /**
     * Find serial numbers by purchase invoice
     */
    List<SerialNumber> findByPurchaseInvoiceId(String purchaseInvoiceId);

    /**
     * Find serial numbers by sales invoice
     */
    List<SerialNumber> findBySalesInvoiceId(String salesInvoiceId);

    /**
     * Find serial numbers with warranty expiring soon
     */
    @Query("{'status': 'SOLD', 'warrantyExpiryDate': {'$gte': ?0, '$lte': ?1}}")
    List<SerialNumber> findByWarrantyExpiringBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find serial numbers with expired warranty
     */
    @Query("{'status': 'SOLD', 'warrantyExpiryDate': {'$lt': ?0}}")
    List<SerialNumber> findByWarrantyExpiredBefore(LocalDateTime date);

    /**
     * Count available serial numbers for an item in a warehouse
     */
    @Query(value = "{'itemCode': ?0, 'status': 'AVAILABLE', 'warehouseCode': ?1}", count = true)
    long countAvailableByItemCodeAndWarehouse(String itemCode, int warehouseCode);

    /**
     * Count sold serial numbers for an item
     */
    @Query(value = "{'itemCode': ?0, 'status': 'SOLD'}", count = true)
    long countSoldByItemCode(String itemCode);

    /**
     * Find serial numbers by multiple serial number strings
     */
    List<SerialNumber> findBySerialNumberIn(List<String> serialNumbers);

    /**
     * Find serial numbers containing search term
     */
    @Query("{'serialNumber': {'$regex': ?0, '$options': 'i'}}")
    List<SerialNumber> findBySerialNumberContainingIgnoreCase(String searchTerm);

    /**
     * Find paginated serial numbers by item code
     */
    Page<SerialNumber> findByItemCode(String itemCode, Pageable pageable);

    /**
     * Find paginated serial numbers by status
     */
    Page<SerialNumber> findByStatus(String status, Pageable pageable);

    /**
     * Find paginated serial numbers by warehouse
     */
    Page<SerialNumber> findByWarehouseCode(int warehouseCode, Pageable pageable);

    /**
     * Find serial numbers by item code and date range
     */
    @Query("{'itemCode': ?0, 'dateAdded': {'$gte': ?1, '$lte': ?2}}")
    List<SerialNumber> findByItemCodeAndDateAddedBetween(String itemCode, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find serial numbers by multiple item codes
     */
    List<SerialNumber> findByItemCodeIn(List<String> itemCodes);

    /**
     * Delete serial numbers by item code
     */
    void deleteByItemCode(String itemCode);

    /**
     * Delete serial numbers by purchase invoice
     */
    void deleteByPurchaseInvoiceId(String purchaseInvoiceId);

    /**
     * Find serial numbers for stock calculation
     */
    @Query("{'itemCode': ?0, 'warehouseCode': ?1, 'status': {'$in': ['AVAILABLE', 'SOLD', 'RETURNED']}}")
    List<SerialNumber> findForStockCalculation(String itemCode, int warehouseCode);
}
