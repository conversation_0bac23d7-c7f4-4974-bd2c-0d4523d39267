package lk.sout.general.inventory.service;

import lk.sout.general.inventory.entity.Model;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ModelService {

    boolean save(Model model);

    boolean remove(Model model);

    Iterable<Model> findAll(Integer page, Integer pageSize);

    Model findOne(String id);

    List<Model> findByName(String key);

    String delete(String id);
}
