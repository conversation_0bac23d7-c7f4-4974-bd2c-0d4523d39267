package lk.sout.general.inventory.repository;

import lk.sout.general.inventory.entity.Item;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 4/22/2020
 */
@Repository
public class CustomItemRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomItemRepository.class);

    @Autowired
    MongoTemplate mongoTemplate;

    public List<Item> searchBarcodeLikeForSuggestions(String param) {
        Query query = new Query();
        query.limit(10);
        param = param.replace("(", "\\(");
        param = param.replace(")", "\\)");
        param = param.replace("-", "\\-");
        query.addCriteria(Criteria.where("active").is(true));
        query.addCriteria(Criteria.where("barcode").regex("(?i)\\b" + param + ".*?\\b"));
        query.fields().include("id").include("barcode").include("itemCode").include("itemName")
                .include("itemCategory").include("brand").include("model").include("supplier")
                .include("sellingPrice").include("itemCost").include("deadStockLevel")
                .include("quantity").include("manageStock").include("wholesale").include("retail")
                .include("active").include("retailDiscount").include("manageSerial");
        return mongoTemplate.find(query, Item.class);
    }

    public List<Item> searchBarcodeLikeForSerialManagement(String param) {
        Query query = new Query();
        query.limit(10);
        param = param.replace("(", "\\(");
        param = param.replace(")", "\\)");
        param = param.replace("-", "\\-");
        query.addCriteria(Criteria.where("active").is(true));
        query.addCriteria(Criteria.where("manageSerial").is(true));
        query.addCriteria(Criteria.where("barcode").regex("(?i)\\b" + param + ".*?\\b"));
        query.fields().include("id").include("barcode").include("itemCode").include("itemName")
                .include("itemCategory").include("brand").include("model").include("supplier")
                .include("sellingPrice").include("itemCost").include("deadStockLevel")
                .include("quantity").include("manageStock").include("wholesale").include("retail")
                .include("active").include("retailDiscount").include("manageSerial");
        return mongoTemplate.find(query, Item.class);
    }

    public List<Item> searchItemNameLikeForSuggestions(String param) {
        Query query = new Query();
        query.limit(15);
        param = param.replace("+", "\\+");
        param = param.replace("(", "\\(");
        param = param.replace(")", "\\)");
        query.addCriteria(Criteria.where("active").is(true));
        query.addCriteria(Criteria.where("itemName").regex("(?i)\\b" + param + ".*?\\b"));
        query.fields().include("id").include("barcode").include("itemCode").include("itemName")
                .include("itemCategory").include("brand").include("model").include("supplier")
                .include("sellingPrice").include("itemCost").include("deadStockLevel")
                .include("quantity").include("manageStock").include("wholesale").include("retail")
                .include("active").include("retailDiscount").include("manageSerial");
        return mongoTemplate.find(query, Item.class);
    }

    public List<Item> searchItemNameLikeForSerialManagement(String param) {
        Query query = new Query();
        query.limit(15);
        param = param.replace("+", "\\+");
        param = param.replace("(", "\\(");
        param = param.replace(")", "\\)");
        query.addCriteria(Criteria.where("active").is(true));
        query.addCriteria(Criteria.where("manageSerial").is(true));
        query.addCriteria(Criteria.where("itemName").regex("(?i)\\b" + param + ".*?\\b"));
        query.fields().include("id").include("barcode").include("itemCode").include("itemName")
                .include("itemCategory").include("brand").include("model").include("supplier")
                .include("sellingPrice").include("itemCost").include("deadStockLevel")
                .include("quantity").include("manageStock").include("wholesale").include("retail")
                .include("active").include("retailDiscount").include("manageSerial");
        return mongoTemplate.find(query, Item.class);
    }

    public Page<Item> findAllItemForTable(Pageable pageable) {
        Query query = new Query();
        // Include all fields that might be displayed in the table columns
        query.fields().include("id")
               .include("barcode")
               .include("itemCode")
               .include("itemName")
               .include("itemCategory")
               .include("brand")
               .include("model")
               .include("supplier")
               .include("sellingPrice")
               .include("itemCost")
               .include("deadStockLevel")
               .include("wholesale")
               .include("retail")
               .include("manageStock")
               .include("active");
        long count = mongoTemplate.count(query, Item.class);
        List<Item> filteredItems = mongoTemplate.find(query.with(pageable), Item.class, "item");
        Page<Item> items = PageableExecutionUtils.getPage(
                filteredItems,
                pageable,
                () -> count);
        return items;
    }

    public Page<Item> findAllFiltered(
            Pageable pageable,
            String categoryId,
            String brandId,
            String modelId,
            String supplierId,
            Boolean wholesale,
            Boolean retail,
            Boolean manageStock,
            Boolean active,
            String sortBy,
            String sortDirection,
            String groupBy) {

        Query query = new Query();

        if (categoryId != null && !categoryId.isEmpty()) {
            query.addCriteria(Criteria.where("itemCategory").is(categoryId));
        }

        if (brandId != null && !brandId.isEmpty()) {
            query.addCriteria(Criteria.where("brand").is(brandId));
        }

        if (modelId != null && !modelId.isEmpty()) {
            query.addCriteria(Criteria.where("model").is(modelId));
        }

        if (supplierId != null && !supplierId.isEmpty()) {
            query.addCriteria(Criteria.where("supplier").is(supplierId));
        }

        if (wholesale != null) {
            query.addCriteria(Criteria.where("wholesale").is(wholesale));
        }

        if (retail != null) {
            query.addCriteria(Criteria.where("retail").is(retail));
        }

        if (manageStock != null) {
            query.addCriteria(Criteria.where("manageStock").is(manageStock));
        }

        if (active != null) {
            query.addCriteria(Criteria.where("active").is(active));
        }

        // Include all fields that might be displayed in the table columns
        query.fields().include("id")
               .include("barcode")
               .include("itemCode")
               .include("itemName")
               .include("itemCategory")
               .include("brand")
               .include("model")
               .include("supplier")
               .include("sellingPrice")
               .include("itemCost")
               .include("deadStockLevel")
               .include("wholesale")
               .include("retail")
               .include("manageStock")
               .include("active");

        // Count total items matching the criteria
        long count = mongoTemplate.count(query, Item.class);

        // Apply grouping if specified
        if (groupBy != null && !groupBy.isEmpty()) {
            // For grouping, we need to first fetch all items and then sort them in memory
            // since we can't directly sort by DBRef fields in MongoDB query
            List<Item> allFilteredItems = mongoTemplate.find(query, Item.class, "item");

            // Determine sort direction
            final Sort.Direction direction = (sortDirection != null && sortDirection.equalsIgnoreCase("desc")) ?
                Sort.Direction.DESC : Sort.Direction.ASC;

            // Sort the items based on the groupBy field
            switch (groupBy) {
                case "category":
                    allFilteredItems.sort((item1, item2) -> {
                        String cat1 = item1.getItemCategory() != null ? item1.getItemCategory().getCategoryName() : "";
                        String cat2 = item2.getItemCategory() != null ? item2.getItemCategory().getCategoryName() : "";
                        int catCompare = cat1.compareTo(cat2);
                        if (catCompare != 0) return catCompare;

                        // Secondary sort by the selected sort field
                        if ("itemName".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getItemName().compareTo(item2.getItemName()) :
                                item2.getItemName().compareTo(item1.getItemName());
                        } else if ("itemCode".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getItemCode().compareTo(item2.getItemCode()) :
                                item2.getItemCode().compareTo(item1.getItemCode());
                        } else if ("sellingPrice".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getSellingPrice().compareTo(item2.getSellingPrice()) :
                                item2.getSellingPrice().compareTo(item1.getSellingPrice());
                        }
                        return 0;
                    });
                    break;
                case "brand":
                    allFilteredItems.sort((item1, item2) -> {
                        String brand1 = item1.getBrand() != null ? item1.getBrand().getName() : "";
                        String brand2 = item2.getBrand() != null ? item2.getBrand().getName() : "";
                        int brandCompare = brand1.compareTo(brand2);
                        if (brandCompare != 0) return brandCompare;

                        // Secondary sort by the selected sort field
                        if ("itemName".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getItemName().compareTo(item2.getItemName()) :
                                item2.getItemName().compareTo(item1.getItemName());
                        } else if ("itemCode".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getItemCode().compareTo(item2.getItemCode()) :
                                item2.getItemCode().compareTo(item1.getItemCode());
                        } else if ("sellingPrice".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getSellingPrice().compareTo(item2.getSellingPrice()) :
                                item2.getSellingPrice().compareTo(item1.getSellingPrice());
                        }
                        return 0;
                    });
                    break;
                case "model":
                    allFilteredItems.sort((item1, item2) -> {
                        String model1 = item1.getModel() != null ? item1.getModel().getName() : "";
                        String model2 = item2.getModel() != null ? item2.getModel().getName() : "";
                        int modelCompare = model1.compareTo(model2);
                        if (modelCompare != 0) return modelCompare;

                        // Secondary sort by the selected sort field
                        if ("itemName".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getItemName().compareTo(item2.getItemName()) :
                                item2.getItemName().compareTo(item1.getItemName());
                        } else if ("itemCode".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getItemCode().compareTo(item2.getItemCode()) :
                                item2.getItemCode().compareTo(item1.getItemCode());
                        } else if ("sellingPrice".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getSellingPrice().compareTo(item2.getSellingPrice()) :
                                item2.getSellingPrice().compareTo(item1.getSellingPrice());
                        }
                        return 0;
                    });
                    break;
                case "supplier":
                    allFilteredItems.sort((item1, item2) -> {
                        String supplier1 = item1.getSupplier() != null ? item1.getSupplier().getName() : "";
                        String supplier2 = item2.getSupplier() != null ? item2.getSupplier().getName() : "";
                        int supplierCompare = supplier1.compareTo(supplier2);
                        if (supplierCompare != 0) return supplierCompare;

                        // Secondary sort by the selected sort field
                        if ("itemName".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getItemName().compareTo(item2.getItemName()) :
                                item2.getItemName().compareTo(item1.getItemName());
                        } else if ("itemCode".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getItemCode().compareTo(item2.getItemCode()) :
                                item2.getItemCode().compareTo(item1.getItemCode());
                        } else if ("sellingPrice".equals(sortBy)) {
                            return direction.equals(Sort.Direction.ASC) ?
                                item1.getSellingPrice().compareTo(item2.getSellingPrice()) :
                                item2.getSellingPrice().compareTo(item1.getSellingPrice());
                        }
                        return 0;
                    });
                    break;
                default:
                    // Apply regular sorting if groupBy is not recognized
                    Sort.Direction defaultDirection = (sortDirection != null && sortDirection.equalsIgnoreCase("desc")) ?
                        Sort.Direction.DESC : Sort.Direction.ASC;
                    query.with(Sort.by(defaultDirection, sortBy));
                    List<Item> filteredItems = mongoTemplate.find(query.with(pageable), Item.class, "item");
                    return PageableExecutionUtils.getPage(
                            filteredItems,
                            pageable,
                            () -> count);
            }

            // Apply pagination manually after sorting
            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), allFilteredItems.size());

            // Check if start index is valid
            if (start <= allFilteredItems.size()) {
                List<Item> pageItems = allFilteredItems.subList(start, end);
                return new PageImpl<>(pageItems, pageable, allFilteredItems.size());
            } else {
                return new PageImpl<>(new ArrayList<>(), pageable, allFilteredItems.size());
            }
        } else {
            // Apply regular sorting if no groupBy is specified
            Sort.Direction defaultDirection = (sortDirection != null && sortDirection.equalsIgnoreCase("desc")) ?
                Sort.Direction.DESC : Sort.Direction.ASC;
            query.with(Sort.by(defaultDirection, sortBy));
            List<Item> filteredItems = mongoTemplate.find(query.with(pageable), Item.class, "item");
            return PageableExecutionUtils.getPage(
                    filteredItems,
                    pageable,
                    () -> count);
        }
    }
}
