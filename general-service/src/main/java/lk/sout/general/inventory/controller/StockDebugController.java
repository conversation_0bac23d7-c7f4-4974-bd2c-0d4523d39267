package lk.sout.general.inventory.controller;

import lk.sout.general.inventory.entity.Stock;
import lk.sout.general.inventory.repository.StockRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Debug controller to troubleshoot Stock repository queries
 */
@RestController
@RequestMapping("/stock-debug")
public class StockDebugController {

    @Autowired
    private StockRepository stockRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * Debug the findAllByItemCodeAndWarehouseCodeOrderBySellingPriceAsc method
     */
    @GetMapping("/debug-query")
    public ResponseEntity<Map<String, Object>> debugQuery(
            @RequestParam("itemCode") String itemCode,
            @RequestParam("warehouseCode") int warehouseCode) {
        
        Map<String, Object> debug = new HashMap<>();
        
        try {
            // 1. Test the actual method
            List<Stock> result = stockRepository.findAllByItemCodeAndWarehouseCodeOrderBySellingPriceAsc(itemCode, warehouseCode);
            debug.put("methodResult", result);
            debug.put("methodResultCount", result.size());
            
            // 2. Check total stock count
            long totalStocks = stockRepository.count();
            debug.put("totalStockCount", totalStocks);
            
            // 3. Check if itemCode exists at all
            List<Stock> byItemCode = stockRepository.findByItemCode(itemCode);
            debug.put("stocksWithItemCode", byItemCode.size());
            debug.put("stocksWithItemCodeDetails", byItemCode.stream()
                .map(s -> Map.of(
                    "id", s.getId(),
                    "itemCode", s.getItemCode(),
                    "warehouseCode", s.getWarehouseCode(),
                    "sellingPrice", s.getSellingPrice(),
                    "quantity", s.getQuantity()
                )).collect(Collectors.toList()));
            
            // 4. Check if warehouseCode exists
            Query warehouseQuery = new Query(Criteria.where("warehouseCode").is(warehouseCode));
            List<Stock> byWarehouse = mongoTemplate.find(warehouseQuery, Stock.class);
            debug.put("stocksWithWarehouseCode", byWarehouse.size());
            
            // 5. Manual query to see what's happening
            Query manualQuery = new Query();
            manualQuery.addCriteria(Criteria.where("itemCode").is(itemCode));
            manualQuery.addCriteria(Criteria.where("warehouseCode").is(warehouseCode));
            List<Stock> manualResult = mongoTemplate.find(manualQuery, Stock.class);
            debug.put("manualQueryResult", manualResult.size());
            debug.put("manualQueryDetails", manualResult.stream()
                .map(s -> Map.of(
                    "id", s.getId(),
                    "itemCode", s.getItemCode(),
                    "warehouseCode", s.getWarehouseCode(),
                    "sellingPrice", s.getSellingPrice(),
                    "quantity", s.getQuantity()
                )).collect(Collectors.toList()));
            
            // 6. Check for case sensitivity issues
            Query caseInsensitiveQuery = new Query();
            caseInsensitiveQuery.addCriteria(Criteria.where("itemCode").regex("^" + itemCode + "$", "i"));
            caseInsensitiveQuery.addCriteria(Criteria.where("warehouseCode").is(warehouseCode));
            List<Stock> caseInsensitiveResult = mongoTemplate.find(caseInsensitiveQuery, Stock.class);
            debug.put("caseInsensitiveResult", caseInsensitiveResult.size());
            
            // 7. Get sample data for reference
            List<Stock> sampleStocks = mongoTemplate.find(Query.query(Criteria.where("id").exists(true)).limit(5), Stock.class);
            debug.put("sampleStocks", sampleStocks.stream()
                .map(s -> Map.of(
                    "id", s.getId(),
                    "itemCode", s.getItemCode(),
                    "warehouseCode", s.getWarehouseCode(),
                    "sellingPrice", s.getSellingPrice(),
                    "quantity", s.getQuantity()
                )).collect(Collectors.toList()));
            
            debug.put("searchParameters", Map.of(
                "itemCode", itemCode,
                "warehouseCode", warehouseCode,
                "itemCodeLength", itemCode.length(),
                "itemCodeType", itemCode.getClass().getSimpleName()
            ));
            
        } catch (Exception e) {
            debug.put("error", e.getMessage());
            debug.put("errorType", e.getClass().getSimpleName());
        }
        
        return ResponseEntity.ok(debug);
    }

    /**
     * Get all unique item codes and warehouse codes for reference
     */
    @GetMapping("/reference-data")
    public ResponseEntity<Map<String, Object>> getReferenceData() {
        Map<String, Object> data = new HashMap<>();
        
        try {
            // Get all unique item codes
            List<String> itemCodes = mongoTemplate.findDistinct("itemCode", Stock.class, String.class);
            data.put("uniqueItemCodes", itemCodes.stream().limit(20).collect(Collectors.toList()));
            data.put("totalUniqueItemCodes", itemCodes.size());
            
            // Get all unique warehouse codes
            List<Integer> warehouseCodes = mongoTemplate.findDistinct("warehouseCode", Stock.class, Integer.class);
            data.put("uniqueWarehouseCodes", warehouseCodes);
            
            // Get total count
            data.put("totalStockRecords", stockRepository.count());
            
        } catch (Exception e) {
            data.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(data);
    }

    /**
     * Test with sample data
     */
    @GetMapping("/test-with-sample")
    public ResponseEntity<Map<String, Object>> testWithSample() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Get first stock record
            List<Stock> sampleStocks = mongoTemplate.find(Query.query(Criteria.where("id").exists(true)).limit(1), Stock.class);
            
            if (!sampleStocks.isEmpty()) {
                Stock sample = sampleStocks.get(0);
                String sampleItemCode = sample.getItemCode();
                int sampleWarehouseCode = sample.getWarehouseCode();
                
                result.put("sampleData", Map.of(
                    "itemCode", sampleItemCode,
                    "warehouseCode", sampleWarehouseCode,
                    "sellingPrice", sample.getSellingPrice()
                ));
                
                // Test the method with this sample data
                List<Stock> testResult = stockRepository.findAllByItemCodeAndWarehouseCodeOrderBySellingPriceAsc(sampleItemCode, sampleWarehouseCode);
                result.put("testResult", testResult.size());
                result.put("testResultDetails", testResult.stream()
                    .map(s -> Map.of(
                        "id", s.getId(),
                        "sellingPrice", s.getSellingPrice(),
                        "quantity", s.getQuantity()
                    )).collect(Collectors.toList()));
                
            } else {
                result.put("message", "No stock records found in database");
            }
            
        } catch (Exception e) {
            result.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
}
