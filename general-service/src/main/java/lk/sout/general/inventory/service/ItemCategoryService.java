package lk.sout.general.inventory.service;

import lk.sout.general.inventory.entity.ItemCategory;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ItemCategoryService {

    boolean save(ItemCategory itemCategory);

    Iterable<ItemCategory> findAll(Integer page, Integer pageSize);

    boolean remove(String id);

    List<ItemCategory> findByName(String name);

    int getCount();

    List<ItemCategory> findAllCategories();

    ItemCategory findById(String id);
}
