package lk.sout.general.inventory.service;

import lk.sout.general.inventory.entity.ItemType;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ItemTypeService {

    boolean save(ItemType itemType);

    boolean remove(String id);

    Iterable<ItemType> findAll(Pageable pageable);

    Iterable<ItemType> findAll();

    ItemType findOne(String id);

    List<ItemType> findByName(String name);
}
