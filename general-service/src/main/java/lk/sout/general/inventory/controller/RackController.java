package lk.sout.general.inventory.controller;

import lk.sout.general.inventory.entity.Rack;
import lk.sout.general.inventory.service.RackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/rack")
public class RackController {

    @Autowired
    RackService rackService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Rack rack) {
        try {
            return ResponseEntity.ok(rackService.save(rack));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(rackService.findAll(Integer.parseInt(page), Integer.parseInt(pageSize)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllRacks", method = RequestMethod.GET)
    public List<Rack> findAllRacks() {
        return rackService.findAllRacks();
    }

    @RequestMapping(value = "/searchByID", method = RequestMethod.GET)
    private ResponseEntity<?> searchByID(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(rackService.findOne(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> serach(@RequestParam("rackNo") String rackNo) {
        try {
            return ResponseEntity.ok(rackService.findByRackNo(rackNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
