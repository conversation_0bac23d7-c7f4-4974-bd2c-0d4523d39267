package lk.sout.general.inventory.entity;

import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Serial Number Management Entity for Inventory Items
 * Supports tracking of individual serial numbers for items like phones, electronics
 */
@Document
@Component
public class SerialNumber {

    @Id
    private String id;

    @Indexed(unique = true)
    private String serialNumber;

    @Indexed
    private String itemCode;

    @Indexed
    private String status; // AVAILABLE, SOLD, RETURNED, DAMAGED

    private LocalDateTime dateAdded;

    private LocalDateTime dateSold;

    private LocalDateTime warrantyExpiryDate;

    private String purchaseInvoiceId;

    private String salesInvoiceId;

    private String returnInvoiceId;

    private Double purchasePrice;

    private Double sellingPrice;

    private String notes;

    private int warehouseCode;

    // Customer information for sold items
    private String customerId;

    private String customerName;

    private String customerNumber;

    @CreatedDate
    private LocalDateTime createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    // Constructors
    public SerialNumber() {}

    public SerialNumber(String serialNumber, String itemCode, String status, int warehouseCode) {
        this.serialNumber = serialNumber;
        this.itemCode = itemCode;
        this.status = status;
        this.warehouseCode = warehouseCode;
        this.dateAdded = LocalDateTime.now();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getDateAdded() {
        return dateAdded;
    }

    public void setDateAdded(LocalDateTime dateAdded) {
        this.dateAdded = dateAdded;
    }

    public LocalDateTime getDateSold() {
        return dateSold;
    }

    public void setDateSold(LocalDateTime dateSold) {
        this.dateSold = dateSold;
    }

    public LocalDateTime getWarrantyExpiryDate() {
        return warrantyExpiryDate;
    }

    public void setWarrantyExpiryDate(LocalDateTime warrantyExpiryDate) {
        this.warrantyExpiryDate = warrantyExpiryDate;
    }

    public String getPurchaseInvoiceId() {
        return purchaseInvoiceId;
    }

    public void setPurchaseInvoiceId(String purchaseInvoiceId) {
        this.purchaseInvoiceId = purchaseInvoiceId;
    }

    public String getSalesInvoiceId() {
        return salesInvoiceId;
    }

    public void setSalesInvoiceId(String salesInvoiceId) {
        this.salesInvoiceId = salesInvoiceId;
    }

    public String getReturnInvoiceId() {
        return returnInvoiceId;
    }

    public void setReturnInvoiceId(String returnInvoiceId) {
        this.returnInvoiceId = returnInvoiceId;
    }

    public Double getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(Double purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public Double getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(Double sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public int getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(int warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(String customerNumber) {
        this.customerNumber = customerNumber;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    // Utility methods
    public boolean isAvailable() {
        return "AVAILABLE".equals(this.status);
    }

    public boolean isSold() {
        return "SOLD".equals(this.status);
    }

    public boolean isReturned() {
        return "RETURNED".equals(this.status);
    }

    public boolean isDamaged() {
        return "DAMAGED".equals(this.status);
    }

    public boolean isWarrantyValid() {
        return warrantyExpiryDate != null && warrantyExpiryDate.isAfter(LocalDateTime.now());
    }

    @Override
    public String toString() {
        return "SerialNumber{" +
                "id='" + id + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", status='" + status + '\'' +
                ", dateAdded=" + dateAdded +
                ", warehouseCode=" + warehouseCode +
                '}';
    }
}
