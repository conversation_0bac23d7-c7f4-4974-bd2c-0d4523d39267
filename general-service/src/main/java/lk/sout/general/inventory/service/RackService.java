package lk.sout.general.inventory.service;

import lk.sout.general.inventory.entity.Rack;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface RackService {

    boolean save(Rack rack);

    boolean remove(Rack rack);

    Iterable<Rack> findAll(Integer page, Integer pageSize);

    List<Rack> findAllRacks();

    Rack findOne(String id);

    List<Rack> findByRackNo(String key);

}
