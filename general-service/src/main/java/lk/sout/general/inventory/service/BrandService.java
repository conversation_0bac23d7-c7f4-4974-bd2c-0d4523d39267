package lk.sout.general.inventory.service;

import lk.sout.general.inventory.entity.Brand;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface BrandService {

    boolean save(Brand brand);

    boolean remove(Brand brand);

    Iterable<Brand> findAll(Integer page, Integer pageNo);

    Brand findOne(String id);

    List<Brand> findByName(String key);

    String delete(String id);
}
