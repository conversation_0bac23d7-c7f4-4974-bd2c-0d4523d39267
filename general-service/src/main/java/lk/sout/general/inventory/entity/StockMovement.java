package lk.sout.general.inventory.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Document
@Component
public class StockMovement {

    @Id
    private String id;

    private String type;

    private String itemCode;

    private int whCode;

    private Double quantity;

    private Double stockCountBefore;

    private Double stockCountAfter;

    private String barcode;

    private String itemName;

    private LocalDateTime dateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void itemCode(String refNo) {
        this.itemCode = refNo;
    }

    public int getWhCode() {
        return whCode;
    }

    public void setWhCode(int whCode) {
        this.whCode = whCode;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public LocalDateTime getDateTime() {
        return dateTime;
    }

    public void setDateTime(LocalDateTime dateTime) {
        this.dateTime = dateTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getStockCountBefore() {
        return stockCountBefore;
    }

    public void setStockCountBefore(Double stockCountBefore) {
        this.stockCountBefore = stockCountBefore;
    }

    public Double getStockCountAfter() {
        return stockCountAfter;
    }

    public void setStockCountAfter(Double stockCountAfter) {
        this.stockCountAfter = stockCountAfter;
    }
}
