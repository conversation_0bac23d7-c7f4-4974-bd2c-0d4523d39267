package lk.sout.general.dashboard.service;

import lk.sout.core.entity.DailyStatistics;
import lk.sout.core.service.StatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for dashboard-specific statistics operations
 * Created by Madhawa Weerasinghe on 7/17/2023
 */

@Service
public class DailyStatService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DailyStatService.class);

    @Autowired
    private StatisticsService statisticsService;

    /**
     * Get dashboard summary for today
     * @return Map containing dashboard summary data
     */
    public Map<String, Object> getTodayDashboardSummary() {
        try {
            DailyStatistics todayStats = statisticsService.findByDate(LocalDate.now());
            if (todayStats == null) {
                // Generate statistics if not found
                todayStats = statisticsService.generateDailyStatistics();
            }

            Map<String, Object> summary = new HashMap<>();

            // Sales summary
            Map<String, Object> salesSummary = new HashMap<>();
            salesSummary.put("dailySales", todayStats.getDailySales());
            salesSummary.put("dailyInvoiceCount", todayStats.getDailyInvoiceCount());
            salesSummary.put("dailyProfit", todayStats.getDailyProfit());
            salesSummary.put("averageInvoiceValue", todayStats.getAverageInvoiceValue());
            summary.put("sales", salesSummary);

            // Stock summary
            Map<String, Object> stockSummary = new HashMap<>();
            stockSummary.put("totalStockValue", todayStats.getTotalStockValue());
            stockSummary.put("totalStockCost", todayStats.getTotalStockCost());
            stockSummary.put("totalStockItems", todayStats.getTotalStockItems());
            stockSummary.put("totalStockQuantity", todayStats.getTotalStockQuantity());
            stockSummary.put("estimatedProfit", todayStats.getEstimatedProfit());
            summary.put("stock", stockSummary);

            // Pending bills summary
            Map<String, Object> pendingBillsSummary = new HashMap<>();
            pendingBillsSummary.put("totalPendingBills", todayStats.getTotalPendingBills());
            pendingBillsSummary.put("pendingBillCount", todayStats.getPendingBillCount());
            pendingBillsSummary.put("oldestPendingBillAge", todayStats.getOldestPendingBillAge());
            summary.put("pendingBills", pendingBillsSummary);

            // Cheque summary
            Map<String, Object> chequeSummary = new HashMap<>();
            chequeSummary.put("totalPendingCheques", todayStats.getTotalPendingCheques());
            chequeSummary.put("pendingChequeCount", todayStats.getPendingChequeCount());
            chequeSummary.put("totalDepositedCheques", todayStats.getTotalDepositedCheques());
            chequeSummary.put("depositedChequeCount", todayStats.getDepositedChequeCount());
            summary.put("cheques", chequeSummary);

            // Cash flow summary
            Map<String, Object> cashFlowSummary = new HashMap<>();
            cashFlowSummary.put("totalIncome", todayStats.getTotalIncome());
            cashFlowSummary.put("totalExpense", todayStats.getTotalExpense());
            cashFlowSummary.put("netCashFlow", todayStats.getNetCashFlow());
            summary.put("cashFlow", cashFlowSummary);

            // Additional stats
            summary.put("additionalStats", todayStats.getAdditionalStats());
            summary.put("date", todayStats.getDate());

            return summary;
        } catch (Exception e) {
            LOGGER.error("Error getting today's dashboard summary: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * Get dashboard summary for a specific date
     * @param date The date to get summary for
     * @return Map containing dashboard summary data
     */
    public Map<String, Object> getDashboardSummaryForDate(LocalDate date) {
        try {
            DailyStatistics stats = statisticsService.findByDate(date);
            if (stats == null) {
                // Generate statistics if not found
                stats = statisticsService.generateStatisticsForDate(date);
            }

            return createDashboardSummary(stats);
        } catch (Exception e) {
            LOGGER.error("Error getting dashboard summary for date {}: {}", date, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * Get dashboard summary for a date range
     * @param startDate Start date
     * @param endDate End date
     * @return Map containing aggregated dashboard summary data
     */
    public Map<String, Object> getDashboardSummaryForDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            List<DailyStatistics> statsList = statisticsService.findByDateRange(startDate, endDate);

            if (statsList == null || statsList.isEmpty()) {
                return new HashMap<>();
            }

            // Aggregate statistics
            Map<String, Object> summary = new HashMap<>();

            double totalSales = statsList.stream().mapToDouble(DailyStatistics::getDailySales).sum();
            int totalInvoices = statsList.stream().mapToInt(DailyStatistics::getDailyInvoiceCount).sum();
            double totalProfit = statsList.stream().mapToDouble(DailyStatistics::getDailyProfit).sum();
            double totalIncome = statsList.stream().mapToDouble(DailyStatistics::getTotalIncome).sum();
            double totalExpense = statsList.stream().mapToDouble(DailyStatistics::getTotalExpense).sum();

            Map<String, Object> salesSummary = new HashMap<>();
            salesSummary.put("totalSales", totalSales);
            salesSummary.put("totalInvoices", totalInvoices);
            salesSummary.put("totalProfit", totalProfit);
            salesSummary.put("averageInvoiceValue", totalInvoices > 0 ? totalSales / totalInvoices : 0);
            summary.put("sales", salesSummary);

            Map<String, Object> cashFlowSummary = new HashMap<>();
            cashFlowSummary.put("totalIncome", totalIncome);
            cashFlowSummary.put("totalExpense", totalExpense);
            cashFlowSummary.put("netCashFlow", totalIncome - totalExpense);
            summary.put("cashFlow", cashFlowSummary);

            summary.put("startDate", startDate);
            summary.put("endDate", endDate);
            summary.put("recordCount", statsList.size());

            return summary;
        } catch (Exception e) {
            LOGGER.error("Error getting dashboard summary for date range {} to {}: {}", startDate, endDate, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * Helper method to create dashboard summary from DailyStatistics
     */
    private Map<String, Object> createDashboardSummary(DailyStatistics stats) {
        Map<String, Object> summary = new HashMap<>();

        // Sales summary
        Map<String, Object> salesSummary = new HashMap<>();
        salesSummary.put("dailySales", stats.getDailySales());
        salesSummary.put("dailyInvoiceCount", stats.getDailyInvoiceCount());
        salesSummary.put("dailyProfit", stats.getDailyProfit());
        salesSummary.put("averageInvoiceValue", stats.getAverageInvoiceValue());
        summary.put("sales", salesSummary);

        // Stock summary
        Map<String, Object> stockSummary = new HashMap<>();
        stockSummary.put("totalStockValue", stats.getTotalStockValue());
        stockSummary.put("totalStockCost", stats.getTotalStockCost());
        stockSummary.put("totalStockItems", stats.getTotalStockItems());
        stockSummary.put("totalStockQuantity", stats.getTotalStockQuantity());
        stockSummary.put("estimatedProfit", stats.getEstimatedProfit());
        summary.put("stock", stockSummary);

        // Pending bills summary
        Map<String, Object> pendingBillsSummary = new HashMap<>();
        pendingBillsSummary.put("totalPendingBills", stats.getTotalPendingBills());
        pendingBillsSummary.put("pendingBillCount", stats.getPendingBillCount());
        pendingBillsSummary.put("oldestPendingBillAge", stats.getOldestPendingBillAge());
        summary.put("pendingBills", pendingBillsSummary);

        // Cheque summary
        Map<String, Object> chequeSummary = new HashMap<>();
        chequeSummary.put("totalPendingCheques", stats.getTotalPendingCheques());
        chequeSummary.put("pendingChequeCount", stats.getPendingChequeCount());
        chequeSummary.put("totalDepositedCheques", stats.getTotalDepositedCheques());
        chequeSummary.put("depositedChequeCount", stats.getDepositedChequeCount());
        summary.put("cheques", chequeSummary);

        // Cash flow summary
        Map<String, Object> cashFlowSummary = new HashMap<>();
        cashFlowSummary.put("totalIncome", stats.getTotalIncome());
        cashFlowSummary.put("totalExpense", stats.getTotalExpense());
        cashFlowSummary.put("netCashFlow", stats.getNetCashFlow());
        summary.put("cashFlow", cashFlowSummary);

        // Additional stats
        summary.put("additionalStats", stats.getAdditionalStats());
        summary.put("date", stats.getDate());

        return summary;
    }
}
