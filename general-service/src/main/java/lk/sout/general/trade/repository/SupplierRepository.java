package lk.sout.general.trade.repository;

import lk.sout.general.trade.entity.Supplier;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 4/25/2018
 */
@Repository
public interface SupplierRepository extends MongoRepository<Supplier, String> {

    Supplier findByRegNoLikeAndNameNot(String code, String name);

    Supplier findByRegNo(String code);

    Supplier findByRegNoIgnoreCase(String supplierCode);

    List<Supplier> findAllByNameNotAndActiveOrderByIdDesc(String default_customer, boolean active);

    List<Supplier> findByNameLikeIgnoreCaseAndRegNoNot(String name, String supplierCode);

    Supplier findByName(String supplier);

}
