package lk.sout.general.trade.controller;

import lk.sout.general.trade.entity.ItemSaleSummaryAggr;
import lk.sout.general.trade.service.SalesInvoiceRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/salesInvoiceRecord")
public class SalesInvoiceRecordController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesInvoiceRecordController.class);

    @Autowired
    SalesInvoiceRecordService salesInvoiceRecordService;
    /**
     * Find profit by range filter with option to filter by payment status
     * @param rangeId Range ID from metadata
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    @RequestMapping(value = "/findProfitByRangeFilter", method = RequestMethod.GET)
    private ResponseEntity<?> findByRangeFilter(
            @RequestParam("rangeId") String rangeId,
            @RequestParam(value = "unrealized", defaultValue = "false") boolean unrealized) {
        try {
            LOGGER.info("Finding {} profit for range ID: {}", unrealized ? "unrealized" : "realized", rangeId);
            List<ItemSaleSummaryAggr> results = salesInvoiceRecordService.findByRangeFilter(rangeId, unrealized);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error finding {} profit for range ID {}: {}", unrealized ? "unrealized" : "realized", rangeId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find profit between dates with option to filter by payment status
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    @RequestMapping(value = "/findProfitBetween", method = RequestMethod.GET)
    private ResponseEntity<?> findProfitBetween(
            @RequestParam("sDate") String sDate,
            @RequestParam("eDate") String eDate,
            @RequestParam(value = "unrealized", defaultValue = "false") boolean unrealized) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            LocalDate startDate = LocalDate.parse(sDate, formatter);
            LocalDate endDate = LocalDate.parse(eDate, formatter);

            LOGGER.info("Finding {} profit between {} and {}", unrealized ? "unrealized" : "realized", startDate, endDate);
            List<ItemSaleSummaryAggr> results = salesInvoiceRecordService.findProfitBetween(startDate, endDate, unrealized);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error finding {} profit between dates: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales grouped by date between dates with option to filter by payment status
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    @RequestMapping(value = "/findSalesGroupByDateBetween", method = RequestMethod.GET)
    private ResponseEntity<?> findSalesGroupByDateBetween(
            @RequestParam("sDate") String sDate,
            @RequestParam("eDate") String eDate,
            @RequestParam(value = "unrealized", defaultValue = "false") boolean unrealized) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            LocalDate startDate = LocalDate.parse(sDate, formatter);
            LocalDate endDate = LocalDate.parse(eDate, formatter);

            LOGGER.info("Finding {} sales between {} and {}", unrealized ? "unrealized" : "realized", startDate, endDate);
            List<ItemSaleSummaryAggr> results = salesInvoiceRecordService.findSalesGroupByDateBetween(startDate, endDate, unrealized);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales between dates: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find unrealized profit between dates with option to filter by payment status
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return Total profit amount
     */
    @RequestMapping(value = "/findUnrealizedProfitBetween", method = RequestMethod.GET)
    private ResponseEntity<?> findUnrealizedProfitBetween(@RequestParam("sDate") String sDate,
                                                          @RequestParam("eDate") String eDate,
                                                          @RequestParam(value = "unrealized", defaultValue = "true") boolean unrealized) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            Double profit = salesInvoiceRecordService.findUnrealizedProfitBetween(
                    LocalDate.parse(sDate, formatter),
                    LocalDate.parse(eDate, formatter),
                    unrealized);
            return ResponseEntity.ok(profit);
        } catch (Exception e) {
            LOGGER.error("Error finding {} profit: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(value = "/findSalesItemGroupByDate", method = RequestMethod.GET)
    private ResponseEntity<?> findSalesItemGroupByDate(@RequestParam("itemId") String itemId, @RequestParam("sDate") String sDate,
                                                       @RequestParam("eDate") String eDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(salesInvoiceRecordService.findSalesItemGroupByDate(itemId, LocalDate.parse(sDate, formatter),
                    LocalDate.parse(eDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    /**
     * Find sales records by cash drawer (cashier) with option to filter by payment status
     * @param drawerNo Cash drawer number
     * @param sDate Start date (optional)
     * @param eDate End date (optional)
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    @RequestMapping(value = "/findSalesByCashier", method = RequestMethod.GET)
    private ResponseEntity<?> findSalesByCashier(
            @RequestParam("drawerNo") String drawerNo,
            @RequestParam(value = "sDate", required = false) String sDate,
            @RequestParam(value = "eDate", required = false) String eDate,
            @RequestParam(value = "unrealized", defaultValue = "false") boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by cash drawer: {}", unrealized ? "unrealized" : "realized", drawerNo);

            // Parse dates if provided
            LocalDate startDate = null;
            LocalDate endDate = null;

            if (sDate != null && eDate != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
                startDate = LocalDate.parse(sDate, formatter);
                endDate = LocalDate.parse(eDate, formatter);
                LOGGER.info("Date range: {} to {}", startDate, endDate);
            }

            // Call service method
            List<ItemSaleSummaryAggr> results;
            if (startDate != null && endDate != null) {
                results = salesInvoiceRecordService.findSalesByCashier(drawerNo, startDate, endDate, unrealized);
            } else {
                results = salesInvoiceRecordService.findSalesByCashier(drawerNo, unrealized);
            }

            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by cash drawer: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales records by user with option to filter by payment status
     * @param username User's username
     * @param sDate Start date (optional)
     * @param eDate End date (optional)
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    @RequestMapping(value = "/findSalesByUser", method = RequestMethod.GET)
    private ResponseEntity<?> findSalesByUser(
            @RequestParam("username") String username,
            @RequestParam(value = "sDate", required = false) String sDate,
            @RequestParam(value = "eDate", required = false) String eDate,
            @RequestParam(value = "unrealized", defaultValue = "false") boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by user: {}", unrealized ? "unrealized" : "realized", username);

            // Parse dates if provided
            LocalDate startDate = null;
            LocalDate endDate = null;

            if (sDate != null && eDate != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
                startDate = LocalDate.parse(sDate, formatter);
                endDate = LocalDate.parse(eDate, formatter);
                LOGGER.info("Date range: {} to {}", startDate, endDate);
            }

            // Call service method
            List<ItemSaleSummaryAggr> results;
            if (startDate != null && endDate != null) {
                results = salesInvoiceRecordService.findSalesByUser(username, startDate, endDate, unrealized);
            } else {
                results = salesInvoiceRecordService.findSalesByUser(username, unrealized);
            }

            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by user: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales records by route with option to filter by payment status
     * @param routeNo Route number
     * @param sDate Start date (optional)
     * @param eDate End date (optional)
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    @RequestMapping(value = "/findSalesByRoute", method = RequestMethod.GET)
    private ResponseEntity<?> findSalesByRoute(
            @RequestParam("routeNo") String routeNo,
            @RequestParam(value = "sDate", required = false) String sDate,
            @RequestParam(value = "eDate", required = false) String eDate,
            @RequestParam(value = "unrealized", defaultValue = "false") boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by route: {}", unrealized ? "unrealized" : "realized", routeNo);

            // Parse dates if provided
            LocalDate startDate = null;
            LocalDate endDate = null;

            if (sDate != null && eDate != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
                startDate = LocalDate.parse(sDate, formatter);
                endDate = LocalDate.parse(eDate, formatter);
                LOGGER.info("Date range: {} to {}", startDate, endDate);
            }

            // Call service method
            List<ItemSaleSummaryAggr> results;
            if (startDate != null && endDate != null) {
                results = salesInvoiceRecordService.findSalesByRoute(routeNo, startDate, endDate, unrealized);
            } else {
                results = salesInvoiceRecordService.findSalesByRoute(routeNo, unrealized);
            }

            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by route: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales records by user and route
     * @param username User's username
     * @param routeNo Route number
     * @param sDate Start date (optional)
     * @param eDate End date (optional)
     * @return List of ItemSaleSummaryAggr
     */
    @RequestMapping(value = "/findSalesByUserAndRoute", method = RequestMethod.GET)
    private ResponseEntity<?> findSalesByUserAndRoute(
            @RequestParam("username") String username,
            @RequestParam("routeNo") String routeNo,
            @RequestParam(value = "sDate", required = false) String sDate,
            @RequestParam(value = "eDate", required = false) String eDate) {
        try {
            LOGGER.info("Finding sales by user: {} and route: {}", username, routeNo);

            // Parse dates if provided
            LocalDate startDate = null;
            LocalDate endDate = null;

            if (sDate != null && eDate != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
                startDate = LocalDate.parse(sDate, formatter);
                endDate = LocalDate.parse(eDate, formatter);
                LOGGER.info("Date range: {} to {}", startDate, endDate);
            }

            // Call service method
            List<ItemSaleSummaryAggr> results;
            if (startDate != null && endDate != null) {
                results = salesInvoiceRecordService.findSalesByUserAndRoute(username, routeNo, startDate, endDate);
            } else {
                results = salesInvoiceRecordService.findSalesByUserAndRoute(username, routeNo);
            }

            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error finding sales by user and route: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
