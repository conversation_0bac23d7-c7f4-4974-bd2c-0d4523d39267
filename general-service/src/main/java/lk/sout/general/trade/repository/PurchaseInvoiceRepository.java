
package lk.sout.general.trade.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.general.trade.entity.PurchaseInvoice;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface PurchaseInvoiceRepository extends MongoRepository<PurchaseInvoice, Object> {
    PurchaseInvoice findByDate(Date date);

    List<PurchaseInvoice> findBySupplierNameLikeIgnoreCase(String name);

    List<PurchaseInvoice> findAllByOrderByIdDesc();

    List<PurchaseInvoice> findAllBySupplierOrderByIdDesc(String supplier);

    List<PurchaseInvoice> findAllByStatusNot(MetaData metaData);

    List<PurchaseInvoice> findBySupplierRegNoLikeIgnoreCase(String supplierCode);

    PurchaseInvoice findByInvoiceNo(String invoiceNo);

    List<PurchaseInvoice> findBySupplier(String id);

    List<PurchaseInvoice> findAllByDate(LocalDateTime date);

    List<PurchaseInvoice> findAllByDateBetween(LocalDateTime sDate, LocalDateTime eDate);

    PurchaseInvoice findByPurchaseInvoiceNo(String id);

    List<PurchaseInvoice> findAllByStatusIdOrderByPurchaseInvoiceNoDesc(String statusId);
}
