package lk.sout.general.trade.entity;

import lk.sout.general.inventory.entity.Item;
import lk.sout.general.inventory.entity.Stock;

public class SalesInvoiceRecordAggr {

    private Item item;

    private Stock stock;

    private String soldQty;

    private Double amount;

    public Item getItem() {
        return item;
    }

    public void setItem(Item item) {
        this.item = item;
    }

    public String getItemName() {
        return item.getItemName();
    }

    public Double getQuantity() {
        return stock.getQuantity();
    }

    public Double getUnitPrice() {
        return item.getSellingPrice();
    }

    public String getSoldQty() {
        return soldQty;
    }

    public void setSoldQty(String soldQty) {
        this.soldQty = soldQty;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

}
