package lk.sout.general.trade.controller;

import lk.sout.general.trade.entity.Cheque;
import lk.sout.general.trade.service.ChequeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/cheque")
public class ChequeController {

    @Autowired
    ChequeService chequeService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Cheque cheque){
        try {
            return ResponseEntity.ok(chequeService.save(cheque));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/updateCheque", method = RequestMethod.GET)
    private ResponseEntity<?> updateCheque(@RequestParam("id") String id, @RequestParam("comment") String comment,
                                           @RequestParam("isDeposit") boolean isDeposit){
        try {
            return ResponseEntity.ok(chequeService.updateCheque(id, comment, isDeposit));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPending", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize){
        try {
            return ResponseEntity.ok(chequeService.findAllPending(
                    PageRequest.of(Integer.parseInt(page),Integer.parseInt(pageSize))));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByStatus", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByStatus(@RequestParam String chequeStatusId) {
        try {
            return ResponseEntity.ok(chequeService.findAllByStatus(chequeStatusId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByBank", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByBank(@RequestParam String bankId){
        try {
            return ResponseEntity.ok(chequeService.findAllByBank(bankId));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByCustomer", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByCustomer(@RequestParam String customerId){
        try {
            return ResponseEntity.ok(chequeService.findAllByCustomer(customerId));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllBySupplier", method = RequestMethod.GET)
    private ResponseEntity<?> findAllBySupplier(@RequestParam String supplierId){
        try {
            return ResponseEntity.ok(chequeService.findAllBySupplier(supplierId));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByChequeType", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByChequeType(@RequestParam String chequeType){
        try {
            return ResponseEntity.ok(chequeService.findAllByChequeType(chequeType));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByPurchaseInvoiceNo", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByPurchaseInvoiceNo(@RequestParam String purchaseInvoiceNo){
        try {
            return ResponseEntity.ok(chequeService.findAllByPurchaseInvoiceNo(purchaseInvoiceNo));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/loadAvailableChequeQty", method = RequestMethod.GET)
    private ResponseEntity<?> loadAvailableChequeQty(){
        try {
            return ResponseEntity.ok(chequeService.loadAvailableChequeQty());
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
