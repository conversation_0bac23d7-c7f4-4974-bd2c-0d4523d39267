package lk.sout.general.trade.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.service.SequenceService;
import lk.sout.general.trade.entity.Route;
import lk.sout.general.trade.repository.RouteRepository;
import lk.sout.general.trade.service.RouteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Implementation of RouteService
 */
@Service
public class RouteServiceImpl implements RouteService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RouteServiceImpl.class);

    @Autowired
    private RouteRepository routeRepository;

    @Autowired
    private SequenceService sequenceService;

    @Override
    public Response save(Route route) {
        Response response = new Response();
        try {

            // Check if route with same name already exists
            Route existingRoute = routeRepository.findByName(route.getName());
            if (existingRoute != null) {
                response.setSuccess(false);
                response.setMessage("Route with name " + route.getName() + " already exists");
                return response;
            }

            // Check if route with same route number already exists (if provided)
            if (route.getRouteNo() != null && !route.getRouteNo().isEmpty()) {
                Route routeWithSameNumber = routeRepository.findByRouteNo(route.getRouteNo());
                if (routeWithSameNumber != null) {
                    response.setSuccess(false);
                    response.setMessage("Route with number " + route.getRouteNo() + " already exists");
                    return response;
                }
            }

            // Set active by default
            route.setActive(true);

            // Generate route number for new routes
            if (route.getId() == null) {
                // Always generate a route number for new routes
                if (route.getRouteNo() == null || route.getRouteNo().isEmpty()) {
                    // Generate route number
                    Sequence sequence = sequenceService.findSequenceByName("RouteNo");
                    if (sequence != null) {
                        String routeNo = sequence.getPrefix() + String.format("%04d", sequence.getCounter() + 1);
                        route.setRouteNo(routeNo);

                        // Increment the sequence counter
                        sequence.setCounter(sequence.getCounter() + 1);
                        sequenceService.save(sequence);
                        LOGGER.info("Generated route number: " + routeNo);
                    } else {
                        LOGGER.error("RouteNo sequence not found");
                        response.setSuccess(false);
                        response.setMessage("Failed to generate route number: sequence not found");
                        return response;
                    }
                }
            }

            // Save the route
            routeRepository.save(route);

            response.setSuccess(true);
            response.setMessage("Route saved successfully");
        } catch (Exception e) {
            LOGGER.error("Error saving route: {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("Error saving route: " + e.getMessage());
        }
        return response;
    }

    @Override
    public Response update(Route route) {
        Response response = new Response();
        try {
            // Check if route exists
            Optional<Route> optionalRoute = routeRepository.findById(route.getId());
            if (!optionalRoute.isPresent()) {
                response.setSuccess(false);
                response.setMessage("Route not found with ID: " + route.getId());
                return response;
            }

            Route existingRoute = optionalRoute.get();



            // Check if route name is being changed and if new name already exists
            if (!existingRoute.getName().equals(route.getName())) {
                Route routeWithSameName = routeRepository.findByName(route.getName());
                if (routeWithSameName != null && !routeWithSameName.getId().equals(route.getId())) {
                    response.setSuccess(false);
                    response.setMessage("Route with name " + route.getName() + " already exists");
                    return response;
                }
            }

            // Preserve the active status if not provided
            if (existingRoute.isActive() != route.isActive()) {
                existingRoute.setActive(route.isActive());
            }

            // Update fields
            existingRoute.setName(route.getName());
            existingRoute.setFrom(route.getFrom());
            existingRoute.setTo(route.getTo());

            // Preserve the route number if it exists, otherwise generate one
            if (existingRoute.getRouteNo() == null || existingRoute.getRouteNo().isEmpty()) {
                Sequence sequence = sequenceService.findSequenceByName("RouteNo");
                if (sequence != null) {
                    String routeNo = sequence.getPrefix() + String.format("%04d", sequence.getCounter() + 1);
                    existingRoute.setRouteNo(routeNo);

                    // Increment the sequence counter
                    sequence.setCounter(sequence.getCounter() + 1);
                    sequenceService.save(sequence);
                    LOGGER.info("Generated route number for existing route: " + routeNo);
                } else {
                    LOGGER.error("RouteNo sequence not found during update");
                    response.setSuccess(false);
                    response.setMessage("Failed to generate route number: sequence not found");
                    return response;
                }
            } else {
                // If route number is provided in the update, use it
                if (route.getRouteNo() != null && !route.getRouteNo().isEmpty() &&
                    !route.getRouteNo().equals(existingRoute.getRouteNo())) {
                    // Check if the new route number is already in use
                    Route routeWithSameNumber = routeRepository.findByRouteNo(route.getRouteNo());
                    if (routeWithSameNumber != null && !routeWithSameNumber.getId().equals(existingRoute.getId())) {
                        response.setSuccess(false);
                        response.setMessage("Route number " + route.getRouteNo() + " is already in use");
                        return response;
                    }
                    existingRoute.setRouteNo(route.getRouteNo());
                }
            }

            // Save the updated route
            routeRepository.save(existingRoute);

            response.setSuccess(true);
            response.setMessage("Route updated successfully");
        } catch (Exception e) {
            LOGGER.error("Error updating route: {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("Error updating route: " + e.getMessage());
        }
        return response;
    }

    @Override
    public Route findById(String id) {
        try {
            Optional<Route> optionalRoute = routeRepository.findById(id);
            return optionalRoute.orElse(null);
        } catch (Exception e) {
            LOGGER.error("Error finding route by ID: {}", e.getMessage(), e);
            return null;
        }
    }



    @Override
    public Route findByName(String name) {
        try {
            return routeRepository.findByName(name);
        } catch (Exception e) {
            LOGGER.error("Error finding route by name: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<Route> findAllActive() {
        try {
            return routeRepository.findByActiveTrue();
        } catch (Exception e) {
            LOGGER.error("Error finding active routes: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Page<Route> findAll(Pageable pageable) {
        try {
            return routeRepository.findAll(pageable);
        } catch (Exception e) {
            LOGGER.error("Error finding all routes: {}", e.getMessage(), e);
            return null;
        }
    }



    @Override
    public Page<Route> searchByName(String name, Pageable pageable) {
        try {
            return routeRepository.findByNameContaining(name, pageable);
        } catch (Exception e) {
            LOGGER.error("Error searching routes by name: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<Route> searchByNameWithoutPagination(String name) {
        try {
            return routeRepository.findByNameContainingOrderByNameAsc(name);
        } catch (Exception e) {
            LOGGER.error("Error searching routes by name without pagination: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Response delete(String id) {
        Response response = new Response();
        try {
            // Check if route exists
            Optional<Route> optionalRoute = routeRepository.findById(id);
            if (!optionalRoute.isPresent()) {
                response.setSuccess(false);
                response.setMessage("Route not found with ID: " + id);
                return response;
            }

            // Delete the route
            routeRepository.deleteById(id);

            response.setSuccess(true);
            response.setMessage("Route deleted successfully");
        } catch (Exception e) {
            LOGGER.error("Error deleting route: {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("Error deleting route: " + e.getMessage());
        }
        return response;
    }

    @Override
    public Response setActive(String id, boolean active) {
        Response response = new Response();
        try {
            // Check if route exists
            Optional<Route> optionalRoute = routeRepository.findById(id);
            if (!optionalRoute.isPresent()) {
                response.setSuccess(false);
                response.setMessage("Route not found with ID: " + id);
                return response;
            }

            Route route = optionalRoute.get();
            route.setActive(active);

            // Save the updated route
            routeRepository.save(route);

            response.setSuccess(true);
            response.setMessage("Route " + (active ? "activated" : "deactivated") + " successfully");
        } catch (Exception e) {
            LOGGER.error("Error setting route active status: {}", e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("Error setting route active status: " + e.getMessage());
        }
        return response;
    }
}
