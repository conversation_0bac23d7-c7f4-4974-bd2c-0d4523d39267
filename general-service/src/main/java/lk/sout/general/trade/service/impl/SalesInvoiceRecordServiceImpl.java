package lk.sout.general.trade.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Transaction;
import lk.sout.core.service.MetaDataService;
import lk.sout.general.inventory.entity.Item;
import lk.sout.general.inventory.service.ItemService;
import lk.sout.general.trade.entity.ItemSaleSummaryAggr;
import lk.sout.general.trade.entity.SalesInvoiceRecord;
import lk.sout.general.trade.repository.SalesInvoiceRecordRepository;
import lk.sout.general.trade.repository.SalesRecordRepositoryTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class SalesInvoiceRecordServiceImpl implements lk.sout.general.trade.service.SalesInvoiceRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesInvoiceRecordServiceImpl.class);

    @Autowired
    SalesInvoiceRecordRepository salesInvoiceRecordRepository;

    @Autowired
    SalesRecordRepositoryTemplate salesRecordRepositoryTemplate;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ItemService itemService;

    /**
     * Find profit by range filter with option to filter by payment status
     * @param rangeId Range ID from metadata
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    public List<ItemSaleSummaryAggr> findByRangeFilter(String rangeId, boolean unrealized) {
        try {
            MetaData range = metaDataService.findById(rangeId);
            LocalDate sDate = LocalDate.now();
            LocalDate eDate = LocalDate.now();
            LocalDate today = LocalDate.now();

            if (range.getValue().equals("Today")) {
                sDate = today;
                eDate = today.plusDays(1);
            }
            if (range.getValue().equals("This Week")) {
                sDate = today.with((DayOfWeek.MONDAY));
                eDate = today.with((DayOfWeek.SUNDAY));
            }
            if (range.getValue().equals("This Month")) {
                sDate = today.withDayOfMonth(1);
                eDate = today.withDayOfMonth(today.lengthOfMonth());
            }
            if (range.getValue().equals("This Year")) {
                sDate = today.withDayOfYear(1);
                eDate = today.withDayOfYear(today.lengthOfYear());
            }

            LOGGER.info("Finding {} profit for range {}: {} to {}", unrealized ? "unrealized" : "realized", range.getValue(), sDate, eDate);
            return salesRecordRepositoryTemplate.findSalesGroupByDateBetween(sDate, eDate, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} profit for range {}: {}", unrealized ? "unrealized" : "realized", rangeId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find profit between dates with option to filter by payment status
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    public List<ItemSaleSummaryAggr> findProfitBetween(LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} profit between {} and {}", unrealized ? "unrealized" : "realized", sDate, eDate);
            return salesRecordRepositoryTemplate.findSalesGroupByDateBetween(sDate, eDate, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} profit between dates: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales grouped by date between dates with option to filter by payment status
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    public List<ItemSaleSummaryAggr> findSalesGroupByDateBetween(LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales between {} and {}", unrealized ? "unrealized" : "realized", sDate, eDate);
            return salesRecordRepositoryTemplate.findSalesGroupByDateBetween(sDate, eDate, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales between dates: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Double findUnrealizedProfitBetween(LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} profit between {} and {}", unrealized ? "unrealized" : "realized", sDate, eDate);
            return salesRecordRepositoryTemplate.findUnrealizedProfitBetween(sDate, eDate, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} profit: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return 0.0;
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesItemGroupByDate(String itemId, LocalDate sDate, LocalDate eDate) {
        try {
            Item item = itemService.findOneByItemCode(itemId);
            return salesRecordRepositoryTemplate.findSalesItemGroupByDateAndItem(item, sDate, eDate);
        } catch (Exception e) {
            LOGGER.error("Error finding sales item group by date: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesByCashier(String drawerNo, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by cash drawer: {}", unrealized ? "unrealized" : "realized", drawerNo);
            return salesRecordRepositoryTemplate.findSalesByCashier(drawerNo, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by cash drawer: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesByCashier(String drawerNo, LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by cash drawer: {} between {} and {}", unrealized ? "unrealized" : "realized", drawerNo, sDate, eDate);
            return salesRecordRepositoryTemplate.findSalesByCashierAndDateRange(drawerNo, sDate, eDate, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by cash drawer and date range: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesByUser(String username, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by user: {}", unrealized ? "unrealized" : "realized", username);
            return salesRecordRepositoryTemplate.findSalesByUser(username, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by user: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesByUser(String username, LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by user: {} between {} and {}", unrealized ? "unrealized" : "realized", username, sDate, eDate);
            return salesRecordRepositoryTemplate.findSalesByUserAndDateRange(username, sDate, eDate, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by user and date range: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesByRoute(String routeNo, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by route: {}", unrealized ? "unrealized" : "realized", routeNo);
            return salesRecordRepositoryTemplate.findSalesByRoute(routeNo, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by route: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesByRoute(String routeNo, LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by route: {} between {} and {}", unrealized ? "unrealized" : "realized", routeNo, sDate, eDate);
            return salesRecordRepositoryTemplate.findSalesByRouteAndDateRange(routeNo, sDate, eDate, unrealized);
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by route and date range: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesByUserAndRoute(String username, String routeNo) {
        try {
            LOGGER.info("Finding sales by user: {} and route: {}", username, routeNo);
            return salesRecordRepositoryTemplate.findSalesByUserAndRoute(username, routeNo);
        } catch (Exception e) {
            LOGGER.error("Error finding sales by user and route: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemSaleSummaryAggr> findSalesByUserAndRoute(String username, String routeNo, LocalDate sDate, LocalDate eDate) {
        try {
            LOGGER.info("Finding sales by user: {} and route: {} between {} and {}", username, routeNo, sDate, eDate);
            return salesRecordRepositoryTemplate.findSalesByUserAndRouteAndDateRange(username, routeNo, sDate, eDate);
        } catch (Exception e) {
            LOGGER.error("Error finding sales by user, route, and date range: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
