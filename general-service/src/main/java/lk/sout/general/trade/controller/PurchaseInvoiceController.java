package lk.sout.general.trade.controller;

import lk.sout.general.trade.entity.PurchaseInvoice;
import lk.sout.general.trade.service.PurchaseInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Formatter;

@RestController
@RequestMapping("/purchaseInvoice")
public class PurchaseInvoiceController {

    @Autowired
    PurchaseInvoiceService purchaseInvoiceService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody PurchaseInvoice purchaseInvoice) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.save(purchaseInvoice));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPages", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPages(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.
                    findAll(Integer.parseInt(page), Integer.parseInt(pageSize)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchBySupplier", method = RequestMethod.GET)
    private ResponseEntity<?> searchBySupplier(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.findBySupplierId(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "findAllByDate", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByDate(@RequestParam("date") String date){
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(purchaseInvoiceService.findAllByDate(LocalDate.parse(date, formatter)));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByInvoiceNo", method = RequestMethod.GET)
    private ResponseEntity<?> searchByInvoiceId(@RequestParam("invoiceNo") String invoiceNo) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.searchByInvoiceNo(invoiceNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/payBalance", method = RequestMethod.GET)
    private ResponseEntity<?> payBalance(@RequestParam String purchaseInvoiceNo, @RequestParam Double amount){
        try {
            return ResponseEntity.ok(purchaseInvoiceService.payBalance(purchaseInvoiceNo, amount));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByStatusId", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByStatusId(@RequestParam String statusId){
        try {
            return ResponseEntity.ok(purchaseInvoiceService.findAllByStatusId(statusId));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
