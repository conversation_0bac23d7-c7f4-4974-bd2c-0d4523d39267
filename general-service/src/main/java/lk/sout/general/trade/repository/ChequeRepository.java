package lk.sout.general.trade.repository;

import lk.sout.general.trade.entity.Cheque;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ChequeRepository extends MongoRepository<Cheque, String> {

    List<Cheque> findTopByOrderByIdDesc();

    List<Cheque> findAllByOrderByIdDesc();

    Cheque findAllByChequeNoLike(String chequeNo);

    List<Cheque> findAllByBankId(String bankId);

    List<Cheque> findAllByStatusId(String chequeStatusId);

    Page<Cheque> findAllByStatusId(String statusId, Pageable pageable);

    List<Cheque> findAllByCustomerId(String customerId);

    List<Cheque> findAllBySupplierId(String supplierId);

    List<Cheque> findAllByChequeType(String chequeType);

    List<Cheque> findAllByPurchaseInvoiceNo(String purchaseInvoiceNo);

    Cheque findChequeById(String id);

    List<Cheque> findAllByChequeDateBetweenAndStatus(LocalDateTime today, LocalDate tomorrow, String statusId);

    List<Cheque> findAllByChequeDate(LocalDate date);


}
