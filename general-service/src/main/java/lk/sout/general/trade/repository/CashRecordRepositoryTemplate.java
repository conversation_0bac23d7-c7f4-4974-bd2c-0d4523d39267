package lk.sout.general.trade.repository;

import lk.sout.general.trade.entity.CashRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 3/9/2020
 */

@Repository
public class CashRecordRepositoryTemplate {

    @Autowired
    MongoTemplate mongoTemplate;

    public Double calculateTotalCashOut(LocalDate date, String counter) {
        try {
            Aggregation agg = Aggregation.newAggregation(
                    Aggregation.match(new Criteria()
                            .and("counter").is(counter)
                            .and("date").gt(date.atStartOfDay()).lte(date.plusDays(1).atStartOfDay())),
                    Aggregation.group().sum("amount").as("total")
            );
            AggregationResults<CashRecord> result = mongoTemplate.aggregate(agg, "result", CashRecord.class);
            return (0 != result.getMappedResults().size() ? result.getMappedResults().get(0).getAmount() : 0.0);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

}
