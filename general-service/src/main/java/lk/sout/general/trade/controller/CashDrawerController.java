package lk.sout.general.trade.controller;

import lk.sout.general.trade.entity.CashDrawer;
import lk.sout.general.trade.service.CashDrawerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/cashDrawer")
public class CashDrawerController {

    @Autowired
    CashDrawerService cashDrawerService;

    @RequestMapping(value = "/findByDrawerNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByDrawerNo(@RequestParam String drawerNo) {
        try {
            return ResponseEntity.ok(cashDrawerService.findByDrawerNo(drawerNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/checkDrawerNoStatus", method = RequestMethod.GET)
    private ResponseEntity<?> checkDrawerNoStatus(@RequestParam String drawerNo){
        try {
            return ResponseEntity.ok(cashDrawerService.checkDrawerNoStatus(drawerNo));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    /**
     * Find all Cash Drawers
     * @return List of Cash Drawers
     */
    @RequestMapping(value = "/findAllCashDrawers", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllCashDrawers() {
        try {
            List<CashDrawer> cashDrawers = cashDrawerService.findAll();
            return ResponseEntity.ok(cashDrawers);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
