package lk.sout.general.trade.controller;

import lk.sout.core.entity.Response;
import lk.sout.general.trade.entity.Route;
import lk.sout.general.trade.service.RouteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing routes
 */
@RestController
@RequestMapping("/route")
public class RouteController {

    @Autowired
    private RouteService routeService;

    /**
     * Save a new route
     * @param route The route to save
     * @return Response with success status and saved route
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseEntity<Response> save(@RequestBody Route route) {
        try {
            Response response = routeService.save(route);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error saving route: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Update an existing route
     * @param route The route to update
     * @return Response with success status and updated route
     */
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public ResponseEntity<Response> update(@RequestBody Route route) {
        try {
            Response response = routeService.update(route);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error updating route: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Find a route by ID
     * @param id The route ID
     * @return The route with the given ID
     */
    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    public ResponseEntity<?> findById(@RequestParam String id) {
        try {
            Route route = routeService.findById(id);
            if (route == null) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(route);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error finding route: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Find routes by name for typeahead
     * @param name The search string to match against route names
     * @return List of routes with names containing the given text (no pagination)
     */
    @RequestMapping(value = "/findByName", method = RequestMethod.GET)
    public ResponseEntity<?> findByName(@RequestParam String name) {
        try {
            // Use the new method that returns a list directly without pagination
            List<Route> routes = routeService.searchByNameWithoutPagination(name);
            return ResponseEntity.ok(routes);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error finding routes by name: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }


    /**
     * Find all active routes
     * @return List of active routes
     */
    @RequestMapping(value = "/findAllActive", method = RequestMethod.GET)
    public ResponseEntity<?> findAllActive() {
        try {
            List<Route> routes = routeService.findAllActive();
            return ResponseEntity.ok(routes);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error finding active routes: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Find all routes with pagination
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of routes
     */
    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAll(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "name"));
            Page<Route> routes = routeService.findAll(pageable);
            return ResponseEntity.ok(routes);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error finding routes: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }



    /**
     * Search routes by name with pagination
     * @param name The name to search for
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of routes with names containing the given text
     */
    @RequestMapping(value = "/searchByName", method = RequestMethod.GET)
    public ResponseEntity<?> searchByName(
            @RequestParam String name,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "name"));
            Page<Route> routes = routeService.searchByName(name, pageable);
            return ResponseEntity.ok(routes);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error searching routes: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Delete a route
     * @param id The route ID
     * @return Response with success status
     */
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public ResponseEntity<Response> delete(@RequestParam String id) {
        try {
            Response response = routeService.delete(id);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error deleting route: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Activate or deactivate a route
     * @param id The route ID
     * @param active Whether to activate or deactivate the route
     * @return Response with success status
     */
    @RequestMapping(value = "/setActive", method = RequestMethod.PUT)
    public ResponseEntity<Response> setActive(
            @RequestParam String id,
            @RequestParam boolean active) {
        try {
            Response response = routeService.setActive(id, active);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Response response = new Response();
            response.setSuccess(false);
            response.setMessage("Error setting route active status: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
