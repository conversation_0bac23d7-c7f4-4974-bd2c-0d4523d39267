package lk.sout.general.trade.service;

import lk.sout.general.trade.entity.CashDrawer;

import java.util.List;

public interface CashDrawerService {

    boolean save(CashDrawer cashDrawer);

    boolean topUpCashDrawer(Double topUpAmount, String counterId);

    boolean deductFromCashDrawer(Double amount, String counterId);

    boolean correctCashDrawer(Double amount, String counterId);

    List<CashDrawer> findAll();

    CashDrawer findByDrawerNo(String counter);

    boolean checkDrawerNoStatus(String counter);

}
