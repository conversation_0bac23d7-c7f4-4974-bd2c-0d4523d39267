package lk.sout.general.trade.repository;

import lk.sout.general.trade.entity.Route;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for Route entity
 */
@Repository
public interface RouteRepository extends MongoRepository<Route, String> {



    /**
     * Find route by name
     * @param name The route name
     * @return Route with the given name or null if not found
     */
    Route findByName(String name);

    /**
     * Find route by route number
     * @param routeNo The route number
     * @return Route with the given route number or null if not found
     */
    Route findByRouteNo(String routeNo);

    /**
     * Find all active routes
     * @return List of active routes
     */
    List<Route> findByActiveTrue();

    /**
     * Find all routes with pagination
     * @param pageable Pagination information
     * @return Page of routes
     */
    Page<Route> findAll(Pageable pageable);



    /**
     * Search routes by name containing the given text with pagination
     * @param name The name to search for
     * @param pageable Pagination information
     * @return Page of routes with names containing the given text
     */
    Page<Route> findByNameContaining(String name, Pageable pageable);

    /**
     * Search routes by name containing the given text without pagination
     * @param name The name to search for
     * @return List of routes with names containing the given text
     */
    List<Route> findByNameContainingOrderByNameAsc(String name);
}
