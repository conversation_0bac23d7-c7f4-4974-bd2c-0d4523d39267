package lk.sout.general.trade.service;

import lk.sout.general.trade.entity.PurchaseInvoice;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 2/4/2020
 */
public interface PurchaseInvoiceService {

    Response save(PurchaseInvoice purchaseInvoice);

    PurchaseInvoice basicSave(PurchaseInvoice purchaseInvoice);

    Iterable<PurchaseInvoice> findAll(Integer page, Integer pageSize);

    List<PurchaseInvoice> findAllBySupplierOrderByIdDesc(String supplier);

    List<PurchaseInvoice> findAllByMetaDataNotCompleted(MetaData metaData);

    PurchaseInvoice searchByInvoiceNo(String invoiceNo);

    List<PurchaseInvoice> findBySupplierId(String id);

    List<PurchaseInvoice> findAllByDate(LocalDate date);

    PurchaseInvoice findById(String id);

    PurchaseInvoice findByPurchaseInvoiceNo(String invoiceNo);

    Response payBalance(String purchaseInvoiceNo, Double amount);

    List<PurchaseInvoice> findAllByStatusId(String statusId);
}
