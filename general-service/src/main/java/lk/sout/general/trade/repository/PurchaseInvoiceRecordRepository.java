package lk.sout.general.trade.repository;

import lk.sout.general.trade.entity.PurchaseInvoiceRecord;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PurchaseInvoiceRecordRepository extends MongoRepository<PurchaseInvoiceRecord, String> {

    List<PurchaseInvoiceRecord> findAllByItemCodeOrderByDateDesc(String code);

    List<PurchaseInvoiceRecord> findAllByItemCodeAndDateBetween(String code, LocalDateTime sDate, LocalDateTime eDate);

    List<PurchaseInvoiceRecord> findAllByItemCode(String code);
}
