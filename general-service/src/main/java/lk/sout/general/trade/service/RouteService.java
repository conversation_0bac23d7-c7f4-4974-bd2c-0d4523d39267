package lk.sout.general.trade.service;

import lk.sout.core.entity.Response;
import lk.sout.general.trade.entity.Route;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Service interface for managing routes
 */
public interface RouteService {

    /**
     * Save a route
     * @param route The route to save
     * @return Response with success status and saved route
     */
    Response save(Route route);

    /**
     * Update a route
     * @param route The route to update
     * @return Response with success status and updated route
     */
    Response update(Route route);

    /**
     * Find a route by ID
     * @param id The route ID
     * @return The route with the given ID or null if not found
     */
    Route findById(String id);



    /**
     * Find a route by name
     * @param name The route name
     * @return The route with the given name or null if not found
     */
    Route findByName(String name);

    /**
     * Find all active routes
     * @return List of active routes
     */
    List<Route> findAllActive();

    /**
     * Find all routes with pagination
     * @param pageable Pagination information
     * @return Page of routes
     */
    Page<Route> findAll(Pageable pageable);



    /**
     * Search routes by name with pagination
     * @param name The name to search for
     * @param pageable Pagination information
     * @return Page of routes with names containing the given text
     */
    Page<Route> searchByName(String name, Pageable pageable);

    /**
     * Search routes by name without pagination
     * @param name The name to search for
     * @return List of routes with names containing the given text
     */
    List<Route> searchByNameWithoutPagination(String name);

    /**
     * Delete a route
     * @param id The route ID
     * @return Response with success status
     */
    Response delete(String id);

    /**
     * Activate or deactivate a route
     * @param id The route ID
     * @param active Whether to activate or deactivate the route
     * @return Response with success status
     */
    Response setActive(String id, boolean active);
}
