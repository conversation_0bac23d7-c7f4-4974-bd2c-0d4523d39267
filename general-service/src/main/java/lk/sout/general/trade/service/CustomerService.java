package lk.sout.general.trade.service;

import lk.sout.core.entity.Response;
import lk.sout.general.trade.entity.Customer;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
public interface CustomerService {

    /**
     * Save or update a customer
     * @param customer The customer to save or update
     * @return Response containing the result of the operation
     */
    Response save(Customer customer);

    Iterable<Customer> findAll(Pageable pageable);

    List<Customer> findAllActive(boolean result);

    Customer findByNicBr(String nicBr);

    List<Customer> findByNicLike(String nic);

    List<Customer> findByTpLike(String tp);

    Customer findById(String id);

    Customer findDefaultCustomer();

    List<Customer> findAllByNameLikeIgnoreCaseAndActive(String itemCode, Boolean active);

    boolean checkNic(String nic);

    /**
     * Find a customer by telephone number
     * @param telephone The telephone number to search for
     * @return The customer with the given telephone number, or null if not found
     */
    Customer findByTelephone(String telephone);

    /**
     * Find a customer by customer number
     * @param customerNo The customer number to search for
     * @return The customer with the given customer number, or null if not found
     */
    Customer findByCustomerNo(String customerNo);
}
