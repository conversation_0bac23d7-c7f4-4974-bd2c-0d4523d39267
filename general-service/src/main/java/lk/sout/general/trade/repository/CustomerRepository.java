package lk.sout.general.trade.repository;

import lk.sout.general.trade.entity.Customer;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 12/15/2019
 */
@Repository
public interface CustomerRepository extends MongoRepository<Customer, String> {

    Customer findByName(String defaultCustomer);

    List<Customer> findAllByActive(boolean active);

    List<Customer> findAllByNameLikeIgnoreCase(String name);

    Customer findByNicBr(String nic);

    List<Customer> findByNicBrLike(String nic);

    List<Customer> findByTelephone1Like(String tp);

    Customer findByTelephone1(String tp);

    /**
     * Find a customer by customer number
     * @param customerNo The customer number to search for
     * @return The customer with the given customer number, or null if not found
     */
    Customer findByCustomerNo(String customerNo);
}
