package lk.sout.general.trade.controller;

import lk.sout.general.trade.service.CashRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/cashRecord")
public class CashRecordController {

    @Autowired
    CashRecordService cashRecordService;

    @RequestMapping(value = "/dayStart", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> dayStart(@RequestParam Double addingAmount) {
        try {
            return ResponseEntity.ok(cashRecordService.dayStart(addingAmount));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/addCash", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> addCash(@RequestParam Double addingAmount) {
        try {
            return ResponseEntity.ok(cashRecordService.addCash(addingAmount));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/withdrawCash", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> withdraw(@RequestParam Double withdrawingAmount, @RequestParam String purpose) {
        try {
            return ResponseEntity.ok(cashRecordService.withdraw(withdrawingAmount, purpose));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDrawerNoAndDates", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByDrawerNoAndDates(@RequestParam String drawerNo, @RequestParam String sDate, @RequestParam String eDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(cashRecordService.findByCounterAndDateBetween(drawerNo, LocalDate.parse(sDate, formatter), LocalDate.parse(eDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByTypeAndDates", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByTypeAndDates(@RequestParam String typeId, @RequestParam String sDate, @RequestParam String eDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(cashRecordService.findByTypeAndDateBetween(typeId, LocalDate.parse(sDate, formatter), LocalDate.parse(eDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDrawerNoAndTypeAndDates", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByDrawerNoAndTypeAndDates(@RequestParam String drawerNo, @RequestParam String typeId, @RequestParam String sDate, @RequestParam String eDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(cashRecordService.findByCounterAndTypeAndDateBetween(drawerNo, typeId, LocalDate.parse(sDate, formatter), LocalDate.parse(eDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDates", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByTypeAndDates(@RequestParam String sDate, @RequestParam String eDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(cashRecordService.findRecordsBetween(LocalDate.parse(sDate, formatter), LocalDate.parse(eDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


}
