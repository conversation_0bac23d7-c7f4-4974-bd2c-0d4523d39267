package lk.sout.general.trade.service;

import lk.sout.general.trade.entity.Cheque;
import lk.sout.core.entity.Response;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 1/6/2020
 */
public interface ChequeService {

    Cheque basicSave(Cheque cheque);

    Response save(Cheque cheque);

    Response updateCheque(String id, String comment, boolean isDeposit);

    void update(String salesInvoiceId, String refType);

    List<Cheque> findAll();

    Cheque findById(String id);

    Cheque findAllByChequeNo(String chequeNo);

    List<Cheque> findAllByDate(LocalDate date);

    Iterable<Cheque> findAllPending(Pageable pageable);

    List<Cheque> findAllByStatus(String chequeStatusId);

    List<Cheque> findAllByBank(String bankId);

    List<Cheque> findAllByCustomer(String customerId);

    List<Cheque> findAllBySupplier(String supplierId);

    List<Cheque> findAllByChequeType(String chequeType);

    List<Cheque> findAllByPurchaseInvoiceNo(String purchaseInvoiceNo);

    boolean handleReceivedCheque(Cheque cheque);

    boolean handleGivenCheque(Cheque cheque);

    boolean checkPaymentUpdate(Cheque cheque);

    Integer loadAvailableChequeQty();
}
