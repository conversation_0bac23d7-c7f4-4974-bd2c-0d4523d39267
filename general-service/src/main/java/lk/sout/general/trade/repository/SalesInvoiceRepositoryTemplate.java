package lk.sout.general.trade.repository;

import lk.sout.general.trade.entity.SalesInvoice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Custom repository for SalesInvoice filtering using MongoTemplate
 * This provides efficient database-level filtering instead of Java-level filtering
 */
@Repository
public class SalesInvoiceRepositoryTemplate {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesInvoiceRepositoryTemplate.class);

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * Find sales invoices with multiple filters using MongoDB queries
     * @param startDate Start date (optional, format: yyyy-MM-dd)
     * @param endDate End date (optional, format: yyyy-MM-dd)
     * @param customerNo Customer number (optional)
     * @param invoiceNo Invoice number (optional, partial match)
     * @param drawerNo Cash drawer number (optional)
     * @param cashierUserName Cashier username (optional)
     * @param routeNo Route number (optional)
     * @param pageable Pagination information
     * @return Page of filtered sales invoices
     */
    public Page<SalesInvoice> findWithFilters(String startDate, String endDate, String customerNo,
                                             String invoiceNo, String drawerNo, String cashierUserName,
                                             String routeNo, Pageable pageable) {
        try {
            LOGGER.info("Finding sales invoices with MongoDB filters - startDate: {}, endDate: {}, customerNo: {}, " +
                       "invoiceNo: {}, drawerNo: {}, cashierUserName: {}, routeNo: {}",
                       startDate, endDate, customerNo, invoiceNo, drawerNo, cashierUserName, routeNo);

            // Build the criteria
            Criteria criteria = new Criteria();
            List<Criteria> criteriaList = new ArrayList<>();

            // Date range filter
            if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
                try {
                    LocalDate start = LocalDate.parse(startDate);
                    LocalDate end = LocalDate.parse(endDate);
                    LocalDateTime startDateTime = start.atStartOfDay();
                    LocalDateTime endDateTime = end.atTime(LocalTime.MAX);

                    criteriaList.add(Criteria.where("date").gte(startDateTime).lte(endDateTime));
                    LOGGER.debug("Added date range filter: {} to {}", startDateTime, endDateTime);
                } catch (Exception e) {
                    LOGGER.warn("Error parsing date filters: startDate={}, endDate={}, error={}", startDate, endDate, e.getMessage());
                }
            }

            // Customer filter
            if (customerNo != null && !customerNo.isEmpty()) {
                criteriaList.add(Criteria.where("customerNo").is(customerNo));
                LOGGER.debug("Added customer filter: {}", customerNo);
            }

            // Invoice number filter (partial match, case insensitive)
            if (invoiceNo != null && !invoiceNo.isEmpty()) {
                criteriaList.add(Criteria.where("invoiceNo").regex(invoiceNo, "i"));
                LOGGER.debug("Added invoice number filter: {}", invoiceNo);
            }

            // Cash drawer filter
            if (drawerNo != null && !drawerNo.isEmpty()) {
                criteriaList.add(Criteria.where("drawerNo").is(drawerNo));
                LOGGER.debug("Added cash drawer filter: {}", drawerNo);
            }

            // Cashier user filter
            if (cashierUserName != null && !cashierUserName.isEmpty()) {
                criteriaList.add(Criteria.where("cashierUserName").is(cashierUserName));
                LOGGER.debug("Added cashier user filter: {}", cashierUserName);
            }

            // Route filter
            if (routeNo != null && !routeNo.isEmpty()) {
                criteriaList.add(Criteria.where("routeNo").is(routeNo));
                LOGGER.debug("Added route filter: {}", routeNo);
            }

            // Combine all criteria with AND
            if (!criteriaList.isEmpty()) {
                criteria = new Criteria().andOperator(criteriaList.toArray(new Criteria[0]));
            }

            // Create the query
            Query query = new Query(criteria);

            // Add sorting (descending by creation date)
            query.with(pageable.getSort());

            // Get total count for pagination
            long totalCount = mongoTemplate.count(query, SalesInvoice.class);
            LOGGER.info("Total matching invoices: {}", totalCount);

            // Apply pagination
            query.with(pageable);

            // Execute the query
            List<SalesInvoice> invoices = mongoTemplate.find(query, SalesInvoice.class);
            LOGGER.info("Retrieved {} invoices for page {} (size: {})", invoices.size(), pageable.getPageNumber(), pageable.getPageSize());

            return new PageImpl<>(invoices, pageable, totalCount);

        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices with filters: {}", e.getMessage(), e);
            // Return empty page on error
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    /**
     * Find sales invoices with filters for cashier users (additional filtering by cashier username)
     * @param startDate Start date (optional, format: yyyy-MM-dd)
     * @param endDate End date (optional, format: yyyy-MM-dd)
     * @param customerNo Customer number (optional)
     * @param invoiceNo Invoice number (optional, partial match)
     * @param drawerNo Cash drawer number (optional)
     * @param cashierUserName Cashier username (optional)
     * @param routeNo Route number (optional)
     * @param currentUserName Current user's username (for cashier filtering)
     * @param pageable Pagination information
     * @return Page of filtered sales invoices
     */
    public Page<SalesInvoice> findWithFiltersForCashier(String startDate, String endDate, String customerNo,
                                                       String invoiceNo, String drawerNo, String cashierUserName,
                                                       String routeNo, String currentUserName, Pageable pageable) {
        try {
            LOGGER.info("Finding sales invoices with MongoDB filters for cashier user: {}", currentUserName);

            // Build the criteria
            Criteria criteria = new Criteria();
            List<Criteria> criteriaList = new ArrayList<>();

            // Always filter by current user for cashier role
            criteriaList.add(Criteria.where("cashierUserName").is(currentUserName));

            // Date range filter
            if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
                try {
                    LocalDate start = LocalDate.parse(startDate);
                    LocalDate end = LocalDate.parse(endDate);
                    LocalDateTime startDateTime = start.atStartOfDay();
                    LocalDateTime endDateTime = end.atTime(LocalTime.MAX);

                    criteriaList.add(Criteria.where("date").gte(startDateTime).lte(endDateTime));
                } catch (Exception e) {
                    LOGGER.warn("Error parsing date filters: {}", e.getMessage());
                }
            }

            // Customer filter
            if (customerNo != null && !customerNo.isEmpty()) {
                criteriaList.add(Criteria.where("customerNo").is(customerNo));
            }

            // Invoice number filter (partial match, case insensitive)
            if (invoiceNo != null && !invoiceNo.isEmpty()) {
                criteriaList.add(Criteria.where("invoiceNo").regex(invoiceNo, "i"));
            }

            // Cash drawer filter
            if (drawerNo != null && !drawerNo.isEmpty()) {
                criteriaList.add(Criteria.where("drawerNo").is(drawerNo));
            }

            // Additional cashier user filter (if different from current user)
            if (cashierUserName != null && !cashierUserName.isEmpty() && !cashierUserName.equals(currentUserName)) {
                // This would result in no results since we already filter by currentUserName
                LOGGER.warn("Cashier user filter {} differs from current user {}, this will return no results",
                           cashierUserName, currentUserName);
                return new PageImpl<>(new ArrayList<>(), pageable, 0);
            }

            // Route filter
            if (routeNo != null && !routeNo.isEmpty()) {
                criteriaList.add(Criteria.where("routeNo").is(routeNo));
            }

            // Combine all criteria with AND
            criteria = new Criteria().andOperator(criteriaList.toArray(new Criteria[0]));

            // Create the query
            Query query = new Query(criteria);

            // Add sorting
            query.with(pageable.getSort());

            // Get total count for pagination
            long totalCount = mongoTemplate.count(query, SalesInvoice.class);

            // Apply pagination
            query.with(pageable);

            // Execute the query
            List<SalesInvoice> invoices = mongoTemplate.find(query, SalesInvoice.class);

            return new PageImpl<>(invoices, pageable, totalCount);

        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices with filters for cashier: {}", e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }
}
