package lk.sout.general.trade.entity;

import lk.sout.core.entity.MetaData;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Document
@Component
public class CashRecord {

    @Id
    private String id;

    private String counter;

    private Double amount;

    @DBRef
    private MetaData purpose;

    @DBRef
    private MetaData type;

    private LocalDate date;

    @CreatedBy
    private String createdBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCounter() {
        return counter;
    }

    public void setCounter(String counter) {
        this.counter = counter;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public MetaData getPurpose() {
        return purpose;
    }

    public void setPurpose(MetaData purpose) {
        this.purpose = purpose;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public MetaData getType() {
        return type;
    }

    public void setType(MetaData type) {
        this.type = type;
    }

    public String getTypeName() {
        return (null != type ? type.getValue() : "N/A");
    }

    public String getPurposeName() {
        return (null != purpose ? purpose.getValue() : "N/A");
    }

}
