// Script to drop the problematic index on route.routeNo in the customer collection
// Run this script with: mongo generalWebAjStore drop_route_index.js

// Connect to the database
db = db.getSiblingDB("generalWebAjStore");

// Print existing indexes for reference
print("Existing indexes on customer collection:");
db.customer.getIndexes().forEach(function(index) {
  printjson(index);
});

// Drop the problematic index
print("\nDropping index on route.routeNo...");
try {
  db.customer.dropIndex("route.routeNo_1");
  print("Index dropped successfully.");
} catch (e) {
  print("Error dropping index: " + e);
}

// Print indexes after dropping
print("\nIndexes after dropping:");
db.customer.getIndexes().forEach(function(index) {
  printjson(index);
});
