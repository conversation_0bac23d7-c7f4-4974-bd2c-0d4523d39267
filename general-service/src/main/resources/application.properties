spring.application.name=General

# MongoDB - Default database (used by DbConfig for system/shared data)
spring.data.mongodb.host=localhost
#spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=generalWeb
spring.data.mongodb.authDatabase=admin
spring.data.mongodb.username=generalWeb
spring.data.mongodb.password=awer@#$cdfDDF!@S_+(

# Multi-tenancy base configuration
multitenancy.enabled=true

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,env,beans,configprops,httptrace,loggers,threaddump,heapdump
management.endpoint.health.show-details=when-authorized
management.endpoint.health.show-components=always
management.endpoints.web.base-path=/actuator
management.endpoint.health.enabled=true
management.endpoint.info.enabled=true
management.endpoint.metrics.enabled=true

# Health Check Configuration
management.health.mongo.enabled=true
management.health.diskspace.enabled=true
management.health.ping.enabled=true

# Info Endpoint Configuration
info.app.name=General Service
info.app.description=ERP General Service
info.app.version=1.0
info.app.encoding=@project.build.sourceEncoding@
info.app.java.version=@java.version@

# MULTIPART (MultipartProperties)
# Enable multipart uploads
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=215MB

spring.main.allow-circular-references=true

sout.mainWhCode=0

# Backup Configuration
backup.enabled=false
backup.retention.days=7
backup.temp.directory=/opt/tomcat11/backup/temp-backups
# backup.databases - Will auto-discover all databases starting with "generalWeb"
backup.database.pattern=generalWeb*

# Google Drive Configuration (you need to set these)
google.drive.credentials.path=/opt/tomcat11/backup/sout-main-439195d6196b.json
google.drive.folder.id=1QX-0qNbc9USEMc6zs-fppG5OIR31AjGo

#File logging
logging.level.root=info
#logging.pattern.console=%d{dd-MM-yyyy HH:mm:ss.SSS} %magenta([%thread]) %highlight(%-5level) %logger.%M - %msg%n
#logging.file.path=/var/log/tomcat11/sout.${sout.customer}.log
#logging.pattern.file=%d{dd-MM-yyyy HH:mm:ss.SSS} :${sout.customer} [%thread] %-5level %logger{36}.%M - %msg%n
#logging.logback.rollingpolicy.max-file-size=10MB

dateFormat=MM/dd/yyyy
dateTimeFormat=MM/dd/yyyy HH:mm:ss
