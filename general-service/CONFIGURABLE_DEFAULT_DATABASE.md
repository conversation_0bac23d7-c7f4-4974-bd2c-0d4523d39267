# 🔧 Configurable Default Database

## 🎯 **Overview**

The multi-tenant system now supports configurable default database through the `application.properties` file. This allows you to customize both the fallback database and the base name for tenant-specific databases.

## ⚙️ **Configuration**

### **Basic Configuration**
```properties
# Set the default/fallback database name
spring.data.mongodb.database=generalWeb
spring.data.mongodb.authDatabase=generalWeb
```

### **Custom Configuration Examples**

#### **Example 1: Company-specific naming**
```properties
spring.data.mongodb.database=mycompany
spring.data.mongodb.authDatabase=mycompany
```
**Result:**
- Default: `mycompany`
- Demo tenant: `mycompanyDemo`
- Wanigarathna tenant: `mycompanyWanigarathna`

#### **Example 2: Environment-specific naming**
```properties
spring.data.mongodb.database=production
spring.data.mongodb.authDatabase=production
```
**Result:**
- Default: `production`
- Demo tenant: `productionDemo`
- Wanigarathna tenant: `productionWanigarathna`

#### **Example 3: Application-specific naming**
```properties
spring.data.mongodb.database=erp
spring.data.mongodb.authDatabase=erp
```
**Result:**
- Default: `erp`
- Demo tenant: `erpDemo`
- Wanigarathna tenant: `erpWanigarathna`

## 🔄 **How It Works**

### **Database Name Resolution Logic:**

1. **Default Tenant** (`localhost` or invalid subdomain):
   - Uses the exact value from `spring.data.mongodb.database`
   - Example: `generalWeb`, `mycompany`, `production`

2. **Named Tenants** (valid subdomains):
   - Base name + Capitalized tenant name
   - Example: `generalWeb` + `Demo` = `generalWebDemo`

3. **Base Name Extraction:**
   - If the configured database has a suffix (like `generalWebAjstore`), it extracts the base (`generalWeb`)
   - Otherwise, uses the full configured name as the base

### **Code Implementation:**
```java
@Component
public class TenantContext {
    private static String defaultDatabase;
    
    public TenantContext(@Value("${spring.data.mongodb.database}") String defaultDatabase) {
        TenantContext.defaultDatabase = defaultDatabase;
    }
    
    public static String getCurrentDatabase() {
        String tenant = getCurrentTenant();
        
        if ("default".equals(tenant)) {
            return defaultDatabase != null ? defaultDatabase : "generalWeb";
        }
        
        String baseDatabaseName = getBaseDatabaseName();
        String capitalizedTenant = tenant.substring(0, 1).toUpperCase() + tenant.substring(1).toLowerCase();
        return baseDatabaseName + capitalizedTenant;
    }
}
```

## 🧪 **Testing**

### **Test Endpoint Response:**
```json
{
  "databaseConnected": true,
  "database": "generalWeb",
  "actualDatabase": "generalWeb",
  "host": "localhost",
  "configuredDefaultDatabase": "generalWeb",
  "tenant": "default",
  "status": "success"
}
```

### **Test Different Configurations:**
```bash
# Test with localhost (default tenant)
curl -H "Host: localhost" http://localhost:8080/api/tenant/info

# Test with demo tenant
curl -H "Host: demo.viganana.com" http://localhost:8080/api/tenant/info

# Test with custom tenant
curl -H "Host: wanigarathna.viganana.com" http://localhost:8080/api/tenant/info
```

## 📋 **Configuration Examples**

### **Development Environment:**
```properties
spring.data.mongodb.database=dev
spring.data.mongodb.authDatabase=dev
```

### **Staging Environment:**
```properties
spring.data.mongodb.database=staging
spring.data.mongodb.authDatabase=staging
```

### **Production Environment:**
```properties
spring.data.mongodb.database=prod
spring.data.mongodb.authDatabase=prod
```

### **Multi-Company Setup:**
```properties
# For Company A
spring.data.mongodb.database=companyA
spring.data.mongodb.authDatabase=companyA

# For Company B
spring.data.mongodb.database=companyB
spring.data.mongodb.authDatabase=companyB
```

## 🔍 **Verification**

### **Startup Logs:**
Look for this log message during application startup:
```
INFO [restartedMain] lk.sout.tenant.TenantContext - 🔧 TenantContext initialized with default database: generalWeb
```

### **API Response:**
The `/api/tenant/info` endpoint now includes the `configuredDefaultDatabase` field showing the configured value.

## ✅ **Benefits**

1. **🔧 Flexible Configuration** - Easy to change database naming scheme
2. **🌍 Environment Support** - Different databases for dev/staging/prod
3. **🏢 Multi-Company** - Support multiple companies with separate databases
4. **🔄 Backward Compatible** - Existing configurations continue to work
5. **📊 Transparent** - Clear visibility of configured vs actual database names

## 🚀 **Migration Guide**

### **From Fixed to Configurable:**
1. **Current**: Hard-coded `generalWeb` as default
2. **New**: Configurable via `spring.data.mongodb.database`
3. **Migration**: No changes needed - existing configurations work as before

### **Changing Default Database:**
1. Update `spring.data.mongodb.database` in `application.properties`
2. Update `spring.data.mongodb.authDatabase` to match
3. Ensure the new database exists in MongoDB
4. Restart the application
5. Verify using `/api/tenant/info` endpoint
