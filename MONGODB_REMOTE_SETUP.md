# 🌐 MongoDB Remote Server Setup

## 🎯 Current Situation
- Remote MongoDB server: `*************:27017`
- Root user exists, but `generalWeb` user doesn't exist
- Need to create `generalWeb` user with proper permissions

## 🚀 Step-by-Step Setup

### **Step 1: Connect as Root User**

```bash
mongosh --host *************:27017 -u root -p --authenticationDatabase admin
```

### **Step 2: Create generalWeb User**

```javascript
use admin

// Create generalWeb user with comprehensive admin privileges
db.createUser({
  user: "generalWeb",
  pwd: "awer@#$cdfDDF!@S_+(", 
  roles: [
    // Core admin roles
    { role: "readWriteAnyDatabase", db: "admin" },
    { role: "listDatabases", db: "admin" },
    { role: "dbAdminAnyDatabase", db: "admin" },
    { role: "userAdminAnyDatabase", db: "admin" },
    
    // Specific database access
    { role: "readWrite", db: "generalWeb" },
    { role: "readWrite", db: "generalWebDemo" },
    { role: "readWrite", db: "generalWebNewcitymobile" },
    { role: "readWrite", db: "generalWebWanigarathna" }
  ]
})

print("✅ generalWeb user created successfully");
```

### **Step 3: Verify User Creation**

```javascript
// Check if user was created
var user = db.getUser("generalWeb");
if (user) {
  print("✅ User exists");
  print("Roles assigned:");
  user.roles.forEach(function(role) {
    print("  - " + role.role + " on " + role.db);
  });
} else {
  print("❌ User creation failed");
}
```

### **Step 4: Test generalWeb User**

```bash
# Test connection as generalWeb user
mongosh --host *************:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase admin
```

```javascript
// Test listDatabases permission
use admin
var dbs = db.runCommand({listDatabases: 1});
print("✅ Found " + dbs.databases.length + " databases:");
dbs.databases.forEach(db => print("  - " + db.name));

// Filter for tenant databases
var tenantDbs = dbs.databases.filter(db => db.name.startsWith('generalWeb'));
print("\n🎯 Tenant databases: " + tenantDbs.length);
tenantDbs.forEach(db => print("  ✅ " + db.name));
```

### **Step 5: Test Tenant Database Access**

```javascript
// Test access to tenant databases
["generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"].forEach(function(dbName) {
  try {
    var result = db.getSiblingDB(dbName).runCommand({ping: 1});
    print(dbName + ": " + (result.ok ? "✅ ACCESSIBLE" : "❌ FAILED"));
  } catch(e) {
    print(dbName + ": ❌ ERROR - " + e.message);
  }
});
```

## ⚙️ Application Configuration Updated

Your `application.properties` has been updated to:

```properties
# MongoDB - Remote server configuration
spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.authDatabase=admin
spring.data.mongodb.username=generalWeb
spring.data.mongodb.password=awer@#$cdfDDF!@S_+(

# MongoDB configuration for tenant-specific databases
mongodb.host=*************
mongodb.port=27017
mongodb.username=generalWeb
mongodb.password=awer@#$cdfDDF!@S_+(
```

## 🧪 Quick Test Commands

### **One-line database listing test:**
```bash
mongosh --host *************:27017 -u generalWeb -p "awer@#$cdfDDF!@S_+(" --authenticationDatabase admin --quiet --eval "use admin; db.runCommand({listDatabases: 1}).databases.forEach(db => print(db.name))"
```

### **Test backup functionality:**
```bash
mongodump --host *************:27017 --username generalWeb --password "awer@#$cdfDDF!@S_+(" --authenticationDatabase admin --db generalWeb --out ./test-backup
```

## 🔧 If You Get Errors

### **Error: "Authentication failed"**
- Make sure you're using `--authenticationDatabase admin`
- Verify the password is correct

### **Error: "not authorized"**
- The user might not have been created properly
- Re-run the user creation commands as root

### **Error: "listDatabases may only be run against the admin database"**
- Always use `use admin` before running `listDatabases`
- Or use: `db.getSiblingDB('admin').runCommand({listDatabases: 1})`

## ✅ Success Indicators

After setup, you should be able to:
1. ✅ Connect as generalWeb user
2. ✅ List all databases
3. ✅ Access tenant databases
4. ✅ Run backup operations
5. ✅ Your Spring Boot app connects successfully

## 🎯 Next Steps

1. Run the user creation commands above
2. Test the generalWeb user connection
3. Restart your Spring Boot application
4. Test the backup functionality

The backup service should now work without any code changes!
