#!/bin/bash

# Script to fix Google Drive credentials file formatting

echo "🔧 Fixing Google Drive credentials file..."

# Backup the original file
cp /opt/tomcat11/backup/sout-main-439195d6196b.json /opt/tomcat11/backup/sout-main-439195d6196b.json.backup

# Create properly formatted JSON file
cat > /opt/tomcat11/backup/sout-main-439195d6196b.json << 'EOF'
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
EOF

# Set proper permissions
chmod 600 /opt/tomcat11/backup/sout-main-439195d6196b.json
chown root:root /opt/tomcat11/backup/sout-main-439195d6196b.json

echo "✅ Google Drive credentials file fixed!"
echo "📁 File location: /opt/tomcat11/backup/sout-main-439195d6196b.json"
echo "🔒 Permissions set to 600 (read/write for owner only)"

# Validate JSON format
echo "🧪 Validating JSON format..."
if python3 -m json.tool /opt/tomcat11/backup/sout-main-439195d6196b.json > /dev/null 2>&1; then
    echo "✅ JSON format is valid"
else
    echo "❌ JSON format is invalid"
fi
